<?php

namespace App\Listeners;

use App\Events\PolicyUploaded;
use App\Mail\PolicyUploadedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendPolicyUploadNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\PolicyUploaded  $event
     * @return void
     */
    public function handle(PolicyUploaded $event)
    {
        // Send notification to user
        $issuance = $event->issuance;

        // send mail
        Mail::to($issuance->task->user)->send(new PolicyUploadedMail($issuance));
    }
}
