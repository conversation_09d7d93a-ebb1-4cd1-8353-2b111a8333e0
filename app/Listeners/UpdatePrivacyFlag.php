<?php

namespace App\Listeners;

use App\Events\PrivacySigned;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdatePrivacyFlag
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\PrivacySigned  $event
     * @return void
     */
    public function handle(PrivacySigned $event)
    {
        foreach ($event->signers as $signer) {
            Log::info("Updating privacy flag for signer: {$signer->id}");
            $signer->privacy = true;
            $signer->privacy_accepted_at = now();
            $signer->save();
        }
    }
}
