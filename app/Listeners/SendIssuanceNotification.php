<?php

namespace App\Listeners;

use App\Events\IssuanceProcessed;
use App\Events\IssueProcessed;
use App\Mail\IssuanceProcessedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendIssuanceNotification
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\IssueProcessed  $event
     * @return void
     */
    public function handle(IssuanceProcessed $event)
    {
        // Send notification to user
        $issuance = $event->issuance;

        // send mail
        Mail::to($issuance->task->user)->send(new IssuanceProcessedMail($issuance));

    }
}
