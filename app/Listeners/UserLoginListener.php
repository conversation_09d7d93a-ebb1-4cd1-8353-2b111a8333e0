<?php namespace App\Listeners;

use Carbon\Carbon;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Log;

class UserLoginListener
{
    public function handle(Login $event)
    {
        if (! $event->user) {
            Log::error("Empty login user");

            return;
        }

        if (! $event->user->id) {
            Log::error("Empty login user ID");

            return;
        }

        Log::debug("User {$event->user->id} login");

        $event->user->last_login_at = Carbon::now();

        $event->user->save();
    }
}