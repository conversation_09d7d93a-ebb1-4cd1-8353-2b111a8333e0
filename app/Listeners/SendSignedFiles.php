<?php

namespace App\Listeners;

use App\Events\SignatureComplete;
use App\Events\PolicyUploaded;
use App\Mail\FilesSignedMail;
use App\Models\Role;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendSignedFiles
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\PolicyUploaded  $event
     * @return void
     */
    public function handle(SignatureComplete $event)
    {
        $recipients = collect($event->task->pipeline->getPersonSubjects('contractor'));

        $cc = Role::whereName('manager')->first()->users->pluck('email');

        Mail
            ::to($recipients->pluck('email'))
            ->cc($cc)
            ->send(new FilesSignedMail($event->file));
    }
}
