<?php

namespace App\Listeners;

use App\Events\SignatureComplete;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\PipelineManager;

class PipelineNext
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\SignatureComplete  $event
     * @return void
     */
    public function handle(SignatureComplete $event)
    {
        $manager = app()->make(PipelineManager::class);

        if (! $event->task) {
            Log::warning("PipelineNext: No task found in SignatureComplete event.");

            return;
        }

        if ($manager->next($event->task->pipeline)) {
            Log::debug("PipelineNext: Successfully moved to the next task in pipeline {$event->task->pipeline->id}.");

            return;
        }

        Log::debug("PipelineNext: No next task found for pipeline (state: {$event->task->pipeline->state}).");
    }
}
