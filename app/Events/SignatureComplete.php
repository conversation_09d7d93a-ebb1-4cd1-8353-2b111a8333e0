<?php

namespace App\Events;

use App\Models\File;
use App\Models\Task;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SignatureComplete
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public File $file;
    
    public Task $task;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(File $file, Task $task)
    {
        $this->file = $file;
        $this->task = $task;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
