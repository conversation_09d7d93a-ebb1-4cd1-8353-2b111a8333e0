<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Log;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        try {
            $tenant = app('currentTenant');
        } catch (BindingResolutionException $ex) {
            Log::error("Tenant not found: " . $request->httpHost());
            
            abort(500);
        }
        

        if ($tenant && $tenant->name == 'dorotea' && ! $request->expectsJson()) {
            abort(401);
        }

        if (! $request->expectsJson()) {
            return route('login');
        }
    }
}
