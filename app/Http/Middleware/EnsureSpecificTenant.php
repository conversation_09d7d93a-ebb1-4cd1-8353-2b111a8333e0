<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class EnsureSpecificTenant
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, ...$tenants)
    {
        if (! in_array(app('currentTenant')->name, $tenants)) {
            Log::warning('Unauthorized tenant access attempt', [
                'tenant' => app('currentTenant')->name,
                'allowed_tenants' => $tenants,
                'user_id' => auth()->id(),
            ]);
            
            abort(404);
        }

        return $next($request);
    }
}
