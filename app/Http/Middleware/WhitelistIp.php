<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WhitelistIp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $tenant = app('currentTenant')->name;

        if ($ips = config("easyprofile.tenants.{$tenant}.allowedIP")) {
            if (is_array($ips) && ! empty($ips)) {
                if (! in_array($request->ip(), $ips)) {
                    Log::warning("Access from unknown IP " . $request->ip());

                    if (! in_array(env('APP_ENV'), ['local', 'testing'])) {
                        abort(403, 'Source not allowed.');
                    } 
                }
            }
        }

        return $next($request);
    }
}
