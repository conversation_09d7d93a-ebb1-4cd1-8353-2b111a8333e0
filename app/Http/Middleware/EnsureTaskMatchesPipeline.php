<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\PipelineManager;

class EnsureTaskMatchesPipeline
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Note: if a task is not found to be bound, <PERSON><PERSON> will 
        // raise a 404 exception and this line of code should not
        // be executed, but better safe than sorry.
        if (! $task = $request->task) {
            Log::error("Request has no task.");

            abort(400);
        }

        // Note: if a task is not found to be bound, <PERSON><PERSON> will 
        // raise a 404 exception and this line of code should not
        // be executed, but better safe than sorry.
        if (! $pipeline = $request->pipeline) {
            Log::error("Request has no pipeline.");

            abort(400);
        }
        
        if ($task->pipeline_id != $pipeline->id) {
            Log::error("Task/Pipeline mismatch");

            abort(400);
        }
        
        return $next($request);
    }
}
