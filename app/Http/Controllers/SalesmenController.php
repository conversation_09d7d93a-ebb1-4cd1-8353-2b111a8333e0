<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Rui;
use App\Models\Rules\UniqueInTenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class SalesmenController extends Controller
{
    use CanSwitchTheme;

    public function getIndex()
    {
        $salesmen = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('node')
        ->with('rui')
        ->orderBy('active', 'desc')
        ->orderBy('lastname')
        ->get();

        $path = $this->getView(app('currentTenant')->name, "Salesmen");

        return Inertia::render($path, [
            'salesmen' => $salesmen,
        ]);
    }

    public function postSalesman(Request $request, $userId)
    {
        $path = $this->getView(app('currentTenant')->name, "Salesmen");

        // Toggle stato del collaboratore
        User::find($userId)->update(['active' => ! $request->currentStatus]);

        $salesmen = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('node')
        ->get();

        return Inertia::render($path, [
            'salesmen' => $salesmen,
        ]);
    }

    public function getSalesman($id)
    {
        $salesman = User::whereHas(
            'roles', function($q){
            $q->where('name', 'salesman');
        })
        ->with('pipelines')
        ->with('node')
        ->with('rui')
        ->with('clients')
        ->whereId($id)
        ->first();

        if (! $salesman) {
            abort(404);
        }

        $path = $this->getView(app('currentTenant')->name, "Salesman");

        return Inertia::render($path, [
            'network' => $salesman->node->get()->toTree(),
            'salesman' => $salesman,
        ]);
    }

    public function putSalesman(Request $request, User $user)
    {
        if (! $user || ! $user->hasRole('salesman')) {
            abort(400);
        }

        $this->validate($request, [
            'name' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                with(new UniqueInTenant(new User(), 'email'))->ignore($user->id)
            ],
            'phone' => [
                'required',
                'string',
                'max:20',
                with(new UniqueInTenant(new User(), 'phone'))->ignore($user->id)
            ],
            'rui.section' => 'required|string|size:1',
            'rui.code' => 'required|string|size:9',
            'rui.subscribed_at' => 'required|date',
        ]);

        $rui = $request->only('rui');
        
        DB::connection('tenant')->beginTransaction();

        if (! $rui = $user->rui) {
            $rui = new Rui();
        }

        $rui->fill($request->only('rui'));
        $rui->save();

        $user->rui_id = $rui->id;
        $user->forceFill($request->except('rui'));
        $user->save();

        DB::connection('tenant')->commit();

        return redirect()->back();
    }

}
