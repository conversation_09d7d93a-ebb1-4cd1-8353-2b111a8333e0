<?php

namespace App\Http\Controllers;

use App\Models\Pipeline;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Dorotea\PipelineManager;
use Upnovation\Easyprofile\Dorotea\UserManager;

class DoroteaAuthController extends Controller
{
    /**
     * @var UserManager
     */
    protected $users;

    /**
     * @var PipelineManager
     */
    protected $pipelines;

    public function __construct(UserManager $manager, PipelineManager $pipelines) 
    {
        $this->users = $manager;
        $this->pipelines = $pipelines;
    }

    public function getAuth(Request $request)
    {
        // Log url and query string
        Log::info($request->fullUrl());

        if (! $token = $request->get('session_token')) {
            abort(400);
        }

        if (! $userId = $request->get('user_id')) {
            abort(400);
        }

        if (! $user = $this->users->auth($userId, $token)) {
            abort(401);
        }

        // Redirect ratio:
        //      - praticaID + anagraficaID => new pipeline
        //      - praticaID => attempt to resume
        //      - else => homepage if manager, error if salesman

        if ($user->hasRole('salesman')) {
            return $this->redirectSalesman($request);
        }

        return redirect('/');
    }

    protected function redirectSalesman(Request $request)
    {
        $opportunityId = $request->get('praticaID');

        if ($pipeline = Pipeline::where('egg_opportunity_id', $opportunityId)->first()) {
            return redirect()->route('pipeline.resume', ['pipeline' => $pipeline]);
        }
        
        if ($opportunityId && $clientId = $request->get('anagraficaID')) {
            $pipeline = $this->pipelines->start(
                auth()->user(), 
                $opportunityId, 
                $clientId
            );
    
            return redirect()->route('pipeline.resume', ['pipeline' => $pipeline]);              
        }

        if ($opportunityId) {
            abort(404);
        }

        abort(401);
    }

    public function getDebugPipeline(Request $request)
    {
        $user = User::whereHas('roles', function($q) {
            $q->where('name', 'salesman');
        })->first();

        $pipeline = $this->pipelines->start(
            $user,
            rand(1, 4999),
            1
        );

        auth()->login($user);

        return redirect()->route('pipeline.resume', ['pipeline' => $pipeline]);    
    }
}
