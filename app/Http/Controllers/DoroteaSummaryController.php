<?php

namespace App\Http\Controllers;

use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Dorotea\EggManager;
use Upnovation\Easyprofile\PipelineManager;

class DoroteaSummaryController extends Controller
{
    /**
     * @var EggManager
     */
    protected $manager;

    /**
     * @var PipelineManager
     */
    protected $pipelines;

    public function __construct(EggManager $manager, PipelineManager $pipelines) 
    {
        $this->manager = $manager;
        $this->pipelines = $pipelines;
    }

    public function postIndex(Pipeline $pipeline)
    {
        try {
            $url = $this->manager->getRedirectUrl($pipeline);

            Log::debug("EGG redirect url: $url");

            if ($pipeline->state == 'closed') {
                return Inertia::location($url);
            }

            $error = false;

            if (! $this->manager->updateOpportunity($pipeline)) {
                throw new \Exception("Bad EGG communication");
            }

            $this->pipelines->close($pipeline);

            return Inertia::location($url);

        } catch (\Exception $ex) {
            Log::error($ex->getMessage());

            $error = true;
        }

        return Inertia::render("Tasks/Dorotea/Summary", [
            'error' => $error ? __('easyprofile.tenants.dorotea.error_egg_return') : null,
        ]);
    }
}
