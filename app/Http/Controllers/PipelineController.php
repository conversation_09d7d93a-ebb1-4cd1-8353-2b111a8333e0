<?php

namespace App\Http\Controllers;

use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\PipelineManager;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class PipelineController extends Controller
{
    use \Upnovation\Easyprofile\Traits\CanSwitchTheme;

    /**
     * Undocumented variable
     *
     * @var PipelineManager
     */
    protected $manager;

    public function __construct(PipelineManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Pipeline $pipeline)
    {
        // make dynamic theme for tenant 
        $path = $this->getView(app('currentTenant')->name, "Pipeline");

        $pipeline = $pipeline
            ->load('tasks')
            ->load('user')
            ->load('user.node');

        // Load user.node.rui, user.rui
        if ($pipeline->user) {
            $pipeline->user->load('node.rui');
            $pipeline->user->load('rui');
        }

        $subjects = $pipeline->clients()
            ->with('person')
            ->with('enterprise')
            ->get();

        $mapperResult = null;

        if ($profile = $pipeline->getProfile()) {
            $mapperResult = $profile->mapperResult;
        }

        $issuances = $pipeline->getIssuances()->map(function ($issuance) {
            $issuance->load('task');
            $issuance->load('task.pipeline');
            return $issuance;
        });

        return Inertia::render($path, [
            'pipeline' => $pipeline,
            'subjects' => $subjects,
            'files' => $pipeline->getFiles(),
            'products' => $pipeline->getSelectedProducts(),
            'issuances' => $issuances,
            'mapperResult' => $mapperResult,
        ]);
    }


    /**
     * Starts a new pipeline.
     *
     * @return void
     */
    public function postIndex()
    {
        $pipeline = $this->manager->start(auth()->user());

        return redirect()->route("pipeline.resume", ['pipeline' => $pipeline->id]);
    }

    /**
     * Resume a pipeline.
     *
     * @return void
     */
    public function getResume(Pipeline $pipeline)
    {
        if (! $task = $this->manager->resume($pipeline)) {
            return redirect()->route('dashboard');
        }

        return redirect()->action(
            [$task->controller, 'getIndex'],
            [$pipeline, $task]
        );
    }

    /**
     * Transition to next task.
     *
     * @return void
     */
    public function getNext(Pipeline $pipeline)
    {
        if (! $this->manager->next($pipeline)) {
            return redirect()->route('dashboard');
        }

        return redirect()->route("pipeline.resume", ['pipeline' => $pipeline->id]);
    }

    public function getPrevious(Pipeline $pipeline)
    {
        return $this->getGoto(
            $pipeline,
            $this->manager->previous($pipeline)
        );
    }

    /**
     * Go to specific task.
     *
     * @param Pipeline $pipeline
     * @param Task $task
     * @return void
     */
    public function getGoto(Pipeline $pipeline, Task $task)
    {
        if (! $task = $this->manager->goto($pipeline, $task)) {
            Log::debug("Task is not accessible, resuming pipeline.");

            return $this->getResume($pipeline);
        }

        return redirect()->action(
            [$task->controller, 'getIndex'],
            [$pipeline, $task]
        );
    }

    public function postSkip(Request $request, Pipeline $pipeline, string $targetTaskName)
    {
        $targetTask = Task::where('type', $targetTaskName)
            ->where('pipeline_id', $pipeline->id)
            ->first();

        if (! $targetTask) {
            throw new Exception("Target task not found: {$targetTaskName} for pipeline ID: {$pipeline->id}");
        }

        if (! $task = $this->manager->skip($pipeline, $targetTask, $request->all())) {
            throw new Exception("Cannot skip to task {$targetTask->type}.");
        }

        return redirect()->action(
            [$task->controller, 'getIndex'],
            [$pipeline, $task]
        );
    }

    /**
     * Delete pipeline.
     *
     * @param Pipeline $pipeline
     * @return void
     */
    public function deleteIndex(Pipeline $pipeline)
    {
        $this->manager->delete($pipeline);

        return redirect()->route("dashboard", ['pipeline' => $pipeline->id]);
    }
}
