<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Document;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;
use Tests\Unit\Pdf\TestDocumentSeeder;

class PdfToolsController extends Controller
{
    protected DocumentsInstaller $installer;

    protected PdfProcessor $pdfProcessor;

    public function __construct(DocumentsInstaller $installer, PdfProcessor $pdfProcessor)
    {
        $this->installer = $installer;

        $this->pdfProcessor = $pdfProcessor;
    }

    public function getDocument(Document $document, ?Pipeline $pipeline = null)
    {
        if (! $config = $document->loadConfiguration()) {
            dd("Document configuration for {$document->title} not found.");
        }

        if (! file_exists($config['file']->getFullPath())) {
            dd("Document file for {$document->title} not found.");
        }

        // invoke seeder
        if (! $pipeline) {
            $pipeline = (new TestDocumentSeeder())->makePipeline();
        }

        // Disabled: there's a fix in Document model that forces overlays
        // to be read from configuration.
        //$document->overlayArray = $config['document']['overlayArray'];

        $outfile = $this->pdfProcessor->compile(
            $document,
            $pipeline,
            new File([
                'disk' => 'documents',
                'path' => 'compiled',
                'filename' => 'test-' . $config['file']->filename,
            ]),
            [
                // Dati generali
                'combinazione' => '5',
                'intermediazione' => '0',
                'importoIntermediazione' => 1200.50,
                'emissione' => 300.00,

                // Dati mutuo
                'importoFinanziato' => 150000,
                'banca' => 'Banca Popolare di Milano',
                'durata' => 20,
                'dataErogazione' => '2024-06-15',
                'notaio' => 'Mario Rossi',

                // Dati immobile
                'tipoAbitazione' => 'appartamento',
                'indirizzoImmobile' => [
                    'type' => 'Residenziale',
                    'street' => 'Via Garibaldi',
                    'number' => '12B',
                    'zip' => '20121',
                    'city' => 'Milano',
                    'province' => 'MI',
                    'region' => 'Lombardia',
                    'country' => 'Italia',
                ],
                'piano' => 3,
                'interno' => '7',
                'importoDaAssicurare' => 100000,

                // Dati catastali
                'datiCatastaliImmobile' => [
                    'foglio' => '123',
                    'part' => '456',
                    'sub' => '7',
                    'cat' => 'A/2',
                    'classe' => '3',
                    'consist' => '5',
                    'rendita' => 25000,
                ],

                'datiCatastaliPertinenza' => [
                    'foglio' => '1',
                    'part' => '5',
                    'sub' => '7',
                    'cat' => '3/2',
                    'classe' => '5',
                    'consist' => '1',
                    'rendita' => 100,
                ],

                // Dati assicurato
                'datiAssicurato' => [
                    'nome' => 'Giovanni',
                    'cognome' => 'Bianchi',
                    'via' => 'Via Verdi',
                    'numVia' => '8',
                    'cap' => '20122',
                    'localita' => 'Milano',
                    'provincia' => 'MI',
                    'telefono' => '3291234567',
                    'email' => '<EMAIL>',
                ],

                // Dichiara inoltre
                'dichiarazione1' => '0',
                'dichiarazione2' => '0',
                'dichiarazione3' => '0',
            ]

        );

        return response()->file($outfile->getFullPath());
    }
}

