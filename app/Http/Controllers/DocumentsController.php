<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\File;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class DocumentsController extends Controller
{
    use CanSwitchTheme;

    /**
     * @var DocumentsManager
     */
    protected $manager;

    public function __construct(DocumentsManager $manager)
    {
        $this->manager = $manager;

        //dump(Storage::disk('public')->get('test.file'));
    }

    public function getIndex()
    {
        $path = $this->getView(app('currentTenant')->name, "Documents");

        return Inertia::render($path, [
            'docs' => $this->manager->getDocuments()
        ]);
    }

    public function getTemplate(Request $request, $id)
    {
        $document = Document::find($id);

        $path = $document->template->getFullPath();

        if ($request->has('download')) {
            return response()->download($path);
        }

        return response()->file($path);
    }

    public function getFile(Request $request, $uuid)
    {
        // @todo ACL
        
        $file = File::where('uuid', $uuid)->firstOrFail();

        $path = $file->getFullPath();

        if ($request->has('download')) {
            return response()->download($path);
        }

        return response()->file($path);
    }
}
