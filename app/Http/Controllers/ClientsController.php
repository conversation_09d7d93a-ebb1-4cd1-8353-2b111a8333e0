<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Person;
use App\Models\Pipeline;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class ClientsController extends Controller
{
    use CanSwitchTheme;

    public function getIndex()
    {
        $path = $this->getView(app('currentTenant')->name, "Clients");

        $clients = Client::with('person')->with('enterprise')->with('pipeline')->get();

        $clients = $clients->map(function ($client) {
            if ($client->enterprise_id && $client->enterprise) {
                $client->enterprise->rep = $client->enterprise->rep ?? new Person();
            }

            // For frontend datatable
            $client->code = $client->person ? $client->person->taxCode : ($client->enterprise ? $client->enterprise->vat : '-');
            $client->type = $client->pipeline && $client->pipeline->type == 'legal' ? 'Azienda' : 'Persona';
            return $client;
        });

        $clients = $clients->unique(function ($client) {
            return $client->person_id . '-' . $client->enterprise_id;
        })->values();

        return Inertia::render($path, [
            'clients' => $clients,

            'persons' => $clients->map(function ($client) {
                return $client->person_id ? $client->person : null;
            })->filter()->unique('id')->values(),

            'enterprises' => $clients->map(function ($client) {
                return $client->enterprise_id ? $client->enterprise : null;
            })->filter()->unique('id')->values(),

        ]);    
    }

    public function getClient(Request $request, Client $client)
    {
        if ($client->person_id) {
            return $this->getClientPerson($request, $client);
        }

        throw new \Exception("Not implemented yet");
    }

    public function getClientPerson(Request $request, Client $client)
    {
        // If the client has no person, we cannot proceed.
        if (! $client->person) {
            abort(404, "Person not found for this client.");
        }

        /** @var SurveyManager $surveyManager */
        $surveyManager = app(SurveyManager::class);

        /** @var IssuanceManager $issuanceManager */
        $issuanceManager = app(IssuanceManager::class);

        // Load the client with its person and enterprise.
        $client->load(['person', 'enterprise', 'person.addresses']);

        $profile = $surveyManager->getProfileByClient($client->person);

        $issuances = $issuanceManager->getIssuancesByClient($client);

        // Load the person's pipelines with tasks and user.
        $pipelines = $client->person->pipelines()->with('tasks')->with('user')->orderBy('created_at', 'desc')->get();

        $files = app(FileManager::class)->loadPersonFiles($client->person);



        // @FIXME controllare acl/gate e che un collab non possa vedere i dati di un altro collab

        $path = $this->getView(app('currentTenant')->name, "Client");

        // Return the Inertia view with the necessary data.
        return Inertia::render($path, [
            'client' => $client,
            'referral' => $client->person->getReferral(),
            'pipelines' => $pipelines,
            'profile' => $profile,
            'issuances' => $issuances,
            'files' => $files,
        ]);
    }

    public function getDownloadDocument(Request $request, Client $client, string $type, string $index)
    {
        if (! $person = $client->person) {
            abort(404, "Person not found for this client.");
        }

        if (! $document = $person->getDocument($type)) {
            abort(404, "Document not found.");
        }

        // Assuming the document has a 'files' property with the file path.
        if (isset($document->files[$index])) {
            $file = new File($document->files[$index]);

            return response()->download($file->getFullPath());
        }

        abort(404, "File not found.");
    }

    public function getClientEnterprise(Request $request, Client $client)
    {
        throw new \Exception("Not implemented yet");
    }
}

