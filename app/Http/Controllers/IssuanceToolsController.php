<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Document;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;
use Tests\Unit\Pdf\TestDocumentSeeder;
use Upnovation\Easyprofile\PipelineManager;

class IssuanceToolsController extends Controller
{

    use CanSwitchTheme;

    public function __construct()
    {
       
    }

    public function getForm(Product $product)
    {
        $manager = app(PipelineManager::class);
        $pipeline = $manager->start(User::find(1));

        $person = Person::first();

        $client = new Client();
        $client->forceFill([
            'pipeline_id' => $pipeline->id,
            'person_id' => $person->id,
            'role' => 'contractor',
            'name' => $person->name
        ]);
        $client->save();

        $task = Task
        ::where('pipeline_id', $pipeline->id)
        ->where('type', 'issuance')
        ->first();

        $issuance = new Issuance();
        $issuance->forceFill([
            'task_id' => $task->id,
            'product_id' => $product->id,
            'status' => 'pending'
        ]);
        $issuance->save();

        $path = $this->getView(app('currentTenant')->name, $product->issuanceProcessor['template']);

        return Inertia::render($path, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'issuance' => $issuance->load('product.company'),
        ]);
    }
}

