<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON><PERSON>\DbDumper\Compressors\GzipCompressor;
use Spatie\Multitenancy\Models\Tenant;
use Symfony\Component\Process\Process;
use Upnovation\Easyprofile\Dorotea\UserManager;
use <PERSON><PERSON>\DbDumper\Databases\MySql;

class Backup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep:backup {--dest=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Database backup.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /*(new Process(["mkdir", "-p $destination"]))->run();

        $options = "-u " . env('TENANT_DB_USERNAME') . " -p" . env('TENANT_DB_PASSWORD') . " " . env('TENANT_DB_DATABASE') . "> {$destination}/x.sql";
        
        $process = new Process(["mysqldump", $options]);

        $process->run();

        $this->info($process->getOutput());*/

        foreach (Tenant::all() as $tenant) {
            $filename = $this->option("dest") . "/" . date('Y-m-d H:i:s') . "-{$tenant->name}.sql.gz";

            $this->info("Backup to $filename");

            MySql
                ::create()
                ->setHost(env("TENANT_DB_HOST"))
                ->setPort(env("TENANT_DB_PORT"))
                ->setDbName($tenant->database)
                ->setUserName(env("TENANT_DB_USERNAME"))
                ->setPassword(env("TENANT_DB_PASSWORD"))
                ->useCompressor(new GzipCompressor())
                ->dumpToFile($filename);
        }

        

        
    }
}
