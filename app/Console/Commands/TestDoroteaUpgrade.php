<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;

class TestDoroteaUpgrade extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dorotea:upgrade {--init}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (env('APP_ENV') == 'production') {
            return Command::SUCCESS;
        }

        if ($this->option('init')) {
            Artisan::call('dev:init', [
                "--tenant" => 'dorotea',
            ], $this->output);
        }

        //$filepath = database_path('sql/ep_dorotea-migration-test.sql');
        $filepath = storage_path('app/private/ep_dorotea.sql');

        $this->info("Loading data from: $filepath");
        $sql = file_get_contents($filepath);

        Tenant::whereName('dorotea')->first()->makeCurrent();

        $this->info("Restoring database from dump.");
        DB::connection('tenant')->unprepared($sql);

        Artisan::call('ep:tenant-update', [
            "--name" => 'dorotea',
        ], $this->output);

        return Command::SUCCESS;
    }
}
