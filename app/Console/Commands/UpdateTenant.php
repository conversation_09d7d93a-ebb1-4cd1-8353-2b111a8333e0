<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Spatie\Multitenancy\Models\Tenant;

class UpdateTenant extends InstallTenant
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep:tenant-update {--name=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates tenant.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Common migrations.
        $this->info("Common migrations");
        Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/common"', [], $this->output);

        //Tenant migrations.
        $this->info("Tenant migrations");
        Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/tenant"', [], $this->output);

        foreach (Tenant::all() as $tenant) {
            if ($this->option('name') && $this->option('name') != $tenant->name) {
                $this->info("Skipping tenant {$tenant->name}");
                
                continue;
            }

            //Run tenant specific migrations.
            $this->info("Tenant specific migrations for {$tenant->name}");
            Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/' . ($tenant->name) . '" --tenant=' . ($tenant->id), [], $this->output);

            // Run migrations meant for maintenance tasks (tenant is in production).
            $this->info("Tenant maintenance migrations for {$tenant->name}");
            Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/' . ($tenant->name) . '/maintenance/" --tenant=' . ($tenant->id), [], $this->output);
        }

        return Command::SUCCESS;
    }
}
