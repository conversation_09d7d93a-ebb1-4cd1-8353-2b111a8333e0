<?php namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Spatie\Multitenancy\Models\Tenant;

class DevelopSeed extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:seed {--tenant=} {--seeder=} {--migrate} {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Develop quick tenant migration and rollback.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if (! $tenant = Tenant::whereName($this->option('tenant'))->first()) {
            $this->error('Tenant not found.');
            return Command::FAILURE;
        }

        if ($this->option('rollback')) {
            // Rollback.
            Artisan::call('tenants:artisan "migrate:rollback --database=tenant --path=database/migrations/tenant" --tenant='.($tenant->id).'', [], $this->output);
            return Command::SUCCESS;
        }

        if ($this->option('migrate')) {
            // Migrate.
            Artisan::call('tenants:artisan "migrate --database=tenant --path=database/migrations/tenant" --tenant='.($tenant->id).'', [], $this->output);
        }

        if (! $seeder = $this->option('seeder')) {
            return Command::SUCCESS;
        }

        Artisan::call('tenants:artisan "db:seed --database=tenant --class=' . ($seeder) . '"' . ' --tenant=' . ($tenant->id), [], $this->output);
        
        return Command::SUCCESS;
    }
}
