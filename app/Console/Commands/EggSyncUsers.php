<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Multitenancy\Models\Tenant;
use Upnovation\Easyprofile\Dorotea\UserManager;

class EggSyncUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'egg:users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync users from EGG.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Tenant::whereName('dorotea')->first()->makeCurrent();

        $manager = app()->make(UserManager::class);

        $count = $manager->syncUsers();

        $this->info("{$count} users synced.");
    }
}
