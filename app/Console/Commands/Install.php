<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class Install extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep:install {--key=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Install application.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('key') != env('APP_KEY')) {
            $this->error("Bad input.");
            
            return Command::FAILURE;
        }

        $this->info("Setup landlord.");
        
        Artisan::call('migrate:fresh', [
            "--database" => "landlord",
            "--path" => "database/migrations/landlord",
        ], $this->output);

        Artisan::call('migrate', [
            "--database" => "landlord",
            "--path" => "database/migrations/common",
        ], $this->output);

        return Command::SUCCESS;
    }
}
