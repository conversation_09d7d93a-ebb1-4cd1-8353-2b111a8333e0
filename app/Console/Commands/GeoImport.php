<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Models\Tenant;

class GeoImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'geo:import {--limit=} {--key=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import geographical data from sql.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // @FIXME: doesn't work as standalone since it's missing the tenant context

        if (env('APP_ENV') == 'production' && $this->option('key') != env('APP_KEY')) {
            $this->error("Bad input.");
            
            return Command::FAILURE;
        }

        $filepath = database_path('sql/gi_db_comuni.sql');

        if (env('APP_ENV') == 'production') {
            // Since i haven't tested this, just stop for now.
            return Command::FAILURE;


            
            if (! $this->confirm("Questa operazione importerà i dati geografici, sovrascrivendo quelli esistenti. Sicuro?")) {
                return Command::SUCCESS;
            }

            $areYouSure = $this->ask("Scrivi DELETE se sei sicuro di voler procedere.");

            if ($areYouSure != 'DELETE') {
                return Command::SUCCESS;
            }

            // controlla se la tabella esiste
            if (DB::connection('tenant')->getSchemaBuilder()->hasTable('gi_comuni_cap')) {
                $this->error("Ci sono dati nel database. Operazione interrotta.");
                return Command::FAILURE;
            }

            $this->import($filepath);
        }

        $this->import($filepath, $this->option("limit"));

        return Command::SUCCESS;
    }

    public function import($filepath, $limit = null)
    {
        if ($limit) {
            $sql = implode(" ", array_slice(file($filepath), 0, $limit));
        } else {
            $sql = file_get_contents($filepath);
        }

        DB::connection('tenant')->table('gi_comuni_cap')->delete();

        DB::connection('tenant')->unprepared($sql);
    }
}
