<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Form extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $fillable = ['cachedResult'];

    protected $casts = [
        'cachedStructure' => 'array',
        'cachedValues' => 'array',
        'cachedResult' => 'array',
    ];
}
