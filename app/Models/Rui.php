<?php namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Rui extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $table = 'rui';

    protected $fillable = [
        'section',
        'code',
        'subscribed_at',
    ];

    public $timestamps = false;
}
