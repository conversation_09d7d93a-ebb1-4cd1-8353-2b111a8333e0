<?php namespace App\Models\Traits;

use App\Models\Rui;

trait HasRui
{
    /**
     * Get the RUI associated with the model.
     */
    public function rui()
    {
        return $this->belongsTo(Rui::class, 'rui_id');
    }

    public function ruiCode()
    {
        return $this->rui ? $this->rui->code : null;
    }

    public function ruiSection()
    {
        return $this->rui ? $this->rui->section : null;
    }

    public function ruiDate()
    {
        return $this->rui ? $this->rui->subscribed_at : null;
    }

    public function nodeRuiCode()
    {
        return $this->node ? $this->node->ruiCode() : null;
    }

    public function nodeRuiSection()
    {
        return $this->node ? $this->node->ruiSection() : null;
    }

    public function nodeRuiDate()
    {
        return $this->node ? $this->node->ruiDate() : null;
    }
}