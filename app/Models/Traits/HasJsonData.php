<?php namespace App\Models\Traits;

trait HasJsonData
{
    // @todo for now, the model must have field named data that has a cast to array.

    /**
     * it just add value in data[key].
     */
    public function addData($key, $value)
    {
        $this->data = array_merge($this->data ?? [], [
            $key => $value
        ]);
    }

    /**
     * It adds an entry, assuming data[$key] is an array.
     */
    public function addEntry($key, $value, $index = null)
    {
        $data = $this->data ?? [];

        if (! isset($data[$key]) || ! is_array($data[$key])) {
            $data[$key] = [];
        }

        $data[$key][$index ?? count($data[$key])] = $value;

        $this->data = $data;
    }
}