<?php namespace App\Models\Traits;

/**
 * Model translated name.
 */
trait CanGloballySearch
{
    /**
     * Search disabling global scopes.
     * See Upnovation\Easyprofile\Dorotea\PipelineManager for a use case 
     * (check if a client *globally* exists).
     */
    public static function findIfExists($fieldName, $value)
    {
        return self
            ::withoutGlobalScopes()
            ->where($fieldName, $value)
            ->first();
    }
}