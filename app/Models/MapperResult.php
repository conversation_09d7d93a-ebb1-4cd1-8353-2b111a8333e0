<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class MapperResult extends Model
{
    use HasFactory, UsesTenantConnection;

    protected $casts = [
        'recommendations' => 'array',
    ];

    public function product() : BelongsTo
    {
        return $this->belongsTo(Product::class)->with('company')->with('coverages');
    }
}
