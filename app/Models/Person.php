<?php

namespace App\Models;

use App\Models\Interfaces\Clientable;
use App\Models\Scopes\PeopleScope;
use App\Models\Traits\CanDisplayName;
use App\Models\Traits\CanGloballySearch;
use App\Models\Traits\CanSafelyFill;
use App\Models\Traits\HasAddress;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Person extends Model implements Clientable
{
    use HasFactory, UsesTenantConnection, CanSafelyFill, HasAddress, CanDisplayName, CanGloballySearch;

    protected $fillable = [
        'name',
        'lastname',
        'birthdate',
        'birthplace',
        'email',
        'taxCode',
        'phone',
    ];

    protected $casts = [
        'birthdate' => 'date',
        'documents' => 'array',
    ];

    protected static function booted(): void
    {
        static::addGlobalScope(new PeopleScope);

        static::saving(function ($person) {
            if (isset($person->taxCode)) {
                $person->taxCode = strtoupper($person->taxCode);
            }
        });
    }

    public function addresses() : HasMany
    {
        return $this->hasMany(Address::class)->with('zipRecord');
    }

    public function pipelines()
    {
        return $this->hasManyThrough(
            Pipeline::class, // Il modello finale
            Client::class,   // Il modello intermedio
            'person_id',     // Foreign key su Client che punta a Person
            'id',            // Foreign key su Pipeline che punta a Pipeline (di solito 'id')
            'id',            // Local key su Person
            'pipeline_id'    // Local key su Client che punta a Pipeline
        );
    }

    public function enterprises()
    {
        return $this->hasMany(Enterprise::class, 'rep_id');
    }

    public function addDocument(string $type, File $fileInfo)
    {
        if (! isset($this->documents[$type])) {
            throw new Exception("Document type {$type} not found in person's documents.");
        }

        $documents = $this->documents;

        $documents[$type]['files'][] = $fileInfo;

        return $this->documents = $documents;
    }
    
    /**
     * Beware: used in signature and certificate issuance.
     */
    public function getDocument(string $type)
    {
        if($this->documents[$type]) {
            $document = $this->documents[$type];
            $document['expiry'] = Carbon::parse($this->documents[$type]['expiry'] ?? null);
            $document['issuerDate'] = Carbon::parse($this->documents[$type]['issuerDate'] ?? null);
            return (object)$document;
        }

        return null;
    }

    public function getReferral()
    {
        // Trova tutti i pipeline associati ai client della persona
        $pipelineIds = Client::where('person_id', $this->id)->pluck('pipeline_id');

        // Trova tutti gli user_id collegati a quei pipeline
        $userIds = Pipeline::whereIn('id', $pipelineIds)
            ->whereNotNull('user_id')
            ->pluck('user_id')
            ->unique()
            ->toArray();

        // Restituisci il primo user in ordine di creazione
        return User::whereIn('id', $userIds)
            ->with('node')
            ->orderBy('created_at')
            ->first();
    }

    public function getType() : string
    {
        return 'individual';
    }

    public function __toString()
    {
        return "{$this->name} {$this->lastname} {$this->taxcode}";
    }
    

    //
    // @TODO?
    // SignerInterface methods
    //
    //
    public function getName(): string
    {
        return $this->name;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    // @todo refactor to HasAddress trait + Addressable interface 
    public function getAddress(string $type): ?Address
    {
        return $this->addresses()->where('type', $type)->first();   
    }

    // @todo subject interface (for pdf compiling)
    public function getSubjectAddress(): ?Address
    {
        return $this->addresses()->where('type', 'residence')->first();   
    }

    public function getPrintableBirthdate()
    {
        if (! $this->birthdate) {
            return null;
        }

        return $this->birthdate->format('d/m/Y');
    }
    
}
