<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Coverage extends Model
{
    use HasFactory, UsesTenantConnection;

    public $timestamps = false;

    public function coverageCategory() : BelongsTo {
        return $this->belongsTo(CoverageCategory::class);
    }
}
