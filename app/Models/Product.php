<?php namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Product extends Model
{
    use HasFactory, UsesTenantConnection;

    public $timestamps = false;

    protected $fillable = ['code', 'processType'];

    protected $casts = [
        'warnings' => 'array',
        'issuanceProcessor' => 'array',
    ];

    public function company() : BelongsTo 
    {
        return $this->belongsTo(Company::class);
    }

    public function coverages() : BelongsToMany 
    {
        return $this->belongsToMany(Coverage::class)->withPivot('requiredJobs', 'excludedJobs', 'setup', 'inOptions');
    }

    public function mainCoverages() : Collection
    {
        return $this
            ->coverages()
            ->where('type', 'main')

            // Questo filtro era stato introdotto nella commit
            // in cui il mapper ha cominciato a prendere in
            // considerazione la divisione delle garanzie base
            // (obbligatorie / facoltative).
            // Lo commento perché penso sia stato un errore introdurlo,
            // in quanto questo filtro il mapper se lo fa per cazzi
            // suoi in StageInit.findProductsWithMain
            // Lasciando questo sotto abilitato ogni metodo che
            // cercasse di accedere alle garanzie base otterrebbe solo
            // le obbligatorie, e questo è sbagliato.
            //->wherePivot('setup', 'standard')

            ->get();
    }

    public function options() : BelongsToMany 
    {
        return $this->belongsToMany(Coverage::class, "coverage_options")->withPivot('option');
    }

    public function getOptions()
    {
        $options = [];

        foreach($this->options as $item) {
            $options[$item->pivot->option][] = $item->label;
        }

        return $options;
    }

    public function constraints() : BelongsToMany 
    {
        return $this->belongsToMany(Constraint::class);
    }

    //
    // @FIXME! Dorotea specific.
    //
    public function eggProduct() : HasOne 
    {
        return $this->hasOne(EggProduct::class);
    }

    public function documents() : BelongsToMany
    {
        return $this->belongsToMany(Document::class);
    }

    public function getFormDocument()
    {
        return $this->documents()->where('type', 'product-form')->first();
    }

    public function getPolicyDocument()
    {
        return $this->documents()->where('type', 'product-policy')->first();
    }
    
}
