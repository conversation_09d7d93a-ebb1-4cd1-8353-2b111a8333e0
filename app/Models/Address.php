<?php

namespace App\Models;

use App\Models\Traits\CanSafelyFill;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Address extends Model
{
    use HasFactory, UsesTenantConnection, CanSafelyFill;

    protected $fillable = [
        'type',
        'street',
        'number',
        'zip',
    ];

    public function person() : BelongsTo
    {
        return $this->belongsTo(Person::class);
    }

    public function enterprise() : BelongsTo
    {
        return $this->belongsTo(Enterprise::class);
    }

    public function zipRecord() : BelongsTo
    {
        return $this->belongsTo(Zip::class, 'zip', 'cap');
    }

    public function __toString()
    {
        $str = "{$this->street} {$this->number}, {$this->zip} - {$this->zipRecord->denominazione_ita} ({$this->zipRecord->sigla_provincia})";

        return mb_convert_encoding($str, 'UTF-8', 'UTF-8');
    }
}
