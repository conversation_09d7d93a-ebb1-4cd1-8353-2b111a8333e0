<?php namespace App\Models\Rules;

use Illuminate\Contracts\Validation\Rule as RuleContract;
use Illuminate\Database\Eloquent\Model;

/**
 * This rule is required because the multitenancy package does not support the unique rule with a tenant connection.
 */
class UniquePhoneInTenant extends UniqueInTenant implements RuleContract
{
    protected Model $model;
    protected string $column;

    protected $prefix;
    protected $phone;

    public function __construct(Model $model, string $column, $data = [])
    {
        $this->model = $model;
        $this->column = $column;
        $this->prefix = $data['phonePrefix'] ?? null;
        $this->phone = $data['phone'] ?? null;
    }

    public function passes($attribute, $value)
    {
        $fullPhone = $this->prefix . $this->phone;

        return ! $this
            ->model
            ->where('id', '!=', $this->model->id)
            ->where($this->column, $fullPhone)
            ->withoutGlobalScopes()
            ->exists();
    }

    public function message()
    {
        return __('validation.unique_in_tenant', ['attribute' => $this->column, 'model' => $this->model->getDisplayName()]);
    }
}