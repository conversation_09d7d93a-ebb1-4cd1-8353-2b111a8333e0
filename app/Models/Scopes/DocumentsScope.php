<?php

namespace App\Models\Scopes;

use App\Models\NetworkNode;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Log;

class DocumentsScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        if (! $user = auth()->user()) {
            return;
        }

        if ($user->hasRole('manager')) {
            return;
        }
        
        // All ancestors + direct parent.
        $nodes = NetworkNode::ancestorsAndSelf($user->node_id)->pluck('id');
        
        // Enforce user not being able to access documents from nodes they don't have access to.
        $builder->whereIn('node_id', $nodes);
    }
}
