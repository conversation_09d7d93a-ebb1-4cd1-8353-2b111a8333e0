<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class PeopleScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {
        if (auth()->user()->hasRole('manager')) {
            return;
        }

        /*return $builder->whereHas('pipelines', function($query) {
            $query->where('user_idx', auth()->user()->id);
        });*/

        return $builder->where(function($q) {
            // Pipeline dirette
            $q->whereHas('pipelines', function($query) {
                $query->where('user_id', auth()->user()->id);
            })
            // Pipeline tramite enterprise dove la persona è rep
            ->orWhereHas('enterprises', function($query) {
                $query->whereHas('pipelines', function($subQuery) {
                    $subQuery->where('user_id', auth()->user()->id);
                });
            });
        });
    }
}
