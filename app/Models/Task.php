<?php

namespace App\Models;

use App\Models\Scopes\RestrictedScope;
use App\Models\Scopes\TaskScope;
use App\Models\Traits\HasJsonData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Task extends Model
{
    use HasFactory, UsesTenantConnection, HasJsonData;

    protected $fillable = ['priority', 'type', 'template', 'manager', 'controller', 'navigation', 'dependson', 'displayName', 'config', 'data',];

    protected $casts = [
        'config' => 'array',
        'data' => 'array',
    ];

    public function user() : BelongsTo {
        return $this->belongsTo(User::class);
    }

    public function pipeline() : BelongsTo
    {
        return $this->belongsTo(Pipeline::class);
    }

    public function files() : HasMany
    {
        return $this->HasMany(File::class);
    }
    
    public function issuances() : HasMany
    {
        return $this->hasMany(Issuance::class);
    }

    /**
     * This method might be called to compile documents and it's based
     * on the dataInjection property, defined in the task config. 
     * 
     * dataInjection is the method name that should exist in Task.
     */
    public function getInjectableData(mixed $data)
    {
        if (isset($this->config['dataInjection']) && method_exists($this, $this->config['dataInjection'])) {
            return $this->{$this->config['dataInjection']}($data);
        }

        Log::debug("No data injection method defined for task {$this->id}, either config.dataInjection or task method not found.", [
            'config' => $this->config,
        ]);
    }

    /**
     * Data injection callback.
     * 
     * Returns the product form data to be injected into the document.
     */
    public function getDataForDocument(Document $document)
    {
        
        if (! isset($this->data['issuance'])) {
            Log::warning("No issuance data found for task {$this->id} and document {$document->id}");
            return [];
        }

        if (! $product = $document->getProduct()) {
            Log::warning("No product found for document {$document->id} in task {$this->id}");

            return [];
        }

        if (! isset($this->data['issuance'][$product->id])) {
            Log::warning("No issuance data found for product {$product->id} in task {$this->id}");

            return [];
        }

        return $this->data['issuance'][$product->id] ?? [];
    }

    /**
     * Data injection callback.
     * 
     * Returns options to be injected into the document.
     */
    public function getPersonPreferences()
    {
        if (! isset($this->data['preferences']) && ! isset($this->data['preferences']['marketing'])) {
            Log::warning("No preferences data found for task {$this->id}");

            return ['marketing' => false];
        }

        return $this->data['preferences'];
    }

    public function getIssuanceData()
    {
        return $this->data['issuanceData'] ?? [];
    }
}
