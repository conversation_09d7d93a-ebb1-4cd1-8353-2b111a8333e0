<?php

namespace App\Models;

use App\Models\Scopes\ClientsScope;
use App\Models\Traits\CanGloballySearch;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Client extends Model
{
    use HasFactory, UsesTenantConnection, CanGloballySearch;

    protected $casts = [
        'birthdate' => 'date',
    ];

    protected static function booted(): void
    {
        static::addGlobalScope(new ClientsScope);
    }

    public function profile() : HasMany
    {
        return $this->HasMany(Profile::class);
    }

    public function pipeline() : BelongsTo
    {
        return $this->belongsTo(Pipeline::class);
    }

    public function users() : BelongsToMany
    {
        return $this->belongsToMany(User::class, 'pipelines');
    }

    public function person() : BelongsTo
    {
        return $this->belongsTo(Person::class);
    }

    public function enterprise() : BelongsTo
    {
        return $this->belongsTo(Enterprise::class);
    }
}
