<?php

namespace App\Models;

use App\Models\Traits\HasRui;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Kalnoy\Nestedset\NodeTrait;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class NetworkNode extends Model
{
    use HasFactory, NodeTrait, UsesTenantConnection, HasRui;

    public function documents() : HasMany
    {
        return $this->hasMany(Document::class, 'node_id');
    }

    public function __toString()
    {
        return $this->name;
    }
}
