<?php namespace App\Models\Interfaces;

use App\Models\Address;



// NOT USED



interface SignerInterface
{
    /**
     * Get the name of the signer.
     *
     * @return string
     */
    public function getName(): string;

    /**
     * Get the last name of the signer.
     *
     * @return string
     */
    public function getLastname(): string;

    /**
     * Get the email of the signer.
     *
     * @return string
     */
    public function getEmail(): string;

    /**
     * Get the phone number of the signer.
     *
     * @return string|null
     */
    public function getPhone(): ?string;

    /**
     * Get the address of the signer.
     *
     * @return string|null
     */
    public function getAddress(string $type): ?Address;
}