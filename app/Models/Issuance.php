<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Issuance extends Model
{
    use HasFactory, UsesTenantConnection;

    public $fillable = ['product_id', 'status'];

    public function task() : BelongsTo
    {
        return $this->belongsTo(Task::class, 'task_id');
    }
    
    public function product() : BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function contract() : BelongsTo
    {
        return $this->belongsTo(File::class, 'contract_file_id');
    }

    public function form() : BelongsTo
    {
        return $this->belongsTo(File::class, 'form_file_id');
    }
}
