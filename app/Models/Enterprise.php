<?php

namespace App\Models;

use App\Models\Interfaces\Clientable;
use App\Models\Traits\CanDisplayName;
use App\Models\Traits\CanSafelyFill;
use App\Models\Traits\HasAddress;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\Multitenancy\Models\Concerns\UsesTenantConnection;

class Enterprise extends Model implements Clientable
{
    use HasFactory, UsesTenantConnection, HasAddress, CanSafelyFill, CanDisplayName;

    protected $fillable = [
        'name',
        'vat',
    ];

    public function rep() : BelongsTo
    {
        return $this->belongsTo(Person::class, 'rep_id');
    }

    public function pipelines()
    {
        return $this->hasManyThrough(
            Pipeline::class,
            Client::class,
            'enterprise_id', // Foreign key su Client che punta a Enterprise
            'id',            // Foreign key su Pipeline (di solito 'id')
            'id',            // Local key su Enterprise
            'pipeline_id'    // Local key su Client che punta a Pipeline
        );
    }

    public function addresses() : HasMany
    {
        return $this->hasMany(Address::class);
    }

    public function getType() : string
    {
        return 'legal';
    }

    // @todo refactor to HasAddress trait + Addressable interface 
    public function getAddress(string $type): ?Address
    {
        return $this->addresses()->where('type', $type)->first();   
    }

    // @todo subject interface (for pdf compiling)
    public function getSubjectAddress(): ?Address
    {
        return $this->addresses()->where('type', 'headquarters')->first();   
    }

    public function __toString()
    {
        return $this->name . " " . $this->legalForm;
    }
}
