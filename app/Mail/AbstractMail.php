<?php

namespace App\Mail;

use App\Models\Issuance;
use App\Models\Role;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AbstractMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [public_path('assets/logo.svg')];
    }
}
