<?php

namespace App\Mail;

use App\Models\Issuance;
use App\Models\Role;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class IssuanceProcessedMail extends AbstractMail
{
    public Issuance $issuance;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Issuance $issuance)
    {
        $this->issuance = $issuance;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        $users = Role::whereName('manager')->first()->users;

        return new Envelope(
            subject: 'Notifica compilazione scheda prodotto',
            from: env('MAIL_FROM_ADDRESS'),
            to: $users->pluck('email')->toArray(),
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.issuance.compiled',
        );
    }
}
