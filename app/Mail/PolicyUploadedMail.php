<?php

namespace App\Mail;

use App\Models\Issuance;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class PolicyUploadedMail extends AbstractMail
{
    public Issuance $issuance;

    protected $recipient;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(Issuance $issuance)
    {
        $this->issuance = $issuance;

        $this->recipient = $issuance->task->pipeline->user;
    }


    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: 'Notifica polizza caricata',
            to: $this->recipient->email,
            from: env('MAIL_FROM_ADDRESS'),
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(
            view: 'emails.issuance.policy-uploaded',
        );
    }

}
