<?php namespace App\Logging;

use Monolog\Formatter\LineFormatter;

class CustomFormatter
{
    /**
     * Customize the given logger instance.
     *
     * @param  \Illuminate\Log\Logger  $logger
     * @return void
     */
    public function __invoke($logger)
    {
        $tenant = app('currentTenant') ? app('currentTenant')->name : 'landlord';

        $tenant = strtoupper($tenant);

        $user = auth()->user() ? auth()->user()->id : 'guest';

        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new LineFormatter(
                '[%datetime%] %channel%.%level_name%.'.$tenant.'.'.$user.': %message%' . "\n",
                'Y-m-d H:i:s'
            ));
        }
    }
}