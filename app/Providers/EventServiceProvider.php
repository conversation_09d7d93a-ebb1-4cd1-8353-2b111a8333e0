<?php

namespace App\Providers;

use App\Events\SignatureComplete;
use App\Events\IssuanceProcessed;
use App\Events\PolicyUploaded;
use App\Events\PrivacySigned;
use App\Listeners\PipelineNext;
use App\Listeners\SendIssuanceNotification;
use App\Listeners\SendPolicyUploadNotification;
use App\Listeners\SendSignedFiles;
use App\Listeners\UpdatePrivacyFlag;
use App\Listeners\UserLoginListener;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Login::class => [
            UserLoginListener::class
        ],
        IssuanceProcessed::class => [
            SendIssuanceNotification::class,
        ],
        PolicyUploaded::class => [
            SendPolicyUploadNotification::class,
        ],
        SignatureComplete::class => [
            SendSignedFiles::class,
            PipelineNext::class,
        ],
        PrivacySigned::class => [
            UpdatePrivacyFlag::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
