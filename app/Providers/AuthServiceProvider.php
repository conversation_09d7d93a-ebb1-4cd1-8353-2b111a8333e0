<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;

use App\Models\Pipeline;
use App\Models\User;
use App\Policies\PipelinePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        'App\Models\Pipeline' => PipelinePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        /*Auth::viaRequest('egg', function (Request $request) {
            if (app('currentTenant')->name == 'dorotea') {
                return User::with('roles')->first();
            }

            return Auth::guard('web')->user();
        });*/

        Gate::define('pipeline.operate', function (User $user) {
            return $user->hasRole('salesman');
        });

        Gate::define('pipeline.delete', function (User $user) {
            return $user->hasRole('salesman') || $user->hasRole('manager');
        });

        Gate::define('pipeline.view.any', function (User $user) {
            return $user->hasRole('manager');
        });

        Gate::define('pipeline.resume', function (User $user, Pipeline $pipeline) {
            return $user->hasRole('manager') || $user->id === $pipeline->user_id;
        });

        Gate::define('salesman.administer', function (User $user) {
            return $user->hasRole('manager');
        });

        Gate::define('product.view', function (User $user, Pipeline $pipeline) {
            return true;
        });

        Gate::define('documents.view', function (User $user) {
            return true;
        });
    }
}
