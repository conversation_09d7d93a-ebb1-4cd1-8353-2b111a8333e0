<?php

namespace App\Providers;

use App\Http\Middleware\EggSSO;
use App\Models\BaseClient;
use App\Models\ClientInterface;
use Illuminate\Routing\Router;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;
use Laravel\Fortify\Fortify;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(Router $router)
    {
        Fortify::loginView(function () {
            return view('auth.login');
        });

        //
        // To inject tenant's custom bindings.
        //
        /* 
        $this->app->bind(ClientInterface::class, function ($app) {
            $tenant = app('currentTenant')->name;
            
            if ($model = config("easyprofile.clients.{$tenant}.model")) {
                return new $model();
            }
            
            return new BaseClient();
        });
        */

        //$router->aliasMiddleware('auth', EggSSO::class);
    }
}
