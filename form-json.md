# Struttura json

- **version** -> Versione del file
- **action** -> Action del form
- **sections** -> Array delle sezioni
  - **id**
  - **title**
  - **description**
  - **items** -> array dei component da renderizzare
    - **id** [REQ] -> 
    - **component** [REQ] -> Nome del component 
    - **name** [REQ] -> Nome del campo nel DB 
    - **title** [REQ] -> Label 
    - **description** 
    - **orientation** [REQ] -> Orientamento delle options [h|v] su checkbox e radio
    - **options** -> Array delle checkbox/radio da renderizzare 
      - **title** [REQ] -> Label 
      - **name** [REQ] -> Nome del campo nel DB
      - **value** [REQ] -> Valore
      - **description**
      - **helper** -> Eventuale testo tooltip da renderizzare
