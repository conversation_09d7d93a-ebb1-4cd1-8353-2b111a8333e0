{"version": "1.0.2 13/06/2024", "action": "/post", "sections": [{"id": "2", "isLast": false, "title": "Situazione familiare e occupazionale", "description": "", "items": [{"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/status", "title": "Stato civile", "description": "", "options": [{"title": "Coniugato/convivente", "name": "profile.status.married", "value": "married"}, {"title": "Celibe/nubile", "name": "profile.status.single", "value": "single"}, {"title": "Separato/a - Divorziato", "name": "profile.status.divorced", "value": "divorced"}, {"title": "Vedovo/a", "name": "profile.status.widow", "value": "widow"}]}, {"id": 2, "component": "Text", "name": "profile/familyMembers", "title": "Componenti del nucleo familiare"}, {"id": 2, "component": "Text", "name": "profile/familyEarners", "title": "Percettori di reddito del nucleo familiare"}, {"id": 2, "component": "Text", "name": "profile/cohabitants", "title": "<PERSON><PERSON><PERSON> conviventi da tutelare"}, {"id": 2, "component": "Text", "name": "profile/cohabitantsMinors", "title": "Figli minori conviventi"}, {"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/realEstate", "title": "Immobili di proprietà", "description": "", "options": [{"title": "<PERSON><PERSON><PERSON>", "name": "profile.realEstate.none", "value": "none"}, {"title": "Prima casa", "name": "profile.realEstate.firstHouse", "value": "firstHouse"}, {"title": "Ulteriori proprietà", "name": "profile.realEstate.manyProperties", "value": "manyProperties"}]}, {"id": 7, "component": "RadioGroup", "orientation": "h", "name": "profile/smoker", "title": "Fumatore", "description": "", "type": "boolean", "options": [{"title": "Si", "name": "profile.smoker.yes", "value": 1}, {"title": "No", "name": "profile.smoker.no", "value": 0}]}, {"id": 7, "component": "RadioGroup", "orientation": "h", "name": "profile/extremeSports", "title": "Pratica di sport pericolosi", "description": "", "type": "boolean", "options": [{"title": "Si", "name": "profile.extremeSports.yes", "value": 1}, {"title": "No", "name": "profile.extremeSports.no", "value": 0}]}, {"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/job", "title": "Situazione occupazionale", "description": "", "options": [{"title": "Dipendente / Dirigente Azienda nel settore privato (contratto a tempo indeterminato)", "name": "profile.job.employeePrivate", "value": "employeePrivate", "triggerNegative": 999}, {"title": "Dipendente / Dirigente Azienda nel settore pubblico (contratto a tempo indeterminato)", "name": "profile.job.employeePublic", "value": "employeePublic", "triggerNegative": 999}, {"title": "Dipendente nel settore privato (contratto a tempo determinato)", "name": "profile.job.employeePrivateLimited", "value": "employeePrivateLimited", "triggerNegative": 999}, {"title": "Libero professionista", "name": "profile.job.freelance", "value": "freelance", "triggerNegative": 999}, {"title": "Lavoratore autonomo", "name": "profile.job.selfEmployed", "value": "selfEmployed", "triggerNegative": 999}, {"title": "Amministratore / Tito<PERSON>e <PERSON>'<PERSON>", "name": "profile.job.businessOwner", "value": "businessOwner", "triggerNegative": 999}, {"title": "Non occupato", "name": "profile.job.unemployed", "value": "unemployed", "triggerNegative": 999}, {"title": "Pensionato", "name": "profile.job.retired", "value": "retired", "triggerNegative": 999}, {"title": "Altro (specificare)", "name": "profile.job.other", "value": "other", "trigger": 999}]}, {"id": 999, "component": "Text", "name": "profile/customJob", "title": "Specifica la tua situazione occupazionale", "question": "Situazione occupazionale (se diversa da quelle elencate)", "isHidden": true}]}, {"id": 3, "isLast": false, "title": "Analisi generale delle esigenze/bisogni assicurativi", "items": [{"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/mortgage", "title": "Ha in corso o sta sottoscrivendo mutui o finanziamenti?", "type": "boolean", "description": "", "options": [{"title": "Si", "name": "profile.mortgage.yes", "value": 1, "trigger": 993}, {"title": "No", "name": "profile.mortgage.no", "value": 0, "triggerNegative": 993, "triggerCallback": "setNeedToFree"}]}, {"id": 993, "component": "RadioGroup", "orientation": "v", "name": "profile/choice", "title": "Da cosa deriva l'esigenza assicurativa?", "description": "", "defaultValue": "free", "isHidden": true, "options": [{"title": "Contrattuale (Mutuo-Finanziamento)", "name": "profile.choice.constrained", "value": "constrained", "triggerCallback": "setMortgageCoverage"}, {"title": "Libera scelta", "name": "profile.choice.free", "value": "free", "triggerCallback": "setMortgageCoverage"}]}, {"component": "Text", "name": "profile/currentExpense", "title": "Eventuali voci di spesa già in essere"}, {"component": "<PERSON><PERSON>", "title": "Esigenza/e assicurativa/e che si intende perseguire?"}, {"component": "Checkbox", "name": "profile/areaHouse", "title": "Protezione Abitazione", "question": "Interessato alla Protezione Abitazione?", "trigger": 996}, {"component": "Checkbox", "name": "profile/areaAssets", "title": "Protezione del patrimonio (RC vita privata / Reddito / Donazione)", "question": "Interessato alla Protezione del patrimonio (RC vita privata / Reddito / Donazione)?", "isHidden": false, "trigger": 995}, {"component": "Checkbox", "name": "profile/areaPerson", "title": "Protezione della persona / famiglia", "question": "Interessato alla Protezione della persona / famiglia?", "isHidden": false, "trigger": 994}, {"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/length", "title": "Qual'è orizzonte temporale che si prefigge per assicurare le esigenze che ha manifestato?", "description": "", "options": [{"title": "Breve termine (fino a 5 anni)", "name": "profile.duration.short", "value": "short"}, {"title": "Medio termine (tra 6 e 10 anni)", "name": "profile.duration.medium", "value": "medium"}, {"title": "Lungo termine (oltre 10 anni)", "name": "profile.duration.long", "value": "long"}]}, {"id": 7, "component": "RadioGroup", "orientation": "v", "name": "profile/budget", "title": "Capacità attuale di risparmio annuo", "description": "", "options": [{"title": "Fino a 5.000 €", "name": "profile.annualBudget.small", "value": "low"}, {"title": "Da 5.000 € a 10.000 €", "name": "profile.annualBudget.medium", "value": "medium"}, {"title": "Oltre 15.000 €", "name": "profile.annualBudget.large", "value": "large"}]}, {"id": 997, "component": "<PERSON><PERSON>", "title": "Se ha già rapporti assicurativi in corso, quali di queste aree coprono?"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceCar", "question": "Ha già rapporti assicurativi in corso nell'area Auto?", "title": "Auto"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceHouse", "question": "Ha già rapporti assicurativi in corso nell'area Abitazione?", "title": "Abitazione"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceInjury", "question": "Ha già rapporti assicurativi in corso nell'area Infortuni?", "title": "Infortuni"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceIllness", "question": "Ha già rapporti assicurativi in corso nell'area Malattia?", "title": "Malattia"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceDeath", "question": "Ha già rapporti assicurativi in corso nell'area Decesso?", "title": "<PERSON><PERSON><PERSON>"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceRC", "question": "Ha già rapporti assicurativi in corso nell'area RC vita privata?", "title": "RC vita privata"}, {"id": 997, "component": "Checkbox", "name": "profile/currentInsuranceOther", "question": "Ha già rapporti assicurativi in corso in aree diverse da quelle elencate in precedenza?", "title": "Altro"}]}, {"id": 4, "isLast": false, "title": "Analisi specifica dei bisogni di copertura assicurativa", "items": [{"id": 996, "component": "CheckboxGroup", "isHidden": true, "orientation": "v", "name": "coverage/house", "title": "A quali tipologie di copertura assicurativa collegate al rischio abitazione ritiene di avere esigenza?", "type": "coverage", "description": "", "options": [{"title": "Incendio (fabbricato) + Esplosione e scoppio, fulmine, fumi-gas, danni arrecati a seguito ordine Autorità, demolizione/sgombero", "name": "fire.building", "value": ""}, {"title": "Eventi atmosferici", "name": "weather", "value": "", "isHidden": false}, {"title": "<PERSON><PERSON> vandal<PERSON> / Eventi socio-politici", "name": "sociopolitical|vandalism", "value": "", "isHidden": false}, {"title": "Fenomeno elettrico", "name": "electric", "value": "", "isHidden": false}, {"title": "Ricorso terzi", "name": "rt", "value": "", "isHidden": false}, {"title": "Rischio catastrofale terremoto (fabbricato)", "name": "eq.building", "value": "", "isHidden": false}, {"title": "Rischio catastrofale te<PERSON> (contenuto)", "name": "eq.contents", "value": "", "isHidden": false}, {"title": "Rischi catastrofali - Indondazione, alluvione e allagamento", "name": "flood", "value": "", "isHidden": false}]}, {"id": 995, "component": "CheckboxGroup", "isHidden": true, "orientation": "v", "name": "coverage/assets", "title": "A quali tipologie di copertura assicurativa collegate al proprio reddito o patrimonio ritiene di avere esigenza?", "type": "coverage", "description": "", "options": [{"title": "<PERSON>den<PERSON><PERSON> in caso di riduzione reddito (solo per lavoratori autonomi / liberi professionisti / imprenditori)", "name": "rir", "value": ""}, {"title": "Indennizzo in caso di perdita lavoro (lavoratori dipendenti)", "name": "pii", "value": ""}]}, {"id": 994, "component": "<PERSON><PERSON>", "isHidden": true, "title": "A quali tipologie di coperture assicurative collegate alla persona ritiene di avere maggiore esigenza?"}, {"id": 994, "component": "CheckboxGroup", "orientation": "v", "name": "coverage/injury", "title": "Protezione da infortunio", "type": "coverage", "isHidden": true, "options": [{"title": "Corresponsione di un capitale ai beneficiari in caso di morte da infortunio", "name": "tcmci", "value": ""}, {"title": "Corresponsione di un rimborso / indennizzo in caso di invalidità totale permanente da infortunio", "name": "ipti", "value": ""}, {"title": "Corresponsione di un rimborso / indennizzo in caso di inabilità totale temporanea da infortunio", "name": "itti", "value": ""}, {"title": "Corresponsione di rimborso / indennizzo in caso di grande intervento chirurgico", "name": "<PERSON><PERSON>", "value": ""}]}, {"id": 994, "component": "CheckboxGroup", "orientation": "v", "name": "coverage/illness", "title": "Prevenzione da Malattia", "type": "coverage", "isHidden": true, "options": [{"title": "Corresponsione di un rimborso / indennizzo in caso di invalidità totale permanente da malattia", "name": "iptm", "value": ""}, {"title": "Corresponsione di un rimborso / indennizzo in caso di inabilità totale temporanea da malattia", "name": "ittm", "value": ""}, {"title": "Corresponsione di rimborso / indennizzo in caso di grande intervento chirurgico", "name": "<PERSON>m", "value": ""}]}, {"id": 994, "component": "CheckboxGroup", "orientation": "v", "name": "coverage/protection", "title": "Protezione beneficiari in caso decesso / stati di non-autosufficienza", "type": "coverage", "description": "", "isHidden": true, "options": [{"title": "Corresponsione di un capitale ai beneficiari in caso di morte per qualsiasi causa", "name": "tcmcq", "value": ""}, {"title": "Corresponsioni di un capitale ai beneficiari in caso di morte per qualsiasi causa a copertura del capitale residuo", "name": "tcmdq", "value": ""}, {"title": "Corresponsione di un capitale ai beneficiari in caso di stato di non-autosufficienza", "name": "ltc", "value": ""}]}]}, {"id": 5, "isLast": true}]}