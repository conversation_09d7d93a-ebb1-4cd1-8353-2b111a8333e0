import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import {execSync} from 'node:child_process';

let hash = execSync('git rev-parse HEAD').toString().trimEnd();
let tag = null;

try {
    tag = execSync('git describe --tags').toString().trimEnd();

    console.log("Build with tag" + tag);
} catch {
    tag = null;
}

process.env.VITE_GIT_COMMIT_HASH = hash;
process.env.VITE_GIT_COMMIT_TAG = tag;

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.js'],
            refresh: true,
        }),
        vue({ 
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ]
});
