<?php

namespace Tests\Feature;

use App\Models\User;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class EnsureSpecificTenantTest extends TestCase
{
    /** @dataProvider provider */
    public function test_tenant_cannot_access_specific_route($expectation, $tenant)
    {
        $tenant = Tenant::whereName($tenant)->first();

        $tenant->makeCurrent();

        $response = $this->get('http://dorotea.laravel.test/testing/tenants'); // @fixme: url should be tenant-aware

        $response->assertStatus($expectation);
    }

    public function provider()
    {
        return [
            [201, 'dorotea'],
            //[201, 'tenant1'],
            [404, 'tenant2'],
        ];
    }
}