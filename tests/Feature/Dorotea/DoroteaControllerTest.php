<?php

namespace Tests\Feature\Dorotea;

use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class DoroteaControllerTest extends TestCase
{
    public function setUp() : void
    {
        parent::setUp();

        Tenant::whereName('dorotea')->first()->makeCurrent();
    }

    /**
     * @dataProvider ssoParamsProvider
     */
    public function test_sso_params($code, $route, $url)
    {
        $response = $this->get($url);
        
        $response->assertStatus($code);

        if ($route) {
            $response->assertRedirectToRoute($route);
        }
    }

    public function ssoParamsProvider()
    {
        $this->createApplication();

        return [
            // Salesman
            [400, null, 'http://dorotea.laravel.test/dorotea/auth'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?session_token=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?user_id=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?session_token=&user_id=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?session_token=1&user_id='],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?session_token='],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?user_id='],
            [401, null,  'http://dorotea.laravel.test/dorotea/auth?session_token=xxx&user_id=yyy'],
            [401, null,  "http://dorotea.laravel.test/dorotea/auth?session_token=".(env('EGG_TESTING_TOKEN'))."&user_id=".(env('EGG_TESTING_USERID')).""],

            // Add more tests after the renaming of params.
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?token=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?utenteID=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?token=&utenteID=1'],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?token=1&utenteID='],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?token='],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?utenteID='],
            [400, null,  'http://dorotea.laravel.test/dorotea/auth?token=xxx&utenteID=yyy'],
            [400, null,  "http://dorotea.laravel.test/dorotea/auth?token=".(env('EGG_TESTING_TOKEN'))."&utenteID=".(env('EGG_TESTING_USERID')).""],
            
            // no idea why this is not working.
            //[302, 'pipeline.resume',  "http://dorotea.laravel.test/dorotea/auth?session_token=".(env('EGG_TESTING_TOKEN'))."&user_id=".(env('EGG_TESTING_USERID'))."&praticaID=".(env('EGG_TESTING_PRATICAID'))],
        ];
    }

    /**
     * @dataProvider ssoRedirectProvider
     */
    public function _test_sso_redirects_salesman($expectation, $url)
    {
        
        $response = $this
            //->actingAs(User::find(1))
            ->get($url);

        $response->assertRedirectToRoute($expectation);
    }

    public function ssoRedirectProvider()
    {
        $this->createApplication();

        $url = "http://dorotea.laravel.test/dorotea/auth?token=".(env('EGG_TESTING_TOKEN'))."&utenteID=".(env('EGG_TESTING_USERID'));

        return [
            ['dashboard', $url],
            ['dashboard', $url . "&praticaID=zzz"],
            ['dorotea.pipeline.start', $url . "&praticaID=zzz&anagraficaID=kkk"],
        ];
    }
}