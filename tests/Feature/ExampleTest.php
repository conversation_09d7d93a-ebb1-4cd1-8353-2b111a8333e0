<?php

namespace Tests\Feature;

// use Illuminate\Foundation\Testing\RefreshDatabase;

use App\Models\Product;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Mockery;
use Spatie\Multitenancy\Commands\Concerns\TenantAware;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    //use RefreshDatabase;
    //use TenantAware;

    /**
     * A basic test example.
     *
     * @return void
     */
    public function _test_the_application_redirects_anonymous()
    {
        $response = $this->get('/');

        $response->assertRedirect('login');
    }

    public function _test_foo()
    {
        $dorotea = new Tenant();
        $dorotea->domain = 'dorotea.laravel.test';
        $dorotea->name = 'dorotea';

        $self = $this;

        $dorotea->execute(function() use($self) {
            $response = $self->get('http://dorotea.laravel.test');
            dump(Tenant::current());

            $this->assertTrue(true);
        });

        //dump(Tenant::current());
    }

}
