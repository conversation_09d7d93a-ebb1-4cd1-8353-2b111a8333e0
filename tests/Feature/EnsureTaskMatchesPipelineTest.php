<?php

namespace Tests\Feature;

use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class EnsureTaskMatchesPipelineTest extends TestCase
{
    /** @dataProvider provider */
    public function test_ensure_task_matches_pipeline($expectation, $pipelineId, $taskId)
    {
        $tenant = Tenant::whereName('tenant1')->first();

        $tenant->makeCurrent();

        DB::connection('tenant')->table('tasks')->delete();
        DB::connection('tenant')->table('pipelines')->delete();

        Pipeline::factory()->create(['id' => 1]);
        Pipeline::factory()->create(['id' => 2]);
        Task::factory()->create(['id' => 1, 'pipeline_id' => 1]);
        Task::factory()->create(['id' => 2, 'pipeline_id' => 2]);

        $response = $this->get("http://{$tenant->domain}/testing/task/matches/{$pipelineId}/{$taskId}");

        $response->assertStatus($expectation);
    }

    public function provider()
    {
        return [
            [200, 1, 1,],
            [400, 1, 2,],
            [200, 2, 2,],
            [400, 2, 1,],
        ];
    }
}