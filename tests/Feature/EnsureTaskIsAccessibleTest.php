<?php

namespace Tests\Feature;

use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class EnsureTaskIsAccessibleTest extends TestCase
{
    public function test_it_returns_400_when_no_arguments_are_provided()
    {
        $response = $this->get('http://dorotea.laravel.test/testing/task/accessible');

        $response->assertStatus(400);
    }

    /** @dataProvider provider */
    public function test_ensure_task_is_accessible($expectation, $pipelineId, $taskId, $isCurrent)
    {
        $tenant = Tenant::whereName('tenant1')->first();

        $tenant->makeCurrent();

        DB::connection('tenant')->table('tasks')->delete();
        DB::connection('tenant')->table('pipelines')->delete();

        Pipeline::factory()->create(['id' => 1]);
        Task::factory()->create(['id' => 1, 'state' => $isCurrent ? 'progress' : 'open']);

        $response = $this->get("http://{$tenant->domain}/testing/task/accessible/{$pipelineId}/{$taskId}");

        $response->assertStatus($expectation);
    }

    public function provider()
    {
        return [
            [404, 0, 0, null],
            [404, 0, 1, null],
            [404, 1, 0, null],
            [200, 1, 1, true],
            [302, 1, 1, false],
        ];
    }
}