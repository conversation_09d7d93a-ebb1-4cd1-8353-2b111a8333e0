<?php

namespace Tests\Unit;

use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Mockery;
use Tests\TestCase;
use Upnovation\Easyprofile\PipelineManager;
use Upnovation\Easyprofile\Tasks\AbstractTask;

class PipelineManagerTest extends TestCase
{
    /** @var PipelineManager */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        //$this->manager = app()->make(PipelineManager::class);
        $this->manager = Mockery::mock(PipelineManager::class)->makePartial();
    }

    /** @dataProvider navigationProvider */
    public function test_get_accessible($expectation, $task, $pipelineState)
    {
        if ($expectation === 'exception') {
            $this->expectException(Exception::class);
        }

        $this->assertEquals($expectation, $this->manager->getAccessible($task, $pipelineState));
    }

    public function navigationProvider()
    {
        return [
            // Navigation not set
            /*['exception', new Task(), ''],
            ['exception', new Task(['navigation' => '']), ''],
            
            // Navigation invalid
            ['exception', new Task(['navigation' => 'invalid', 'dependson' => null]), ''],*/

            // Valid navigation values: pipeline.open
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => 'y']), 'open'],
            [false, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open', 'dependson' => null]), 'open'],

            // Valid navigation values: pipeline.closed
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.closed', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'pipeline.closed', 'dependson' => null]), 'closed'],

            // Valid navigation values: always
            [true, new Task(['navigation' => 'always', 'dependson' => null]), ''],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'always', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'always', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'always', 'dependson' => null]), 'x'],

            // Valid navigation values: never
            [false, new Task(['navigation' => 'never', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'never', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'never', 'dependson' => 'y']), 'open'],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'closed'],
            [false, new Task(['navigation' => 'never', 'dependson' => null]), 'x'],

            // Composite states
            [true, new Task(['navigation' => 'always|pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'never|pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'x'],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => 'y']), ''],
            [false, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => 'y']), 'open'],
            [true, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open|pipeline.closed', 'dependson' => null]), 'open'],
            [true, new Task(['navigation' => '|pipeline.closed', 'dependson' => null]), 'closed'],
            [true, new Task(['navigation' => 'pipeline.open|', 'dependson' => null]), 'open'],
            [true, new Task(['navigation' => 'pipeline.open|x', 'dependson' => null]), 'open'],
        ];
    }

    public function skipProviderConditions()
    {
        return [
            // Expectation,     currentTask                     // targetTask                     
            [null,              ['config' => ['no skip configured']],               []],
            [null,              ['config' => ['canSkipTo' => null]],                []],
            [null,              ['config' => ['canSkipTo' => '']],                  []],

            [null,              ['config' => ['canSkipTo' => 'foo'], 'priority' => 1],         ['priority' => 1]],
            [null,              ['config' => ['canSkipTo' => 'foo'], 'priority' => 2],         ['priority' => 1]],

            [null,              ['config' => ['canSkipTo' => 'foo'], 'priority' => 1],         ['priority' => 2, 'type' => 'bar']],
           
            //[true,              ['config' => ['canSkipTo' => 'bar'], 'priority' => 1],         ['priority' => 2, 'type' => 'bar']],

            /*[null,              ['config' => ['canSkipTo' => 'bad name']],          1],*/
            
            
            //[null,              ['canSkipTo' => ['foo']],         1],
        ];
    }

    /**
     * @dataProvider skipProviderConditions
     */
    public function testSkip_initial_conditions_are_met($expectation, $currentTaskConfig, $targetTaskConfig)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $pipeline = Mockery::mock(Pipeline::class)->makePartial();

        $currentTask = new Task();
        $currentTask->forceFill($currentTaskConfig);
        $targetTask = new Task();
        $targetTask->forceFill($targetTaskConfig);

        $pipeline
            ->shouldReceive('currentTask')
            ->andReturn($currentTask);

        if ($expectation) {
            $pipeline
            ->shouldReceive('prioritizedTasks')
            ->andReturn([]);
        }

        $result = $this->manager->skip($pipeline, new Task());

        $this->assertEquals($expectation, $result);
    }

    
    public function skipProviders()
    {
        return [
            [
                true, 
                [
                    'config' => ['canSkipTo' => 'bar'], 'priority' => 1, 'manager' => AbstractTask::class
                ], 
                [
                    'priority' => 2, 'type' => 'bar', 'manager' => AbstractTask::class
                ],
            ],

            // @todo finalize with real cases
        ];
    }

    /**
     * @dataProvider skipProviders
     */
    public function testSkip($expectation, $currentTaskConfig, $targetTaskConfig)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $pipeline = Mockery::mock(Pipeline::class)->makePartial();
        $pipeline->state = 'foo';

        $currentTask = new Task();
        $currentTask->forceFill($currentTaskConfig);
        $targetTask = new Task();
        $targetTask->forceFill($targetTaskConfig);

        $pipeline
            ->shouldReceive('currentTask')
            ->andReturn($currentTask);

        if ($expectation) {
            $pipeline
            ->shouldReceive('prioritizedTasks')
            ->andReturn(new Collection([$currentTask, $targetTask]));
        }

        $tasksRelationMock = Mockery::mock(\Illuminate\Database\Eloquent\Relations\HasMany::class);
        $tasksRelationMock->shouldReceive('saveMany')->once()->andReturnTrue();
        $pipeline->shouldReceive('tasks')->andReturn($tasksRelationMock);

        $manager = Mockery::mock(AbstractTask::class);
        $manager->shouldReceive('skip');
        $manager->shouldReceive('initialize');
        app()->instance(AbstractTask::class, $manager);

        //$this->manager->shouldReceive('getAccessible');

        $result = $this->manager->skip($pipeline, $targetTask);

        $this->assertEquals($targetTask, $result);
    }
}
