<?php namespace Tests\Unit\Quote;

use Tests\TestCase;
use TypeError;
use Upnovation\Easyprofile\Quote\QuoteService;
use Upnovation\Easyprofile\Quote\QuoteServiceException;

class QuoteServiceTest extends TestCase
{
    protected QuoteService $quoteService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->quoteService = new QuoteService();
    }
    
    public function testConnectivity()
    {
        $this->quoteService->getToken();
    }

    public function authDataProvider()
    {
        return [
            [QuoteServiceException::class, 400, '', ''],
            [QuoteServiceException::class, 400, null, null],
            [QuoteServiceException::class, 400, 'foo', ''],
            [QuoteServiceException::class, 400, '', 'bar'],
            [false, 200, 'foo', 'bar'],
            [true, 200, 'env', 'env'],
        ];
    }

    /**
     * @dataProvider authDataProvider
     */
    public function testAuth($expectation, $exceptionCode, $username, $password)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
            $this->expectExceptionCode($exceptionCode);
        }

        $parameters = [
            'email' => $username,
            'password' => $password,
        ];

        if ($username == 'env') {
            $parameters['email'] = env('QUOTE_API_USER');
        }

        if ($password == 'env') {
            $parameters['password'] = env('QUOTE_API_PASSWORD');
        }

        $response = $this->quoteService->call('auth.php', $parameters);

        $this->assertEquals($expectation, $response['success']);

        if ($expectation) {
            $this->assertArrayHasKey('token', $response);
            $this->assertNotEmpty($response['token']);
        } 
    }

    public function testGetToken()
    {
        $token = $this->quoteService->getToken();
        $this->assertNotEmpty($token);
        $this->assertEquals($this->quoteService->getCachedToken(), $token);
    }

    public function quoteDataProvider()
    {
        return [
            [TypeError::class, null, []],
            [QuoteServiceException::class, '', []],
            [QuoteServiceException::class, 'bad product', []],
            [QuoteServiceException::class, 'CFMUTUIEVOL', []],
            [QuoteServiceException::class, 'CFMUTUIEVOL', []],
            [QuoteServiceException::class, 'CFMUTUIEVOL', ['capitale' => 1000]],
            [QuoteServiceException::class, 'CFMUTUIEVOL', ['capitale' => 1000, 'durata' => 12]],
            [QuoteServiceException::class, 'CFMUTUIEVOL', ['combinazione' => 'foo', 'capitale' => 1000, 'durata' => 12]],
            ['', 'CFMUTUIEVOL', ['combinazione' => 1, 'capitale' => 1000, 'durata' => 12]],
        ];
    }

    /**
     * @dataProvider quoteDataProvider
     */
    public function testQuote($expectation, $product, $parameters)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }
     
        $quote = $this->quoteService->quote($product, $parameters);

        $this->assertNotEmpty($quote['totale']);

        $sum = 0;
        foreach ($quote['addendi'] as $addendum) {
            $sum += $addendum;
        }

        $this->assertEquals($quote['totale'], $sum);
    }
}
