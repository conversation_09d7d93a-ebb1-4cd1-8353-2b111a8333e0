<?php namespace Tests\Unit\Tasks\Signature;

use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Mockery;
use Tests\TestCase;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\PipelineManager;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;

class SignatureManagerTest extends TestCase
{
    protected SignatureManager $manager;

    protected $files;

    protected $documents;

    protected $surveys;

    protected $mock;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(SignatureManager::class);

        $this->files = Mockery::mock(FileManager::class);

        $this->documents = Mockery::mock(DocumentsManager::class);

        $this->surveys = Mockery::mock(SurveyManager::class);

        $this->mock = Mockery::mock(SignatureManager::class, [$this->documents, $this->files, $this->surveys])->makePartial();
    }

    public function initializeProvider()
    {
        return [
            [Exception::class, []],
            [null, [1]],
        ];
    }

    /**
     * @dataProvider initializeProvider
     */
    public function testInitialize($expectation, $collection)
    {
        // make SignatureManager partial mock with mockery
        $manager = Mockery::mock(SignatureManager::class)->makePartial();

        $manager->shouldReceive('loadFiles')
            ->once()
            ->with(Mockery::type(Task::class))
            ->andReturn(collect($collection));

        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $task = new Task();

        $result = $manager->initialize($task);

        $this->assertEquals($task, $result); 
    }

    public function testFinalize_should_delete_all_signable_files_in_pipeline()
    {
        $task = new Task();
        $task->pipeline = new Pipeline();

        $this->files->shouldReceive('deletePipelineSignableFiles');

        $this->assertEquals($task, $this->mock->finalize($task)); 
    }

    public function tearDown() : void
    {
        parent::tearDown();

        Mockery::close();
    }
}