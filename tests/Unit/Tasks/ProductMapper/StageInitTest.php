<?php

namespace Tests\Unit\Tasks\ProductMapper;

use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageInit;

class StageInitTest extends TestCase
{
    /** @var StageInit */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(StageInit::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    //
    //
    // ==========================================================
    // Expecting products to be found when main coverages from
    // client's profile are matched.
    //
    //

    public function test_find_products_with_main_and_empty_input()
    {
        $result = $this->manager->findProductsWithMain([]);

        $this->assertEmpty($result);
    }

    /** @dataProvider productCoveragesProvider */
    public function test_find_products_with_main($expectation, $coverages)
    {
        $this->seed(ProductMapperSeeder::class);

        $result = $this->manager->findProductsWithMain($coverages);

        $this->assertEquals(count($expectation), $result->count());

        foreach($expectation as $name) {
            $this->assertTrue($result->contains('name', $name));
        }
    }

    public function productCoveragesProvider()
    {
        return [
            [[], ['doesnt-exist']],
            [['p1', 'p2', 'p5', 'p6'], ['main1', 'doesnt-exist']],
            [['p1', 'p2', 'p5', 'p6'], ['main1']],
            [['p1', 'p5', 'p6'], ['main2']],
            [['p2'], ['main3']],
            [['p1', 'p2', 'p5', 'p6'], ['main2', 'main3']],
            // Check standard/optional
            [['p10'], ['main10']],
            [['p10', 'p11'], ['main10', 'main11']],
            [['p11'], ['main11']],
        ];
    }
}
