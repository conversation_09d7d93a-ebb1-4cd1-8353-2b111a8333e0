<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use App\Models\Profile;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageConstraints;

class StageConstraintsTest extends TestCase
{
    /** @var StageConstraints */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(StageConstraints::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    //
    //
    // ========================================================
    // age
    //

    /** @dataProvider ageConstraintsProvider */
    public function test_age_constraints($expectation, $age, $min, $max)
    {
        if ($age == null) {
            $this->expectException(\Exception::class);
        }

        $result = $this->manager->checkAgeConstraints($age, $min, $max);

        $this->assertEquals($expectation, $result);
    }

    public function ageConstraintsProvider()
    {
        return [
            [ null, null, 1, null ],
            [ null, null, null, null ],
            [ 'ex', 0, null, null ],
            [ 'ex', 0, null, 1 ],

            [ true, 10, null, null ],
            [ true, 1, 0, null ],
            [ true, 1, 1, null ],
            [ true, 1, 0, null ],
            [ false, 1, 2, null ],

            [ true, 1, null, 1 ],
            [ true, 1, null, 0 ],
            [ true, 1, null, 1 ],

            [ true, 1, 0, 1 ],
            [ true, 1, 1, 1 ],
            [ true, 1, 1, 2 ],
            [ false, 3, 1, 2 ],
            [ false, 0, 1, 2 ],
        ];
    }

    //
    //
    // ========================================================
    // job
    //

    /** @dataProvider jobConstraintsProvider */
    public function test_check_job_constraints($expectation, $productId, $profileId)
    {
        $this->seed(ProductMapperSeeder::class);

        $profile = Profile::find($profileId);

        if (! $profile->job) {
            $this->expectException(\Exception::class);
        }

        $item = new ProductMapperResultItem(Product::find($productId), new ProductMapperResult());

        $result = $this->manager->checkJobConstraints(
            Profile::find($profileId),
            $item
        );

        $this->assertEquals($expectation, count($result));
    }

    public function jobConstraintsProvider()
    {
        return [
            // Exceptions
            [ 'exception' , 1, 4],

            // Prod 1
            // result count, product id, profile id
            [ 2, 1, 1],
            [ 1, 1, 2],
            [ 0, 1, 3],

            // Prod 2
            [ 1, 2, 1],
            [ 1, 2, 2],
            [ 1, 2, 3],

            // Prod 4
            [ 0, 4, 2],
        ];
    }

    /** @dataProvider checkJobProvider */
    public function test_check_job($expectation, $job, $jobs, $mode)
    {
        if ($expectation === 'exception') {
            $this->expectException(\Exception::class);
        }

        $result = $this->manager->checkJobs($job, $jobs, $mode);

        $this->assertEquals($expectation, $result);
    }

    public function checkJobProvider()
    {
        return [
            // Exceptions
            [ 'exception' , '', '', ''],
            [ 'exception' , '', '', 'doesnt-exist'],
            [ 'exception', '', '', 'required'],

            // Required
            // [true, '', null, 'required'], <-- null not allowed by design
            // [true, 'any-string', null, 'required'], <-- null not allowed by design
            [false, '', 'job1', 'required'],
            [true, 'job1', 'job1', 'required'],
            [true, 'job1', 'job1|', 'required'],
            [true, 'job1', 'job1|job2', 'required'],
            [true, 'job1', 'job1|job1', 'required'],
            [true, 'job1', '|job1|job2', 'required'],
            [true, 'job2', 'job1|job2', 'required'],
            [true, 'job2', 'job1|job2|', 'required'],
            [false, 'job1', 'job11', 'required'],
            [false, 'job1', 'jjob11', 'required'],
            [true, 'job1', 'jjob11|job1', 'required'],
            [false, 'job1|', 'job1|job2', 'required'],
            [false, '|job2', 'job1|job2', 'required'],

            // Excluded
            // [true, '', null, 'excluded'], <-- null not allowed by design
            // [true, 'any-string', null, 'excluded'], <-- null not allowed by design
            [true, '', 'job1', 'excluded'],
            [false, 'job1', 'job1', 'excluded'],
            [false, 'job1', 'job1|', 'excluded'],
            [false, 'job1', 'job1|job2', 'excluded'],
            [false, 'job1', 'job1|job1', 'excluded'],
            [false, 'job1', '|job1|job2', 'excluded'],
            [false, 'job2', 'job1|job2', 'excluded'],
            [false, 'job2', 'job1|job2|', 'excluded'],

            [true, 'job1', 'job11', 'excluded'],
            [true, 'job1', 'jjob11', 'excluded'],

            [false, 'job1', 'jjob11|job1', 'excluded'],
            
            [true, 'job1|', 'job1|job2', 'excluded'],
            [true, '|job2', 'job1|job2', 'excluded'],
        ];
    } 

    //
    //
    // ========================================================
    // length
    //

    /** @dataProvider rangeProvider */
    public function test_check_range($expectation, $value, $min, $max)
    {
        $this->assertEquals($expectation, $this->manager->checkRange($value, $min, $max));
    }

    public function rangeProvider()
    {
        return [
            [ false, 0, 5, 40],
            [ true, 5, 5, 40],
            [ true, 0, 0, 0],
            [ true, 0, 0, 1],
            [ false, 1, 0, 0],
            [ true, 1, 0, 1],
            [ false, 1, 1, 0],
            [ true, 1, 1, 2],
            [ false, 0, 1, 2],
            [ true, 1, 1, INF],
            [ false, 1, INF, INF],
            [true, 2, 1, null],
            [true, 2, null, 3],
            [true, 6, 5, null],
            [true, 10, 5, null],
            [true, 10, 5, null],
            [true, INF, 5, null],
            [true, 1, null, null],
        ];
    }

    /** @dataProvider lengthProvider */
    public function test_check_length($expectation, $profileMin, $profileMax, $productMin, $productMax)
    {
        $this->assertEquals(
            $expectation, 
            $this->manager->checkLength($profileMin, $profileMax, $productMin, $productMax)
        );
    }

    public function lengthProvider()
    {
        return [
            [ true, 5, 40, 5, 40],
            [ true, 6, 40, 5, 40],
            [ true, 5, 39, 5, 40],
            [ true, 4, 40, 5, 40],
            [ true, 5, 41, 5, 40],
            [ false, 3, 4, 5, 40],
            [ false, 41, 42, 5, 40],

            // Real world cases

            // Incendio Unico casa New
            [ true, 0, 5, 5, 40],
            [ true, 6, 10, 5, 40],
            [ true, 10, INF, 5, 40],
            [ false, 0, 4, 5, 40],
            [ false, 41, INF, 5, 40],

            // Sei Coperto
            [ true, 0, 5, 0, INF],

            // Sei Coperto Easy
            [ true, 0, 5, 3, 15],
            [ true, 6, 10, 3, 15],
            [ true, 10, INF, 3, 15],

            // Nice Life
            [ true, 0, 5, 1, 30],
            [ true, 6, 10, 1, 30],
            [ true, 10, INF, 1, 30],

            // Protezione Massima
            [ true, 0, 5, 3, 5],
            [ false, 6, 10, 3, 5],
            [ false, 10, INF, 3, 5],

            // Vita a colori
            [ true, 0, 5, 5, 35],
            [ true, 6, 10, 5, 35],
            [ true, 10, INF, 5, 35],

            // Protezione eventi naturali piu
            [ true, 0, 5, 0, 0],
            [ true, 6, 10, 0, 0],
            [ true, 10, INF, 0, 0],
            [ true, rand(0, 100), rand(0, 100), 0, 0],

            // Protezione salute piu
            [ true, 0, 5, 1, 10],
            [ true, 6, 10, 1, 10],
            [ true, 10, INF, 1, 10],

            // Vivendo 4 100
            [ true, 0, 5, 3, 10],
            [ true, 6, 10, 3, 10],
            [ true, 10, INF, 3, 10],

            // Vivendo Elite 100
            [ true, 0, 5, 1, 10],
            [ true, 6, 10, 1, 10],
            [ true, 10, INF, 1, 10],
        ];
    }
}