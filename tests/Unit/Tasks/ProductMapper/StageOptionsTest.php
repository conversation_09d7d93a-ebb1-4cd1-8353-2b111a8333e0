<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use Exception;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageOptions;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageOptionsSeeder;

class StageOptionsTest extends TestCase
{
    /** @var StageOptions */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(StageOptions::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    /** @dataProvider checkProductOptionsProvider */
    public function test_check_product_options($expectationBool, $expectationArray, $productId, $label)
    {
        // @TODO refactor to StageOptionsSeeder
        $this->seed(StageOptionsSeeder::class);

        if ($expectationBool === 'exception') {
            $this->expectException(Exception::class);
        }

        $result = $this->manager->checkProductOptions(Product::find($productId), $label);

        $this->assertEquals($expectationBool, $result->result);
        $this->assertEquals($expectationArray, array_values($result->unallowedCoverages));
    }

    public function checkProductOptionsProvider()
    {
        return [
            ['exception', [], 1, []], 
            [true, [], 1, ['main1']],
            [true, [], 1, ['main2']],

            [false, ['main1'], 2, ['main1']],
            [false, ['main2'], 2, ['main2']],
            [true, [], 2, ['main1', 'main2']],
            [true, [], 2, ['main1', 'main2', 'main3']],
            [false, ['main1'], 2, ['main1', 'main3']],

            [false, ['main1'], 3, ['main1']],
            [false, ['main2'], 3, ['main2']],
            [true, [], 3, ['main1', 'main2']],
            [true, [], 3, ['main1', 'main2', 'main3']], 
            [true, [], 3, ['main1', 'main2', 'main4']], 
            [true, [], 3, ['main1', 'main3']],

            [true, [], 4, ['main1']],
            [true, ['main2'], 4, ['main1', 'main2']],
            [true, ['main3'], 4, ['main1', 'main3']],
            [true, [], 4, ['main1', 'main2', 'main3']],
            [true, [], 4, ['main2', 'main3']],
            [false, ['main2'], 4, ['main2']],
            [false, ['main3'], 4, ['main3']],

            [false, ['main1'], 5, ['main1']],
            [false, ['main2'], 5, ['main2']],
            [true, [], 5, ['main1', 'main2']],
            [true, ['main3'], 5, ['main1', 'main2', 'main3']], 
            [true, ['main4'], 5, ['main1', 'main2', 'main4']], 
            [false, ['main1', 'main3'], 5, ['main1', 'main3']],

            [false, ['main1'], 6, ['main1']],
            [false, ['main1', 'main2'], 6, ['main1', 'main2']],
            [true, [], 6, ['main1', 'main2', 'main4', 'main5']],
            [true, [], 6, ['main1', 'main2', 'main3', 'main4', 'main5']],
            [false, ['main3'], 6, ['main3']],
            [false, ['main3', 'main4'], 6, ['main3', 'main4']],
            [true, [], 6, ['main1', 'main2', 'main3', 'main4']],
            [true, [], 6, ['main1', 'main2', 'main3', 'main4', 'main5']],

            [false, ['main1'], 7, ['main1']],
            [true, [], 7, ['main1', 'main2']],
            [true, [], 7, ['main1', 'main2', 'main3']],
            [false, ['main1', 'main3'], 7, ['main1', 'main3']],
            [false, ['main2', 'main3'], 7, ['main2', 'main3']],
        ];
    }

    /** @dataProvider canHaveOptionProvider */
    public function test_can_have_option($expectation, $optionCoverages, $profileCoverages)
    {
        $result = $this->manager->canHaveOption($optionCoverages, $profileCoverages);

        $this->assertEquals($expectation, $result);
    }

    public function canHaveOptionProvider()
    {
        return [
            [true, [], [], ],
            [true, ['a'], [], ],
            [true, [], ['a'], ],
            [false, ['a'], ['b'], ],
            [true, ['a'], ['a'], ],
            [true, ['a'], ['a', 'b'], ],
            [true, ['a', 'b'], ['a', 'b'], ],
            [false, ['a', 'b'], ['c', 'b'], ],
            [false, ['a', 'b'], ['c'], ],
        ];
    }
}
