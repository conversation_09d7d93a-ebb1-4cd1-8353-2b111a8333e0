<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use App\Models\Profile;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageRank;

class StageRankTest extends TestCase
{
    /** @var StageRank */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(StageRank::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    //
    //
    // ========================================================
    // rank
    //

    /** @dataProvider rankProvider */
    public function test_rank($profileId, $productId)
    {
        $this->seed(ProductMapperSeeder::class);

        $result = new ProductMapperResult(
            //Product::all()
            collect([Product::find($productId)])
        );

        //dd($result->items[0]->getProduct());

        $this->manager->run(Profile::find($profileId), $result);
    }

    public function rankProvider()
    {
        return [
            // Profile, Product
            [ 1, 1 ],
            [ 2, 1 ],
            [ 3, 1 ],
            [ 5, 1 ],
            [ 6, 1 ],
        ];
    }

}