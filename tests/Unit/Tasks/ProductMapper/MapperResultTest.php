<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use TypeError;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;

class MapperResultTest extends TestCase
{
    public function setUp() : void
    {
        parent::setUp();

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    /** @dataProvider constructorProvider */
    public function test_constructor($expectation, $products)
    {
        if ($expectation === 'ex') {
            $this->expectException(TypeError::class);
        }

        $result = new ProductMapperResult(collect($products));

        $this->assertEquals($expectation, $result->products()->count());
    }

    public function constructorProvider() 
    {
        return [
            [0, []],
            [1, [new Product()]],
            ['ex', [1]],
        ]; 
    }

    /** @dataProvider logsProvider */
    public function test_logs($logs)
    {
        $result = new ProductMapperResult(collect([]));

        foreach ($logs as $log) {
            $result->log($log);
        }

        $this->assertEquals(count($logs), count($result->logs));
    }

    public function logsProvider() 
    {
        return [
            [[]],
            [['', 'log2']],
            [[null, 'log2']],
            [['log1', 'log2']],
        ]; 
    }
}