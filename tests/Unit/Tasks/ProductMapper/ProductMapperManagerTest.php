<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use App\Models\Profile;
use App\Models\User;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class ProductMapperManagerTest extends TestCase
{
    /** @var ProductMapperManager */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(ProductMapperManager::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    //
    //
    // ========================================================
    // general purpose feature tests
    //

    /** @dataProvider flowProvider */
    public function _test_flow($profileId, $productId)
    {
        $this->seed(ProductMapperSeeder::class);

        // $profile->client use RestrictedScope so we need an user.
        $this->actingAs(User::find(1));

        $result = $this->manager->map(Profile::find($profileId));

        dump($result);
    }

    public function flowProvider()
    {
        return [
            // Profile, Product
            [ 1, 1 ],
            [ 2, 1 ],
            [ 3, 1 ],
            [ 5, 1 ],
            [ 6, 1 ],

            [ 8, 8 ],
        ];
    }
}