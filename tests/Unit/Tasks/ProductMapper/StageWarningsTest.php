<?php

namespace Tests\Unit\Tasks\ProductMapper;

use App\Models\Product;
use App\Models\Profile;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageRank;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageWarnings;

class StageWarningsTest extends TestCase
{
    /** @var StageWarnings */
    protected $manager;

    public function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(StageWarnings::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }


    /** @dataProvider extractProvider */
    public function test_extractWarningsFromProfile($expectation, $profile, $warnings)
    {
        $result = $this->manager->extractWarningsFromProfile($profile, $warnings['profile']);

        $this->assertEquals(count($expectation), count($result));

        foreach($result as $warning) {
            $this->assertTrue(in_array($warning, $warnings['profile']));
        }
    }

    public function extractProvider()
    {
        return [
            // (mocked)profile, warnings
            [['lorem ipsum'], (new Profile())->forceFill(['property' => 'value']), ['profile' => ['property' => 'lorem ipsum']] ],
            
            [['lorem ipsum'], (new Profile())->forceFill(['property' => '1']), ['profile' => ['property' => 'lorem ipsum']] ],
            [['lorem ipsum'], (new Profile())->forceFill(['property' => 1]), ['profile' => ['property' => 'lorem ipsum']] ],
            [['lorem ipsum extremeSports'], (new Profile())->forceFill(['extremeSports' => 1]), ['profile' => ['extremeSports' => 'lorem ipsum extremeSports']] ],

            [[], (new Profile())->forceFill(['property' => 0]), ['profile' => ['property' => 'lorem ipsum']] ],
            [[], (new Profile())->forceFill(['property' => false]), ['profile' => ['property' => 'lorem ipsum']] ],
        ];
    }
}