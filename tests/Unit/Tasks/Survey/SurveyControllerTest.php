<?php namespace Tests\Unit\Tasks\ProductMapper;

use Illuminate\Support\Facades\Config;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Tasks\Survey\SurveyController;

class SurveyControllerTest extends TestCase
{
    /** @var SurveyController */
    protected $controller;

    public function setUp() : void
    {
        parent::setUp();

        $this->controller = app()->make(SurveyController::class);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    /** @dataProvider getRulesProvider */
    public function test_get_validation_rules($expectation, $tenant, $tenantConfig)
    {
        Config::set("easyprofile.tenants.default.tasks.survey.rules", "default");

        $this->assertEquals('default', $this->controller->getValidationRules($tenant));

        Config::set("easyprofile.tenants.{$tenant}.tasks.survey.rules", $tenantConfig);

        $this->assertEquals($expectation, $this->controller->getValidationRules($tenant));
    }

    public function getRulesProvider()
    {
        return [
            ['default', null, null],
            ['default', '', null],
            ['tenant-config-value', 'tenant-for-testing-purpose', 'tenant-config-value'],
        ];
    }

    //
    //
    // /////////////////////////////////////
    //
    //

    /** @dataProvider filterRulesProvider */
    public function test_filter_validation_rules($expectation, $rules, $filter)
    {
        $result = $this->controller->filterValidationRules($rules, $filter);

        $this->assertEquals(count($expectation), count($result));

        foreach($expectation as $key => $value) {
            $this->assertTrue(isset($result[$key]) && $value == $result[$key]);
        }
    }

    public function filterRulesProvider()
    {
        return [
            [[], [], []],
            [['foo' => 'bar'], ['foo' => 'bar'], ['foo']],
            [['foo' => 'bar'], ['foo' => 'bar', 'baz' => 'gaz'], ['foo']],
            [['foo' => 'bar', 'baz' => 'gaz'], ['foo' => 'bar', 'baz' => 'gaz'], []],
            [['baz' => 'gaz'], ['foo' => 'bar', 'baz' => 'gaz'], ['baz']],
            [['baz' => 'gaz'], ['foo' => 'bar', 'baz' => 'gaz'], ['baz', null]],
            [[], [], ['baz']],
        ];
    }
}