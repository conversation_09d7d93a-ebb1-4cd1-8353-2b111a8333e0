<?php

namespace Tests\Unit;

use App\Models\User;
use Illuminate\Support\Facades\Config;
use Mockery;
use Tests\TestCase;
use Upnovation\Easyprofile\Exceptions\QuotaException;
use Upnovation\Easyprofile\UserManager;

class UserManagerTest extends TestCase
{
    protected $manager;

    public function __construct() 
    {
        parent::__construct();

        $this->manager = new UserManager();
    }

    public function test_user_is_not_created_over_quota()
    {
        $manager = Mockery::mock(UserManager::class)->makePartial();

        Config::shouldReceive('get')
            ->once()
            ->with('easyprofile.tenants.tenant1.maxUsers')
            ->andReturn(1);

        $manager->shouldReceive('countUsers')->andReturn(2);

        $this->expectException(QuotaException::class);

        $manager->on('tenant1')->create(new User(), 'salesman');
    }

    public function tearDown() : void
    {
        Mockery::close();
    }
}
