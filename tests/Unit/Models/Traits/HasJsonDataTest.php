<?php namespace Tests\Unit\Pdf\Overlays;

use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class HasJsonDataTest extends TestCase
{
    public function addDataProvider()
    {
        return [
            [[], 'key', []],
            [['foo' => 'bar'], 'key', (object)['foo' => 'bar']],
            [0, 'key', 0],
            [null, 'key', null],
            ['foo bar', 'key', 'foo bar'],
        ];
    }

    /**
     * @dataProvider addDataProvider
     */    
    public function testAddData($expectation, $key, $value)   
    {
        $dummy = new \Tests\Unit\Models\Traits\DummyModel();
        $dummy->addData($key, $value);
        $this->assertEquals($expectation, $dummy->data[$key]);
    }

    public function addDataSequenceProvider()
    {
        return [
            [
                ['key' => ['bar' => 'baz']], 
                'key', (object)['foo' => 'bar'],
                'key', (object)['bar' => 'baz'],
            ],

            [
                ['key1' => ['foo' => 'bar'], 'key2' => ['bar' => 'baz']], 
                'key1', (object)['foo' => 'bar'],
                'key2', (object)['bar' => 'baz'],
            ],

        ];
    }

    /**
     * @dataProvider addDataSequenceProvider
     */    
    public function testAddSequenceData($expectation, $key1, $value1, $key2, $value2)   
    {
        $dummy = new \Tests\Unit\Models\Traits\DummyModel();
        $dummy->addData($key1, $value1);
        $dummy->addData($key2, $value2);
        $this->assertEquals($expectation, $dummy->data);
    }

    public function addEntryProvider()
    {
        return [
            [['key' => ['value']], 'key', 'value'],

            [
                ['key1' => ['value1'], 'key2' => ['value2']], 
                'key1', 'value1',
                'key2', 'value2',
            ],

            [
                ['key' => ['value1', 'value2']],
                'key', 'value1',
                'key', 'value2',
            ],

            [
                ['key' => ['samevalue', 'samevalue']],
                'key', 'samevalue',
                'key', 'samevalue',
            ],
        ];
    }

    /**
     * @dataProvider addEntryProvider
     */    
    public function testAddEntry($expectation, $key1, $value1, $key2 = null, $value2 = null)   
    {
        $dummy = new \Tests\Unit\Models\Traits\DummyModel();
        
        if ($key2 !== null) {
            $dummy->addEntry($key1, $value1);
            $dummy->addEntry($key2, $value2);
            $this->assertEquals($expectation, $dummy->data);
            return;
        }

        $dummy->addEntry($key1, $value1);
        $this->assertEquals($expectation, $dummy->data);
    }

    public function addIndexedEntryProvider()
    {
        return [
            [
                ['key' => ['index1' => 'value1']], 
                'key', 'index1', 'value1',
            ],
            
            [
                ['key' => ['index1' => 'value1', 'index2' => 'value2']], 
                'key', 'index1', 'value1', 'index2', 'value2',
            ],

            [
                ['key' => ['index1' => 'value2',]], 
                'key', 'index1', 'value1', 'index1', 'value2',
            ],

        ];
    }

    /**
     * @dataProvider addIndexedEntryProvider
     */    
    public function testIndexedAddEntry($expectation, $key, $index1, $value1, $index2 = null, $value2 = null)   
    {
        $dummy = new \Tests\Unit\Models\Traits\DummyModel();
        
        if ($index2 !== null) {
            $dummy->addEntry($key, $value1, $index1);
            $dummy->addEntry($key, $value2, $index2);
            $this->assertEquals($expectation, $dummy->data);
            return;
        }

        $dummy->addEntry($key, $value1, $index1);
        $this->assertEquals($expectation, $dummy->data);
    }
}