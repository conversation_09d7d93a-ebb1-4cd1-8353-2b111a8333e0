<?php namespace Tests\Unit\Models;

use App\Models\NetworkNode;
use Illuminate\Database\Eloquent\Model;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;

class NetworkNodeTest extends TestCase
{
    public function testNetworkNodeCreation()
    {
        // I just don't want changes to the __toString method to go under the radar.
        $node = new NetworkNode();
        $node->name = 'Test Node';
        $this->assertEquals('Test Node', (string)$node);
    }
}
