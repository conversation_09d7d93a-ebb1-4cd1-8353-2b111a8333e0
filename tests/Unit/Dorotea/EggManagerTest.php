<?php

namespace Tests\Unit\Dorotea;

use App\Models\Form;
use App\Models\MapperResult;
use App\Models\Pipeline;
use Exception;
use Illuminate\Support\Facades\Log;
use Mockery;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Dorotea\EggManager;

class EggManagerTest extends TestCase
{
    /** @var EggManager */
    protected $manager;
    
    protected function setUp() : void
    {
        parent::setUp();

        $this->manager = app()->make(EggManager::class);

        Tenant::whereName('dorotea')->first()->makeCurrent();
    }
    
    //
    //
    // Test auth.
    //
    //

    public function test_auth_returns_null_when_egg_fails()
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('getUser')->andThrow(Exception::class);
        Log::shouldReceive('info');
        
        $this->assertNull($egg->auth("foo", "bar"));
    }

    /**
     * @dataProvider eggUserNullProvider
     */
    public function test_auth_returns_null_when_egg_returns_null($userData)
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('getUser')->andReturn($userData);
        Log::shouldReceive('debug');
        
        $this->assertNull($egg->auth("foo", "bar"));
    }

    public function eggUserNullProvider()
    {
        return [
            [null],
            [[]],
        ];
    }

    /**
     * @dataProvider authProvider
     */
    public function test_auth($expectation, $token, $userData)
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('getUser')->andReturn($userData);
        Log::shouldReceive('debug');
        
        $this->assertEquals($expectation, $egg->auth("foo", $token));
    }

    public function authProvider()
    {
        $validUser = (object)['session_token' => 'valid_token',];

        return [
            [null, "foo", (object)['session_token' => '',]],
            [null, "foo", (object)['session_token' => null,]],
            [null, "foo", (object)['session_token' => 0,]],
            [null, "token", (object)['session_token' => 'invalid',]],
            [$validUser, "valid_token", $validUser],
        ];
    }

    //
    //
    // Test getUser.
    //
    //

    public function test_getUser_returns_null_if_token_fails()
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('getToken')->andThrow(Exception::class);
        Log::shouldReceive('info');
        Log::shouldReceive('debug');

        $this->assertNull($egg->getUser('foo'));
    }

    /**
     * @dataProvider eggUserProvider
     */
    public function test_getUser($expectation, $user)
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('getToken')->andReturn((object)[
            'access_token' => 'foo',
        ]);

        $egg->shouldReceive('call')->andReturn($user);

        Log::shouldReceive('debug');

        $this->assertEquals($expectation, $egg->getUser('foo'));
    }

    public function eggUserProvider()
    {
        $validResponse = (object)[
            'session_token' => 'foo',
            'utenteID' => 'bar',
            'nome' => 'bar',
            'cognome' => 'baz',
            'email1' => '<EMAIL>',
        ];

        return [
            [null, null],
            [null, []],
            [null, (object)['foo' => 'bar']],
            [null, (object)['result' => null]],
            [null, (object)['result' => '']],
            [null, (object)['result' => []]],
            [null, (object)['result' => (object)['session_token' => 'foobar',]]],
            [$validResponse, (object)['result' => $validResponse]],
        ];
    }

    //
    //
    // Test params.
    //
    //

    /**
     * @dataProvider paramsProvider
     */
    public function test_params($expectation, $input)
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $this->assertEquals($expectation, $egg->params($input));
    }

    public function paramsProvider()
    {
        $validUser = (object)['session_token' => 'valid_token',];

        return [
            [null, null],
            [null, []],
            [null, 0],
            ["foo=bar", ["foo" => "bar"]],
            ["foo=bar&bar=baz", ["foo" => "bar", "bar" => "baz"]],
        ];
    }

    public function test_params_fails_for_invalid_params()
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $egg->shouldReceive('checkParam')->once()->andReturnFalse();

        try {
            $egg->params(['foo' => 'bar']);

            $this->fail('Exception was not thrown.');
        } catch (Exception $ex) {
            // Ok fine.
        }

        $this->assertTrue(true);
    }

    /**
     * @dataProvider checkParamsProvider
     */
    public function test_checkParams($expectation, $input)
    {
        $egg = Mockery::mock(EggManager::class)->makePartial();

        $this->assertEquals($expectation, $egg->checkParam($input));
    }

    public function checkParamsProvider()
    {
        return [
            [false, null],
            [false, 0],
            [false, "0"],
            [false, "0 "],
            [false, " "],
            [false, "&"],
            [false, "="],
            [false, "'"],
            [true, "foo!"],
            [false, "foo&"],
            [false, "foo="],
            [true, "foo"],
            [true, "foo "],
            [true, "foo  "],
            [true, " foo  "],
            [true, " foo"],
        ];
    }

    //
    //
    //  Redirect
    //
    //

    /** @dataProvider redirectProvider */
    public function test_redirect_url($expectation, $id)
    {
        $pipeline = new Pipeline();
        $pipeline->egg_opportunity_id = $id;

        if ($expectation == 'exception') {
            $this->expectException(Exception::class);
        }

        $url = $this->manager->getRedirectUrl($pipeline);

        preg_match("/opzione=([a-zA-Z0-9]+)/", $url, $matches);
        
        $this->assertEquals(count($expectation), count($matches) - 1);
        $this->assertEquals($expectation[0], $matches[1]);
    }

    public function redirectProvider()
    {
        return [
            ['exception', null],
            ['exception', ''],
            ['exception', 0],
            ['exception', '0'],
            ['exception', "="],
            ['exception', "?"],
            ['exception', "/"],
            ['exception', "&"],
            
            [[1], 1],
            [[11], 11],
            [['a'], 'a'],
            [['a'], 'a'],
            [['1'], '1'],
        ];
    }

    //
    //
    //  Update opportunity
    //
    //

    /** @dataProvider formatOpportunityProvider */
    public function test_format_opportunity_data($expectation, $resultIds, $confirmed, $formJson)
    {
        $this->seed(EggManagerSeeder::class);

        $results = MapperResult::whereIn('id', $resultIds)->get();

        if ($expectation === 'exception') {
            $this->expectException(Exception::class);
        }

        $data = $this->manager->formatOpportunityData(
            $results, 
            new Form(['cachedResult' => $formJson])
        );

        foreach ($results as $result) {
            $this->assertEquals(
                $expectation[$result->product->eggProduct->field],
                $data[$result->product->eggProduct->field]
            );
        }

        $this->assertEquals($confirmed, $data['c__prodrichie']);
    }

    public function formatOpportunityProvider()
    {
        return [
            ['exception', [], null, []],
            ['exception', [1], null, []],
            [
                'exception', 
                [2,6], 
                null,
                []
            ],
            [
                ['c__cfincendio' => 'SI',], 
                [2], 
                101,
                []
            ],
            [
                ['c__cfincendio' => 'SI', 'c__cfrataprot' => 'SI', 'c__cf6copeasy' => 'SI', 'c__cfnicelife' => 'SI'], 
                [2,3,4,5], 
                101,
                []
            ],
            [
                ['c__cfnicelife' => 'SI', 'c__cfprotmax' => 'SI'], 
                [5,6], 
                105,
                []
            ],
        ];
    }

    public function tearDown() : void
    {
        Mockery::close();
    }
}
