<?php

namespace Tests\Unit\Dorotea;

use App\Models\User;
use Illuminate\Database\Connection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Mockery;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Upnovation\Easyprofile\Dorotea\EggManager;
use Upnovation\Easyprofile\Dorotea\UserManager;
use Upnovation\Easyprofile\UserManager as EasyprofileUserManager;

class UserManagerTest extends TestCase
{
    protected $manager;
    
    protected $egg;
    protected $baseUserManager;

    public function setUp() : void
    {
        parent::setUp();

        $this->egg = Mockery::mock(EggManager::class);
        $this->baseUserManager = Mockery::mock(EasyprofileUserManager::class);

        $this->manager = new UserManager(
            $this->egg,
            $this->baseUserManager
        );

        Tenant::whereName('dorotea')->first()->makeCurrent();
    }

    //
    //
    // Test auth.
    //
    //

    public function test_auth_fails_if_egg_doesnt_return()
    {
        $this->egg->shouldReceive('auth')->andReturnNull();
        
        $this->assertNull($this->manager->auth('foo', 'bar'));
    }

    public function test_auth_fails_if_user_not_retrieved()
    {
        $manager = Mockery::mock(UserManager::class, [$this->egg, $this->baseUserManager])->makePartial();

        $this->egg->shouldReceive('auth')->andReturn((object)[]);

        $manager->shouldReceive('getUser')->andReturnNull();
        
        $this->assertNull($manager->auth('foo', 'bar'));
    }

    public function test_auth_succeed_if_user_is_retrieved()
    {
        $manager = Mockery::mock(UserManager::class, [$this->egg, $this->baseUserManager])->makePartial();

        $this->egg->shouldReceive('auth')->andReturn((object)[]);

        $user = new User();
        $manager->shouldReceive('getUser')->andReturn($user);
        $manager->shouldReceive('login')->andReturn($user);
        $this->assertEquals($user, $manager->auth('foo', 'bar'));
    }

    //
    //
    // Test createUserFromEgg.
    //
    //

    public function test_user_is_not_created_if_not_configured_so()
    {
        app()->make(EggManagerSeeder::class)->init();

        $manager = app()->make(UserManager::class);

        Config::set('easyprofile.tenants.dorotea.createUserOnLogin', false);
        $this->assertNull($manager->getUser((object)[ 'utenteID' => 1, ]));

        Config::set('easyprofile.tenants.dorotea.createUserOnLogin', true);
        $this->assertNotNull($manager->getUser((object)[
            'utenteID' => 1,
            'nome' => 1,
            'cognome' => 1,
            'email1' => 1,
        ]));
    }

    public function test_user_is_created()
    {
        $manager = Mockery::mock(
            UserManager::class, [
                $this->egg, 
                $this->baseUserManager
         ])->makePartial();

        $connection = Mockery::mock(Connection::class);

        DB::shouldReceive('connection')
            ->andReturn($connection);

        $user = new User();

        $this->baseUserManager
            ->shouldReceive('on')
            ->andReturn($this->baseUserManager)
            ->shouldReceive('create')
            ->andReturn($user);

        $connection->shouldReceive('beginTransaction');
        $connection->shouldReceive('commit');

        Hash::shouldReceive('make');
        Log::shouldReceive('info');

        $this->assertEquals($user, $manager->createUserFromEgg(
            (object)[
                'utenteID' => 1,
                'nome' => 1,
                'cognome' => 1,
                'email1' => 1,
            ]
        ));
    }

    //
    //
    //  User sync
    //
    //

    /** @dataProvider activateUsersProvider */
    public function test_activate_users($expectation, $eggUsers)
    {
        app()->make(EggManagerSeeder::class)->init();

        $manager = app()->make(UserManager::class);

        $this->assertEquals($expectation, $manager->activateUsers($eggUsers));
    }

    function activateUsersProvider()
    {
        return [
            [
                1,
                [
                    (object)[
                        'utenteID' => 1,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 1,
                    ],
                ],
            ],
            [
                2,
                [
                    (object)[
                        'utenteID' => 1,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 1,
                    ],
                    (object)[
                        'utenteID' => 2,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 2,
                    ],
                ],
            ],
            // Same email
            [
                1,
                [
                    (object)[
                        'utenteID' => 1,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 1,
                    ],
                    (object)[
                        'utenteID' => 2,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 1,
                    ],
                ],
            ],
            // Same user ID => user will be updated at second pass.
            [
                1,
                [
                    (object)[
                        'utenteID' => 111,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 1,
                    ],
                    (object)[
                        'utenteID' => 111,
                        'nome' => 1,
                        'cognome' => 1,
                        'email1' => 2,
                    ],
                ],
            ],
        ];
    }

    public function tearDown() : void
    {
        Mockery::close();
    }
}
