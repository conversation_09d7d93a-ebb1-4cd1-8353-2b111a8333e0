<?php namespace Tests\Unit\Dorotea;

use App\Models\Company;
use App\Models\CoverageCategory;
use Database\Factories\CoverageFactory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;

class EggManagerSeeder extends Seeder
{
    public function run()
    {
        //
        // Must run on dorotea tenant.
        //

        $this->init();

        DB::connection('tenant')->table('companies')->insert([
            ['id' => 1, 'name' => 'Groupama', 'logo' => '',],
            ['id' => 2, 'name' => 'CF Assicurazioni', 'logo' => '',],
            ['id' => 3, 'name' => 'Net Insurance', 'logo' => '',],
            ['id' => 4, 'name' => 'Axa Assicurazioni', 'logo' => '',],
            ['id' => 5, 'name' => 'Afi Esca', 'logo' => '',],
        ]);

        DB::connection('tenant')->table('products')->insert([
            ['id' => 1, 'company_id' => 1, 'code' => 'code1', 'name' => 'p1', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 2, 'company_id' => 1, 'code' => 'code2', 'name' => 'p2', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 3, 'company_id' => 1, 'code' => 'code3', 'name' => 'p3', 'enabled' => false, 'minLen' => null, 'maxLen' => null,],
            ['id' => 4, 'company_id' => 2, 'code' => 'code4', 'name' => 'p4', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 5, 'company_id' => 2, 'code' => 'code5', 'name' => 'p5', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 6, 'company_id' => 2, 'code' => 'code6', 'name' => 'p6', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 7, 'company_id' => 2, 'code' => 'code7', 'name' => 'p7', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
        ]);

        DB::connection('tenant')->table('egg_products')->insert([
            ['id' => 100, 'product_id' => 1, 'field' => 'c__yyy'],
            ['id' => 101, 'product_id' => 2, 'field' => 'c__cfincendio'],
            //['id' => 284, 'product_id'=> 1, 'field' => 'c__cfincuca2'],
            ['id' => 102, 'product_id' => 3, 'field' => 'c__cfrataprot'],
            ['id' => 103, 'product_id' => 4, 'field' => 'c__cf6copeasy'],
            ['id' => 104, 'product_id' => 5, 'field' => 'c__cfnicelife'],
            ['id' => 105, 'product_id' => 6, 'field' => 'c__cfprotmax'],
            ['id' => 106, 'product_id' => 7, 'field' => 'c__cfvitaacol'],
        ]);

        DB::connection('tenant')->table('users')->insert([
            ['id'=> 1, 'egg_id' => 1, 'name' => 1, 'lastname' => 1, 'email' => 1, 'password' => 1, 'active' => 1,],
        ]);

        DB::connection('tenant')->table('clients')->insert([
            ['id'=> 1, 'name' => 'client name', 'egg_client_id' => 1, ],
        ]);

        DB::connection('tenant')->table('pipelines')->insert([
            ['id'=> 1, 'user_id' => 1, 'state' => 'open', 'egg_opportunity_id' => 1, ],
        ]);

        DB::connection('tenant')->table('profiles')->insert([
            ['id'=> 1, 'pipeline_id' => 1, 'job' => 'job1', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
        ]);

        DB::connection('tenant')->table('mapper_results')->insert([
            ['id' => 1, 'profile_id' => 1, 'product_id'=> 1, 'confirmed' => 0],
            ['id' => 2, 'profile_id' => 1, 'product_id'=> 2, 'confirmed' => 1],
            ['id' => 3, 'profile_id' => 1, 'product_id'=> 3, 'confirmed' => 0],
            ['id' => 4, 'profile_id' => 1, 'product_id'=> 4, 'confirmed' => 0],
            ['id' => 5, 'profile_id' => 1, 'product_id'=> 5, 'confirmed' => 0],
            ['id' => 6, 'profile_id' => 1, 'product_id'=> 6, 'confirmed' => 1],
        ]);
    }

    public function init() 
    {
        DB::connection('tenant')->table('coverage_product')->delete();
        DB::connection('tenant')->table('coverage_profile')->delete();
        DB::connection('tenant')->table('profiles')->delete();
        DB::connection('tenant')->table('pipelines')->delete();
        DB::connection('tenant')->table('clients')->delete();
        DB::connection('tenant')->table('coverages')->delete();
        DB::connection('tenant')->table('products')->delete();
        DB::connection('tenant')->table('mapper_results')->delete();
        DB::connection('tenant')->table('egg_products')->delete();
        DB::connection('tenant')->table('users')->delete();
        DB::connection('tenant')->table('companies')->delete();
    }
}