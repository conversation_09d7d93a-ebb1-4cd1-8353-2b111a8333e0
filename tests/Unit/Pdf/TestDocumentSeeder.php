<?php namespace Tests\Unit\Pdf;

use App\Models\Address;
use App\Models\Client;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Role;
use App\Models\User;
use Database\Factories\RoleFactory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

class TestDocumentSeeder extends Seeder
{
    public function run()
    {
        DB::connection('tenant')->table('addresses')->delete();
        DB::connection('tenant')->table('gi_comuni_cap')->delete();
        DB::connection('tenant')->table('files')->delete();
        DB::connection('tenant')->table('documents')->delete();
        DB::connection('tenant')->table('clients')->delete();
        DB::connection('tenant')->table('pipelines')->delete();
        DB::connection('tenant')->table('enterprises')->delete();
        DB::connection('tenant')->table('people')->delete();
        DB::connection('tenant')->table('users')->delete();
        DB::connection('tenant')->table('roles')->delete();

        $document = new \App\Models\Document();
        $document->id = 1;
        $document->node_id = null;
        $document->title = 'Test doc';
        $document->type = 'privacy';
        $document->version = '1.0.0';
        $document->description = "foo bar";
        $document->processor = PdfProcessor::class;
        $document->save();

        $file = new File();
        $file->id = 1;
        $file->document_id = $document->id;
        $file->configName = 'test';
        $file->disk = 'tests';
        $file->path = null;
        $file->filename = 'dummy.pdf';
        $file->save();

        Role::factory()->create([
            'id' => 1,
            'name' => 'manager',
        ]);

        $user = User::factory()->create([
            'id' => 1,
        ]);

        $user->roles()->attach(1);

        Person::factory()->create([
            'id' => 1,
            'name' => 'Test Person',
            'lastname' => 'Test Lastname',
        ]);

        $a = Address::factory()->create([
            'id' => 1,
            'type' => 'residence',
            'person_id' => 1,
            'street' => 'Test Street',
        ]);

        Person::factory()->create([
            'id' => 2,
            'name' => 'Test Rep',
            'lastname' => 'Test Rep',
        ]);

        Enterprise::factory()->create([
            'id' => 1,
            'rep_id' => 2,
        ]);

        Address::factory()->create([
            'id' => 2,
            'type' => 'headquarters',
            'enterprise_id' => 1,
            'street' => 'Test Street',
        ]);

        $this->seedContext();
    }

    public function seedContext()
    {
        // Pipeline 1: individual
        Pipeline::factory()->createMany([
            ['id' => 1, 'user_id' => 1, 'type' => 'individual'],
        ]);

        Client::factory()->create([
            'pipeline_id' => 1,
            'person_id' => 1,
            'name' => 'Test Client',
            'role' => 'contractor',
        ]);

        // Pipeline 2: legal
        Pipeline::factory()->createMany([
            ['id' => 2, 'user_id' => 1, 'type' => 'legal'],
        ]);

        Client::factory()->create([
            'pipeline_id' => 2,
            'enterprise_id' => 1,
            'name' => 'Test Client',
        ]);

        // Pipeline 3: without contractor
        Pipeline::factory()->createMany([
            ['id' => 3, 'user_id' => 1, 'type' => 'legal'],
        ]);
    }

    public function makePipeline()
    {
        $pipeline = Pipeline::factory()->createMany([
            ['user_id' => 1, 'type' => 'individual'],
        ]);

        Client::factory()->create([
            'pipeline_id' => $pipeline[0]->id,
            'person_id' => Person::factory()->create([
                'name' => 'TestDocumentSeeder Test Person',
                'lastname' => 'TestDocumentSeeder Test Lastname',
            ])->id,
            'name' => 'Test Client',
            'role' => 'contractor',
        ]);

        return $pipeline[0];
    }
}