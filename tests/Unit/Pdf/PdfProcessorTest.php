<?php namespace Tests\Unit\Pdf;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class PdfProcessorTest extends TestCase
{
    protected PdfProcessor $pdfProcessor;

    protected File $outfile;

    public function setUp() : void
    {
        parent::setUp();

        $this->pdfProcessor = new PdfProcessor();

        $this->outfile = new File([
            'disk' => 'tests',
            'path' => null,
            'filename' => /*date('Ymd-His') . */'test_output.pdf',
            'task_id' => null,
            'document_id' => null,
        ]);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    public function testFoo()
    {
        $this->seed(TestDocumentSeeder::class);

        $this->actingAs(User::find(1));

        $document = Document::withoutGlobalScopes()->find(1);
        $pipeline = Pipeline::withoutGlobalScopes()->find(2);

        $document->overlayArray = [
            'name' => new TextOverlay(1, 30, 50, [], "foo bar baz"),
            new UserOverlay(1, 30, 90, [
                'properties' => ['name', 'lastname'],
                'separator' => ' - ',
            ]),
            new SubjectOverlay(1, 30, 130, [
                'role' => 'contractor',
                'properties' => ['name'],
                'separator' => ' - ',
            ]),
            new SubjectOverlay(1, 30, 150, [
                'role' => 'rep',
                'properties' => ['name', 'lastname'],
                'separator' => ' - ',
            ]),
            new SubjectOverlay(1, 30, 170, [
                'role' => 'contractor',
                'method' => 'getSubjectAddress',
                'separator' => ' - ',
            ]),
            new AddressOverlay(1, 30, 190, [
                'properties' => ['street',],
                'type' => 'headquarters',
                'role' => 'contractor',
            ]),
        ];

        $document->save();
        /*$resolver = new OverlayResolver();

        $document->overlays = $resolver->resolve($document->getOverlayArray(), $pipeline);*/

        $this->pdfProcessor->compile(
            $document,
            $pipeline,
            $this->outfile
        );
 
    }

    public function tearDown() : void
    {
        
    }
}