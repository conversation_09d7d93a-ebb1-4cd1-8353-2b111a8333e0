<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Document;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Exception;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class SubjectOverlayTest extends TestCase
{
    protected PdfProcessor $pdfProcessor;

    protected File $outfile;

    public function setUp() : void
    {
        parent::setUp();
    }

    public function constructorProvider()
    {
        return [
            [Exception::class, []],
            [Exception::class, ['role' => '']],
            [Exception::class, ['role' => ' ']],
            [Exception::class, ['conditionalRole' => []]],
            [null, ['role' => 'role', 'properties' => ['name']]],
            [null, ['conditionalRole' => ['foo'], 'properties' => ['name']]],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        new SubjectOverlay(1, 30, 90, $settings);

        // Suppress warnings.
        $this->assertTrue(true);
    }

    public function resolveRoleProvider()
    {
        return [
            [Exception::class,  null,               1, ['role' => 'doesntexist', 'properties' => ['placeholder just to make the constructor work']]],
            [1,                 Person::class,      1, ['role' => 'contractor', 'properties' => ['placeholder just to make the constructor work']]],
            [1,                 Enterprise::class,  2, ['role' => 'contractor', 'properties' => ['placeholder just to make the constructor work']]],

            // Individual pipeline: there's no rep
            [Exception::class,  null,               1, ['role' => 'rep', 'properties' => ['placeholder just to make the constructor work']]],
            // Pipeline without contractor
            [Exception::class,  null,               3, ['role' => 'rep', 'properties' => ['placeholder just to make the constructor work']]],

            [2,                 Person::class,      2, ['role' => 'rep', 'properties' => ['placeholder just to make the constructor work']]],
        ];
    }

    /**
     * @dataProvider resolveRoleProvider
     */
    public function testResolveRole($expectation, $expectationClass, $pipelineId, $settings)
    {
        $this->seed(TestDocumentSeeder::class);

        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        $this->actingAs(User::find(1));

        $pipeline = Pipeline::withoutGlobalScopes()->find($pipelineId);

        $overlay = new SubjectOverlay(1, 30, 90, $settings);

        $subject = $overlay->resolveRole($pipeline);

        $this->assertEquals($expectationClass, get_class($subject));
        $this->assertEquals($expectation, $subject->id);
    }

    public function conditionalResolveProvider()
    {
        return [
            // No field setting
            [Exception::class, null,      1, ['conditionalRole' => [
                'options' => ['individual' => 'contractor', 'legal' => 'rep'],
            ], 'properties' => ['placeholder just to make the constructor work']]], 
            
            // No options setting
            [Exception::class, null,      1, ['conditionalRole' => [
                'field' => 'type',
            ], 'properties' => ['placeholder just to make the constructor work']]],
            
            // Bad field setting
            [Exception::class, null,      1, ['conditionalRole' => [
                'field' => 'doesntexist',
                'options' => ['individual' => 'contractor', 'legal' => 'rep'],
            ], 'properties' => ['placeholder just to make the constructor work']]],
            
            // Pipeline 1: individual => person contractor
            [1, Person::class,      1, ['conditionalRole' => [
                'field' => 'type',
                'options' => ['individual' => 'contractor', 'legal' => 'rep'],
            ], 'properties' => ['placeholder just to make the constructor work']]],

            // Pipeline 2: legal => enterprise rep
            [2, Person::class,  2, ['conditionalRole' => [
                'field' => 'type',
                'options' => ['individual' => 'contractor', 'legal' => 'rep'],
            ], 'properties' => ['placeholder just to make the constructor work']]],

            // Pipeline 2: legal => enterprise contractor
            [1, Enterprise::class,      2, ['conditionalRole' => [
                'field' => 'type',
                'options' => ['individual' => 'contractor', 'legal' => 'contractor'],
            ], 'properties' => ['placeholder just to make the constructor work']]],
        ];
    }

    /**
     * @dataProvider conditionalResolveProvider
     */
    public function testResolveConditional($expectation, $expectationClass, $pipelineId, $settings)
    {
        $this->seed(TestDocumentSeeder::class);

        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        $this->actingAs(User::find(1));

        $pipeline = Pipeline::withoutGlobalScopes()->find($pipelineId);

        $overlay = new SubjectOverlay(1, 30, 90, $settings);

        $subject = $overlay->resolve($pipeline);

        $this->assertEquals($expectationClass, get_class($subject));
        $this->assertEquals($expectation, $subject->id);
    }

    public function testValueIsSetNullWhenConditionIsNull()
    {
        $this->seed(TestDocumentSeeder::class);

        $this->actingAs(User::find(1));

        $pipeline = Pipeline::withoutGlobalScopes()->find(1);

        $overlay = new SubjectOverlay(1, 30, 90, [
            'conditionalRole' => [
                'field' => 'type',
                'options' => ['individual' => null, 'legal' => 'contractor'],
            ],
            'properties' => ['placeholder just to make the constructor work'],
        ]);

        $overlay->value = 'some value';

        $overlay->resolve($pipeline);

        $this->assertNull($overlay->value);
    }
}