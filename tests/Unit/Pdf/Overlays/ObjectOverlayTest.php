<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Exception;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class ObjectOverlayTest extends TestCase
{
    protected PdfProcessor $pdfProcessor;

    public function setUp() : void
    {
        parent::setUp();
    }

    public function resolveProvider()
    {
        return [
            [Exception::class, []],
            [Exception::class, ['properties' => []]],
            [Exception::class, ['method' => []]],
            [Exception::class, ['properties' => [], 'method' => []]],
            [null, ['properties' => ['foo' => 'bar'], 'method' => []]],
            [null, ['properties' => [], 'method' => ['foo' => 'bar']]],
            [null, ['properties' => ['foo' => 'bar'], 'method' => ['foo' => 'bar']]],
        ];
    }

    /**
     * @dataProvider resolveProvider
     */
    public function testConstructor($expectation, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        new FooOverlay(1, 30, 90, $settings);
        
        // Suppress warnings.
        $this->assertTrue(true);
    }

    public function getValueProvider()
    {
        return [
            [null, null, ['method' => 'doesnt exist']],
            [Exception::class, new FooSubject(), ['method' => 'doesnt exist']],
            [null, new FooSubject(), ['method' => 'method']],
            [Exception::class, new FooSubject(), ['properties' => 'doesnt exist']],
            [null, new FooSubject(), ['properties' => ['p1']]],
        ];
    }

    /**
     * @dataProvider getValueProvider
     */
    public function testGetValue($expectation, $subject, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        $overlay = new FooOverlay(1, 30, 90, $settings);

        $overlay->value = $subject;

        $overlay->getValue();

        // Suppress warnings.
        $this->assertTrue(true);
    }

    public function getValueProviderMethod()
    {
        return [
            ['foo', new FooSubject(), ['method' => 'method']],
        ];
    }

    /**
     * @dataProvider getValueProviderMethod
     */
    public function testGetValueWithMethod($expectation, $subject, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        $overlay = new FooOverlay(1, 30, 90, $settings);

        $overlay->value = $subject;

        $this->assertEquals($expectation, $overlay->getValue());
    }

    public function getValueProviderProperties()
    {
        return [
            ['p1', new FooSubject(), ['properties' => ['p1']]],
            ['p2', new FooSubject(), ['properties' => ['p2']]],
            ['p1 p2', new FooSubject(), ['properties' => ['p1', 'p2']]],
            ['p1p2', new FooSubject(), ['properties' => ['p1', 'p2'], 'separator' => '']],
            ['p1/p2', new FooSubject(), ['properties' => ['p1', 'p2'], 'separator' => '/']],
        ];
    }
    
    /**
     * @dataProvider getValueProviderProperties
     */
    public function testGetValueWithProperties($expectation, $subject, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        $overlay = new FooOverlay(1, 30, 90, $settings);

        $overlay->value = $subject;

        $this->assertEquals($expectation, $overlay->getValue());
    }

    public function tearDown() : void
    {
        
    }
}