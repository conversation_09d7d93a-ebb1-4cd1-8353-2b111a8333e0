<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class UserOverlayTest extends TestCase
{
    protected PdfProcessor $pdfProcessor;

    protected File $outfile;

    public function setUp() : void
    {
        parent::setUp();

        $this->pdfProcessor = new PdfProcessor();

        $this->outfile = new File([
            'disk' => 'tests',
            'path' => null,
            'filename' => /*date('Ymd-His') . */'test_output.pdf',
            'task_id' => null,
            'document_id' => null,
        ]);

        Tenant::whereName('tenant1')->first()->makeCurrent();
    }

    public function resolveProvider()
    {
        return [
            [1, 1, []]
        ];
    }

    /**
     * @dataProvider resolveProvider
     */
    public function testResolve($expectation, $pipelineId, $settings)
    {
        $this->seed(TestDocumentSeeder::class);

        $pipeline = Pipeline::withoutGlobalScopes()->find($pipelineId);

        // Constructor is tested in ObjectOverlayTest.
        $overlay = new UserOverlay(1, 30, 90, [
            'properties' => ['name', 'lastname'],
        ]);

        $user = $overlay->resolve($pipeline);

        $this->assertEquals($expectation, $user->id);
    }

    public function tearDown() : void
    {
        
    }
}