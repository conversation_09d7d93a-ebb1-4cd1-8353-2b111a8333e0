<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Address;
use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Exception;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class AddressOverlayTest extends TestCase
{
    public function setUp() : void
    {
        parent::setUp();
    }

    public function constructorProvider()
    {
        return [
            [null, 1, ['properties' => ['name', 'lastname'],                            'role' => 'role already tested in subject overlay']],
            [null, 1, ['properties' => ['name', 'lastname'], 'type' => 'something',     'role' => 'role already tested in subject overlay']],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $pipelineId, $settings)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        new AddressOverlay(1, 30, 90, $settings);

        $this->assertTrue(true);
    }

    public function resolveProvider()
    {
        return [
            [1, 1, ['properties' => ['name', 'lastname'], 'type' => 'residence',    'role' => 'contractor']],
            [1, 2, ['properties' => ['name', 'lastname'], 'type' => 'headquarters',    'role' => 'contractor']],
            [null, 1, ['properties' => ['name', 'lastname'], 'type' => 'doesntexist',    'role' => 'contractor']],
            [null, 2, ['properties' => ['name', 'lastname'], 'type' => 'doesntexist',    'role' => 'contractor']],
        ];
    }

    /**
     * @dataProvider resolveProvider
     */
    public function testResolve($expectation, $pipelineId, $settings)
    {
        $this->seed(TestDocumentSeeder::class);

        $this->actingAs(User::find(1));

        $pipeline = Pipeline::withoutGlobalScopes()->find($pipelineId);

        if (class_exists($expectation)) {
            $this->expectException($expectation);
        } 

        // Constructor is tested in ObjectOverlayTest.
        $overlay = new AddressOverlay(1, 30, 90, $settings);

        $address = $overlay->resolve($pipeline);

        if (is_null($expectation)) {
            $this->assertNull($address);
        } else {
            $this->assertEquals(Address::class, get_class($address));
        }
    }

    public function tearDown() : void
    {
        
    }
}