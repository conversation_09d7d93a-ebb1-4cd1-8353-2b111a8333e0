<?php namespace Tests\Unit\Pdf\Overlays;

use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\User;
use Exception;
use Spatie\Multitenancy\Models\Tenant;
use Tests\TestCase;
use Tests\Unit\Pdf\TestDocumentSeeder;
use TypeError;
use Upnovation\Easyprofile\Pdf\Overlays\ObjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\OverlayResolver;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class RadioOverlayTest extends TestCase
{

    public function constructorProvider()
    {
        return [
            [Exception::class,  [],                  []],
            [Exception::class,  ['foo'],             []],
            [Exception::class,  ['options' => ''],    []],
            [Exception::class,  ['options' => []],    []],
        ];
    }

    /**
     * @dataProvider constructorProvider
     */
    public function testConstructor($expectation, $settings, $data)   
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new RadioOverlay(1, 30, 90, $settings);

        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
    }

    public function getValueProvider()
    {
        return [
            [
                Exception::class,    
                [],  
                [
                    'options' => [
                        'm' => new TextOverlay(1, 30, 50, [], "foo"),
                        'f' => new TextOverlay(1, 30, 50, [], "bar"),
                    ], 
                    'key' => 'doesnt exist'
                ], 
                ['sex' => 'm']
            ],
            [
                'male',    
                ['page' => 1, 'x' => 2, 'y' => 2],
                [
                    'options' => [
                        'm' => new TextOverlay(1, 2, 2, [], "male"),
                        'f' => new TextOverlay(2, 3, 3, [], "female"),
                    ], 
                    'key' => 'sex'
                ], 
                ['sex' => 'm']
            ],
            [
                'female',    
                ['page' => 2, 'x' => 5, 'y' => 5],  
                [
                    'options' => [
                        'm' => new TextOverlay(1, 4, 4, [], "male"),
                        'f' => new TextOverlay(2, 5, 5, [], "female"),
                    ], 
                    'key' => 'sex'
                ], 
                ['sex' => 'f']
            ],
        ];
    }

    /**
     * @dataProvider getValueProvider
     */
    public function testGetValue($expectation, $expectedCoordinates, $settings, $data)
    {
        if (class_exists($expectation)) {
            $this->expectException($expectation);
        }

        $overlay = new RadioOverlay(1, 1, 1, $settings);

        $overlay->inject($data);

        $this->assertEquals($expectation, $overlay->getValue());
        $this->assertEquals($expectedCoordinates['page'], $overlay->page);
        $this->assertEquals($expectedCoordinates['x'], $overlay->x);
        $this->assertEquals($expectedCoordinates['y'], $overlay->y);
    }
}