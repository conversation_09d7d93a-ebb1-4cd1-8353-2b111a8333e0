/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin')
module.exports = {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./resources/**/*.vue",
    ],
    theme: {
        extend: {
            backgroundColor: {
                "primary" : "var(--color-primary)",
                "primary-accent" : "var(--color-primary-accent)",
                "info" : "var(--color-info)",
                "info-accent" : "var(--color-info-accent)",
                "secondary" : "var(--color-secondary)",
                "secondary-accent" : "var(--color-secondary-accent)",
                "danger" : "var(--color-danger)",
                "danger-accent" : "var(--color-danger-accent)",
            },
            fontFamily: {
                sans: ['Inter var'],
            },
        },
    },
  plugins: [
      require('@tailwindcss/forms'),
  ],
}

