<?php

use App\Http\Controllers\ClientsController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DoroteaAuthController;
use App\Http\Controllers\DoroteaSummaryController;
use App\Http\Controllers\PdfToolsController;
use App\Http\Controllers\PipelineController;
use App\Http\Controllers\PlaceholderYouSignContorller;
use App\Http\Controllers\SalesmenController;
use App\Models\Client;
use App\Models\Document;
use App\Models\File;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Laravel\Fortify\Http\Controllers\AuthenticatedSessionController;
use Nahid\JsonQ\Jsonq;
use Upnovation\DocumentReader\Providers\ConfigLoader;
use Upnovation\DocumentReader\Providers\Readers\AzureIdReader;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Tasks\AIDocs\AIDocsController;
use Upnovation\Easyprofile\Tasks\Client\ClientController;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceController;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager;
use Upnovation\Easyprofile\Tasks\Issuance\Processors\FormIssuanceController;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController;
use Upnovation\Easyprofile\Tasks\Signature\SignatureController;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;
use Upnovation\Easyprofile\Tasks\Summary\SummaryController;
use Upnovation\Easyprofile\Tasks\Survey\SurveyController;
use App\Http\Controllers\IssuanceToolsController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::inertia('/new-password', 'PasswordReset');

Route::get('/', [DashboardController::class, 'getIndex'])->middleware(['auth'])->name('dashboard');

Route::post('pipeline', [PipelineController::class, 'postIndex'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.start');
Route::get('pipeline/{pipeline}/resume', [PipelineController::class, 'getResume'])->middleware(['auth', 'can:pipeline.resume,pipeline'])->name('pipeline.resume');
Route::get('pipeline/{pipeline}/next', [PipelineController::class, 'getNext'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.next');
Route::get('pipeline/{pipeline}/previous', [PipelineController::class, 'getPrevious'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.previous');
Route::post('pipeline/{pipeline}/skip/{targetTaskName}', [PipelineController::class, 'postSkip'])->middleware(['auth', 'can:pipeline.operate'])->name('pipeline.skip');
Route::get('pipeline/{pipeline}/task/{task}', [PipelineController::class, 'getGoto'])->middleware(['auth', 'can:pipeline.operate']);
Route::delete('pipeline/{pipeline}', [PipelineController::class, 'deleteIndex'])->middleware(['auth', 'can:pipeline.delete']);
Route::get('pipeline/{pipeline}', [PipelineController::class, 'getIndex'])->middleware(['auth', 'can:pipeline.view.any'])->name('pipeline.view');


//
//
// Dorotea actions
//
//
Route::get('dorotea/auth', [DoroteaAuthController::class, 'getAuth'])->middleware(['tenant.is:dorotea']);
Route::post('dorotea/{pipeline}/summary', [DoroteaSummaryController::class, 'postIndex'])->middleware(['auth', 'tenant.is:dorotea']);

//
//
// Survey actions
//
//
Route::get('tasks/survey/{pipeline}/{task}', [SurveyController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache', 'can:pipeline.operate']);
Route::post('tasks/survey/{pipeline}/{task}', [SurveyController::class, 'postIndex']);

//
//
// Mapper actions
//
//
Route::get('tasks/mapper/{pipeline}/{task}', [ProductMapperController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache', 'can:pipeline.operate']);
Route::post('tasks/mapper/{pipeline}/{task}', [ProductMapperController::class, 'postProducts']);

//
//
// Summary actions
//
//
Route::get('tasks/summary/{pipeline}/{task}', [SummaryController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);

//
//
// Client actions
//
//
Route::get('clients', [ClientsController::class, 'getIndex'])->middleware(['auth'])->name('clients');
Route::get('clients/{client}/document/{type}/{index}', [ClientsController::class, 'getDownloadDocument'])->middleware(['auth', 'nocache'])->name('clients.document');
Route::get('clients/{client}', [ClientsController::class, 'getClient'])->middleware(['auth'])->name('clients.show');

Route::get('tasks/client/{pipeline}/{task}', [ClientController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.client');
Route::post('tasks/client/{pipeline}/{task}', [ClientController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::put('tasks/client/{pipeline}/{task}', [ClientController::class, 'putIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::get('clients/search/{query}', [ClientController::class, 'getSearch'])->middleware(['auth', 'nocache']);

//
//
// Signature actions
//
//
Route::get('tasks/signature/{pipeline}/{task}', [SignatureController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.signature');

// Signature module.
Route::group(['prefix' => 'sign', 'middleware' => ['tenant.is:' . config('easyprofile.modules.signature.enabledTenants')]], function(){
    if (in_array(env('APP_ENV'), ['local'])) {
        Route::get('fake/{task}', function(Task $task){
            $task->addData('signatureStatus', 'done');
            $task->addData('folder', null);
            $task->save();

            app()->make(FileManager::class)->deletePipelineSignableFiles($task->pipeline);

            return redirect()->route('pipeline.next', [
                'pipeline' => $task->pipeline, 
            ]);
        });
    }

    Route::post('/{pipeline}/{task}', [SignatureController::class, 'postSign'])->middleware(['auth',]);
});

Route::get('signature/{pipeline}/{task}/callback/igs/{folderId}/{eventId}', [SignatureController::class, 'getIGSignCallback'])->name('signature.callback.igsign');

//
//
// Issuance actions
//
//
Route::get('tasks/issuance/{pipeline}/{task}/product/{product}', [IssuanceController::class, 'getIssue'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches', 'nocache']);

// Route::post('tasks/issuance/{pipeline}/{task}/ask/{issuance}', [IssuanceController::class, 'postAskReload'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches']);
Route::get('tasks/issuance/{pipeline}/{task}/form/{issuance}', [IssuanceController::class, 'getIssuanceForm'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches']);
Route::post('tasks/issuance/{pipeline}/{task}/form/{issuance}', [IssuanceController::class, 'postIssuanceForm'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches']);

Route::post('tasks/issuance/{pipeline}/{task}/upload', [IssuanceController::class, 'postUpload'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::get('tasks/issuance/{pipeline}/{task}', [IssuanceController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache'])->name('tasks.issuance');
Route::post('tasks/issuance/{pipeline}/{task}', [IssuanceController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'task.matches', 'nocache']);
Route::get('issue/{pipeline}/{task}/{issuance}', [FormIssuanceController::class, 'getIndex'])->middleware(['auth', 'task.accessible', 'nocache'])->name('issue.form');
Route::post('issue/{pipeline}/{task}/{issuance}', [FormIssuanceController::class, 'postIndex'])->middleware(['auth', 'task.accessible', 'nocache'])->name('issue.form.post');

//
//
// AI Docs
//
//
Route::get('tasks/aidocs/{pipeline}/{task}', [AIDocsController::class, 'getIndex'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches', 'nocache']);
Route::post('tasks/aidocs/{pipeline}/{task}', [AIDocsController::class, 'postIndex'])->middleware(['auth', 'can:pipeline.operate', 'task.accessible', 'task.matches', 'nocache']);

//
//
// PDF tools
//
//
if (in_array(env('APP_ENV'), ['local'])) {
    Route::get('tools/pdf/{document}/{pipeline?}', [PdfToolsController::class, 'getDocument'])->middleware(['auth', 'nocache']);
    Route::get('tools/issuance/{product?}', [IssuanceToolsController::class, 'getForm'])->middleware(['auth', 'nocache']);
}

// @TODO whether the upload will work as standalone. Non used for now.
//Route::post('clients/id/upload', [ClientController::class, 'postUpload'])->middleware(['auth']);

//
//
// APP actions / modules
//
//
Route::get('/salesmen', [SalesmenController::class, 'getIndex'])->middleware(['auth', 'can:salesman.administer']);
Route::get('/salesmen/{id}', [SalesmenController::class, 'getSalesman'])->middleware(['auth', 'can:salesman.administer']);
Route::post('/salesman/status/{userId}', [SalesmenController::class, 'postSalesman'])->middleware(['auth', 'can:salesman.administer']);
Route::put('/salesman/{user}', [SalesmenController::class, 'putSalesman'])->middleware(['auth', 'can:salesman.administer']);

Route::get('/products', [\App\Http\Controllers\ProductsController::class, 'getIndex'])->middleware('auth');
Route::get('/products/{product}', [\App\Http\Controllers\ProductsController::class, 'getProduct'])->middleware('auth');

// Documents module.
Route::group(['prefix' => 'documents', 'middleware' => ['tenant.is:' . config('easyprofile.modules.documents.enabledTenants')]], function(){
    Route::get('/', [\App\Http\Controllers\DocumentsController::class, 'getIndex'])->middleware(['auth', 'can:documents.view']);
    Route::get('/{id}/template', [\App\Http\Controllers\DocumentsController::class, 'getTemplate'])->middleware(['auth', 'can:documents.view']);
    Route::get('/{uuid}', [\App\Http\Controllers\DocumentsController::class, 'getFile'])->middleware(['auth', 'can:documents.view']);
});

//
//
// GEO utils.
//
//
Route::get('geo/countries/{query}', function(Request $request, $query){
    
    // Disabled as it's been replaced with JS world-countries package.
    // @TODO remove this route in the future.
    return null;

    // Load json from storage.
    $json = storage_path('app/private/countries.json');

    if (! file_exists($json)) {
        abort(404);
    }

    if (! $countries = json_decode(file_get_contents($json), true)) {
        Log::error("Error decoding countries JSON file.");

        abort(500);
    }

    if ($query = strtolower($query)) {
        $countries = array_filter($countries, function ($country) use ($query) {
            return str_contains(strtolower($country['name']), $query)
                || str_contains(strtolower($country['code']), $query);
        });

    $countries = array_values($countries); // reindicizza
}

    // Return the countries as a JSON response.
    return response()->json($countries);

})->middleware(['auth']);

Route::get('geo/{q}', function(Request $request, $query){
    /*$jsonq = new Jsonq(storage_path('app/geo.json'));
    
    $result = ($jsonq->from('data')->where('nome', 'contains', $query)->fetch());*/

    $qb = DB::connection('tenant')->table('gi_comuni_cap');

    if ($request->has('_')) {
        $qb->select('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap');
        $qb->where('denominazione_ita', 'like', "%$query%");
        $qb->orderByRaw('LENGTH(denominazione_ita) ASC');
        //$qb->groupBy('denominazione_ita');
    } else {
        $qb->select('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap');
        $qb->where('cap', 'like', "$query%");
    }
                //->groupBy('denominazione_ita', 'sigla_provincia', 'denominazione_provincia', 'denominazione_regione', 'cap')
    return $qb->get();

    // Fuck up to get it into an array.
    return array_values(json_decode($result, true));
})->middleware(['auth']);


//
//
// DEV routes.
//
//
if (in_array(env('APP_ENV'), ['local', 'staging']))
{
    Route::get('dorotea/debug/pipeline', [DoroteaAuthController::class, 'getDebugPipeline'])->middleware(['auth', 'tenant.is:dorotea']);
}

if (in_array(env('APP_ENV'), ['local', 'testing']))
{
    Route::get('callbacks', function(Request $request, User $user){
        $pipelines = Pipeline::withoutGlobalScopes()->where('state', '!=', 'closed')->get();

        foreach($pipelines as $pipeline) {
            print "ID# {$pipeline->id} ";

            $client = $pipeline->getContractor();

            print ($client ? $client->name : 'N/A') . "<br>";

            print "<a href='/debug/{$pipeline->user->id}'>User: {$pipeline->user->id} {$pipeline->user->name}</a><br>";

            foreach($pipeline->tasks as $task) {
                if ($task->type == 'signature') {
                    if (empty($task->data['folder'])) {
                        print "#{$task->id} no folder <br>";

                        continue;
                    }
                    print "#{$task->id} <a href='/signature/{$pipeline->id}/{$task->id}/callback/igs/{$task->data['folder']['id']}/11'>Callback</a><br>";
                }
            }

            $profile = $pipeline->getProfile();

            if ($profile && $log = $profile->log) {
                dump($log->logs);
            }

            print "<hr class='margin: 16px 0'>";
        }

        $users = User::withoutGlobalScopes()->whereHas('roles', function($query){
            $query->where('name', 'salesman');
        })->get();

        foreach($users as $user) {
            print "<a href='/debug/{$user->id}'>Login as {$user->name}</a><br>";
        }
    });

    // for PeopleScope quick testing
    Route::get('_people/{user}', function(Request $request, User $user){
        
        auth()->login($user);
        return Person::find(27);
        return [Person::find(25), Client::find(27)];

        return [Client::find(1), Client::find(2)];

        return $user;
    });

    /**Route::get('pp', function(){
        $p = Pipeline::find()
    });*/

    Route::get('ttt', function(){
        $document = Document::find(8);

        $file = File::find(5);

        //dump($document->template);

        return compact('document', 'file');
    });

    Route::get('tt', function(){
        $reader = new AzureIdReader(env('AZURE_OPENAI_ENDPOINT'), env('AZURE_OPENAI_APIKEY1'));

        try {
            return ($reader->analyzeFile(Storage::path('doc.jpeg')));

            file_put_contents('testcie.txt', print_r($result['analyzeResult']['documents'][0], true), FILE_APPEND);
        } catch (Exception $e) {
            echo "Errore: " . $e->getMessage();
        }


    });

    //Route::get('yousign', [PlaceholderYouSignContorller::class, 'getIndex']);

    Route::get('testing/tenants', function(){
        return response(null, 201);
    })->middleware('tenant.is:dorotea,tenant1');

    Route::get('testing/task/accessible/{pipeline?}/{task?}', function(Pipeline $pipeline, Task $task){
        
    })->middleware('task.accessible');

    Route::get('testing/task/matches/{pipeline?}/{task?}', function(Pipeline $pipeline, Task $task){
        
    })->middleware('task.matches');

    Route::get("debug/admin", function(){
        // Load firs user with role manager
        $user = User::whereHas('roles', function($query){
            $query->where('name', 'manager');
        })->first();

        Auth::guard('web')->login($user);

        return redirect("/");
    });

    Route::get("debug/{user?}", function(Request $request, ?User $user = null){
        if ($user) {
            Auth::guard('web')->login($user);

            return redirect("/");
        }

        $users = User::whereHas('roles', function($query){
            $query->where('name', 'salesman');
        })->get();


        Auth::guard('web')->login($users->first());


        return redirect("/");
    });
}

// Kind of hack to add a middleware to the 'login' route
// which is being added by Fortify. The goal here is to
// prevent arbitrary tenants to access the login route.
// In this case, the ips middleware is filtering by 
// whitelisted IP address.
//Route::prefix('login')->middleware('ips')->group(function(){
    // @TODO move in easyprofile service provider, like:
    
    // Identify route somehow
    //$route = Route::getRoutes()->getByName('login');
    //dump(Route::getRoutes()->getRoutesByName());
    //dump($route);

    // Add the required middleware
    //Route::getRoutes()->getIterator()[0]->middleware('ips');
    // -------------------------------------------------------
    
    // @FIXME workaround, adding the fortify action.
    Route::get('login', [AuthenticatedSessionController::class, 'create'])->middleware(['ips', 'guest'])->name('login');
//});

if (in_array(env('APP_ENV'), ['staging']))
{
    Route::get("staging/admin/" . env("VITE_STAGING_KEY"), function(){
        Auth::guard('web')->login(User::find(2));

        return redirect("/");
    });

    Route::get("staging/" . env("VITE_STAGING_KEY"), function(){
        Auth::guard('web')->login(User::find(1));

        return redirect("/");
    });
}
