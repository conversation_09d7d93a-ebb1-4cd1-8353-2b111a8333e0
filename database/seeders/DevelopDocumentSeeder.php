<?php namespace Database\Seeders;

use Database\Factories\DocumentFactory;
use Illuminate\Database\Seeder;

class DevelopDocumentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        
        DocumentFactory::new()->count(10)->create();

        // seed a document with a specific type
        DocumentFactory::new()->create([
            'node_id' => 1,
            'title' => 'Informativa sul trattamento dei dati personali',
            'type' => 'privacy',
        ]);

        DocumentFactory::new()->create([
            'node_id' => 1,
            'title' => 'Allegato 3',
            'type' => 'all-3',
        ]);
        
    }
}
