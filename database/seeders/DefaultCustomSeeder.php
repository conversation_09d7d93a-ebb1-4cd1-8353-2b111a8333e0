<?php namespace Database\Seeders;

use App\Models\Address;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Pdf\PdfProcessorDN;

class DefaultCustomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (app('currentTenant')->name == 'default') {

            // Crea 25 persone
            $persons = Person::factory()->count(25)->create();

            $persons->add(Person::factory()->create([
                'name' => 'Mario',
                'lastname' => 'Rossi',
            ]));

            $persons->each(function ($person) {
                $address = Address::factory()->make();
                $address->type = 'residence'; 
                $person->address()->save($address);
            });

            // Crea 25 aziende, ognuna con un rep_id preso dalle persone appena create
            $enterprises = Enterprise::factory()->count(25)->make(['rep_id' => $persons->random()->id]);

            $enterprises->add(Enterprise::factory()->create([
                'name' => 'Azienda srl',
                'rep_id' => $persons->random()->id,
            ]));

            $enterprises->each(function ($enterprise) use ($persons) {
                $enterprise->save();

                $address = Address::factory()->make();
                $address->type = 'headquarters';
                $enterprise->address()->save($address);
            });

        }
    }
}
