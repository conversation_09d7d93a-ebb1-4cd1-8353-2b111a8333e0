<?php

namespace Database\Seeders;

use Upnovation\Easyprofile\Tasks\Issuance\Processors\FormIssuanceController;

class Catalog
{

    public static function companies($id = null)
    {
        $companies = [
            1 => ['id' => 1, 'name' => 'Groupama', 'logo' => 'logo-groupama.jpg'],
            2 => ['id' => 2, 'name' => 'CF Assicurazioni', 'logo' => 'logo-cf.jpg'],
            3 => ['id' => 3, 'name' => 'Net Insurance', 'logo' => 'logo-net.jpg'],
            4 => ['id' => 4, 'name' => 'Axa Assicurazioni', 'logo' => 'logo-axa.jpg'],
            5 => ['id' => 5, 'name' => 'Afi Esca', 'logo' => 'logo-afi-esca.png'],
            6 => ['id' => 6, 'name' => 'Nobis', 'logo' => 'logo-nobis.png'],
            7 => ['id' => 7, 'name' => 'Dual', 'logo' => 'logo-dual.png'],
        ];

        return $id ? $companies[$id] : $companies;
    }

    public static function categories($id = null)
    {
        $categories = [
            1 => ['id' => 1, 'name' => 'Protezione Persona', 'branch' => 'non-life'],
            2 => ['id' => 2, 'name' => 'Protezione Beni', 'branch' => 'non-life'],
            3 => ['id' => 3, 'name' => 'Protezione Patrimonio', 'branch' => 'non-life'],
        ];

        return $id ? $categories[$id] : $categories;
    }

    public static function coverages($id = null)
    {
        $coverages = [
            ['id' => 1, 'label' => 'fire.building', 'category_id' => 2,     'name' => 'Incendio (fabbricato)',  'shortname' => 'Incendio (fabbricato)', 'target' => 'retail', 'type' => 'main'],
            ['id' => 2, 'label' => 'electric', 'category_id' => 2,          'name' => 'Fenomeno elettrico',     'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 9, 'label' => 'weather', 'category_id' => 2,           'name' => 'Eventi atmosferici',     'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 10, 'label' => 'sociopolitical', 'category_id' => 2,   'name' => 'Eventi socio-politci',   'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 3, 'label' => 'b', 'category_id' => 2,                 'name' => 'Fumi-gas',               'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 4, 'label' => 'c', 'category_id' => 2,                 'name' => 'Danni arrecati a seguito ordine Autorità', 'shortname' => '',  'target' => 'retail', 'type' => 'complementary'],
            ['id' => 5, 'label' => 'd', 'category_id' => 2,                 'name' => 'Deomlizione / sgombero', 'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 6, 'label' => 'e', 'category_id' => 2,                 'name' => 'Eventi socio-atmosferici', 'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 7, 'label' => 'snow', 'category_id' => 2,              'name' => 'Sovraccarico neve', 'shortname' => '', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 8, 'label' => 'fire.RC', 'category_id' => 2,           'name' => 'RC Terzi da incendio', 'shortname' => 'RC Terzi da incendio', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 11, 'label' => 'fire.contents', 'category_id' => 2,    'name' => 'Incendio (contenuto)', 'shortname' => 'Incendio (contenuto)', 'target' => 'retail', 'type' => 'main'],
            ['id' => 40, 'label' => 'water', 'category_id' => 2,            'name' => 'Acqua condotta', 'shortname' => 'Acqua condotta', 'target' => 'retail', 'type' => 'main'],

            ['id' => 33, 'label' => 'rcp', 'category_id' => 3,              'name' => 'RC Terzi - Vita privata', 'shortname' => 'RCP', 'target' => 'retail', 'type' => 'main'],
            ['id' => 34, 'label' => 'rcd', 'category_id' => 3,              'name' => 'RC Dipendenti', 'shortname' => 'RCD', 'target' => 'retail', 'type' => 'main'],
            ['id' => 35, 'label' => 'rcf', 'category_id' => 3,              'name' => 'RC Fabbricati', 'shortname' => 'RCF', 'target' => 'retail', 'type' => 'main'],
            ['id' => 36, 'label' => 'tut', 'category_id' => 3,              'name' => 'Tutela legale', 'shortname' => 'TUT', 'target' => 'retail', 'type' => 'main'],
            ['id' => 37, 'label' => 'ass', 'category_id' => 3,              'name' => 'Assistenza', 'shortname' => 'ASS', 'target' => 'retail', 'type' => 'main'],
            ['id' => 38, 'label' => 'suc', 'category_id' => 3,              'name' => 'Indennizzo successione', 'shortname' => 'SUC', 'target' => 'retail', 'type' => 'main'],
            ['id' => 39, 'label' => 'don', 'category_id' => 3,              'name' => ' Perdite derivanti da un’azione di restituzione da parte di un legittimario di immobile oggetto di donazione', 'shortname' => 'Indennizzo donazione', 'target' => 'retail', 'type' => 'main'],
            ['id' => 14, 'label' => 'pii', 'category_id' => 3,              'name' => 'Perdita impiego', 'shortname' => 'PII', 'target' => 'retail', 'type' => 'main'],

            ['id' => 17, 'label' => 'ltc', 'category_id' => 1,              'name' => 'Long Term Care', 'shortname' => 'LTC', 'target' => 'retail', 'type' => 'main'],
            ['id' => 18, 'label' => 'eq.building', 'category_id' => 2,       'name' => 'Terremoto (fabbricato)', 'shortname' => 'Terremoto (fabbricato)', 'target' => 'retail', 'type' => 'main'],
            ['id' => 19, 'label' => 'eq.contents', 'category_id' => 2,       'name' => 'Terremoto (contenuto)', 'shortname' => 'Terremoto (contenuto)', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 20, 'label' => 'flood', 'category_id' => 2,       'name' => 'Rischi catastrofali - Indondazione, alluvione e allagamento', 'shortname' => 'Inondazione', 'target' => 'retail', 'type' => 'complementary'],

            ['id' => 22, 'label' => 'ipti', 'category_id' => 1,              'name' => 'Invalidità permanente totale da infortunio', 'shortname' => 'IPTi', 'target' => 'retail', 'type' => 'main'],
            ['id' => 23, 'label' => 'iptm', 'category_id' => 1,              'name' => 'Invalidità permanente totale da malattia', 'shortname' => 'IPTm', 'target' => 'retail', 'type' => 'main'],
            ['id' => 24, 'label' => 'itti', 'category_id' => 1,              'name' => 'Inabilità temporanea totale da infortunio', 'shortname' => 'ITTi', 'target' => 'retail', 'type' => 'main'],
            ['id' => 25, 'label' => 'ittm', 'category_id' => 1,              'name' => 'Inabilità temporanea totale da malattia', 'shortname' => 'ITTm', 'target' => 'retail', 'type' => 'main'],
            ['id' => 26, 'label' => 'rir', 'category_id' => 1,              'name' => 'Riduzione reddito', 'shortname' => 'RIR', 'target' => 'retail', 'type' => 'main'],
            ['id' => 27, 'label' => 'surgeryi', 'category_id' => 1,       'name' => 'Grandi interventi chirurgici da infortunio', 'shortname' => 'Grandi interventi chirurgici', 'target' => 'retail', 'type' => 'main'],
            ['id' => 28, 'label' => 'surgerym', 'category_id' => 1,       'name' => 'Grandi interventi chirurgici da malattia', 'shortname' => 'Grandi interventi chirurgici', 'target' => 'retail', 'type' => 'main'],

            ['id' => 15, 'label' => 'tcmcq', 'category_id' => 1,              'name' => 'Morte per qualsiasi causa (capitale costante)', 'shortname' => 'TCM', 'target' => 'retail', 'type' => 'main'],
            ['id' => 16, 'label' => 'tcmci', 'category_id' => 1,              'name' => 'Morte da infortunio (capitale costante)', 'shortname' => 'TCM', 'target' => 'retail', 'type' => 'main'],
            ['id' => 29, 'label' => 'tcmdq', 'category_id' => 1,              'name' => 'Morte per qualsiasi causa (capitale decrescente)', 'shortname' => 'TCM', 'target' => 'retail', 'type' => 'main'],
            ['id' => 30, 'label' => 'tcmdi', 'category_id' => 1,              'name' => 'Morte da infortunio (capitale decrescente)', 'shortname' => 'TCM', 'target' => 'retail', 'type' => 'main'],
            
            ['id' => 31, 'label' => 'rt', 'category_id' => 3,              'name' => 'Ricorso terzi', 'shortname' => 'Ric.Terzi', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 32, 'label' => 'vandalism', 'category_id' => 2,              'name' => 'Atti vandalici', 'shortname' => 'Atti Vandalici', 'target' => 'retail', 'type' => 'complementary'],
            ['id' => 41, 'label' => 'ro', 'category_id' => 1,              'name' => 'Ricovero ospedaliero', 'shortname' => 'Ricovero ospedaliero', 'target' => 'retail', 'type' => 'complementary'],
        ];

        return $id ? array_values(array_filter($coverages, function($coverage) use ($id) { return $coverage['id'] === $id || $coverage['label'] === $id; })) : array_values($coverages);
    }

    public static function products($id = null)
    {
        $products = [
            1 => ['id' => 1, 'company_id' => 2, 'enabled' => true, 'code' => 'CFAINUNCASA', 'name' => 'Incendio Unico Casa', 'minAge' => null, 'maxAge' => null, 'minLen' => 5, 'maxLen' => 40, 'warnings' => [
                'product' => ['La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],
            
            2 => [
                'id' => 2, 'company_id' => 2, 'enabled' => true, 'code' => 'CFASEICOPES', 'name' => 'Sei Coperto', 'minAge' => 18, 'maxAge' => 65, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                    'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',]
                ],
                'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/PlaceholderTBD",
                ],
                'processType' => 'deferred',
            ],
            
            3 => ['id' => 3, 'company_id' => 2, 'enabled' => true, 'code' => 'CFASEICOPEE', 'name' => 'Sei Coperto Easy', 'minAge' => 18, 'maxAge' => 60, 'minLen' => 3, 'maxLen' => 15, 'warnings' => [
                'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',]
            ]],
            
            4 =>['id' => 4, 'company_id' => 2, 'enabled' => true, 'code' => 'CFANICELIFE', 'name' => 'Nice Life', 'minAge' => 18, 'maxAge' => 70, 'minLen' => 1, 'maxLen' => 30, 'warnings' => [
                'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',],
                'product' => ['Il capitale assicurato in caso di decesso per qualsiasi causa è decrescente',]
            ]],
            
            5 => ['id' => 5, 'company_id' => 2, 'enabled' => true, 'code' => 'CFAPROTMASS', 'name' => 'Protezione Massima', 'minAge' => 18, 'maxAge' => 60, 'minLen' => 3, 'maxLen' => 5, 'warnings' => [
                'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',]
            ]],
            
            6 => ['id' => 6, 'company_id' => 2, 'enabled' => true, 'code' => 'CFAVITACOLO', 'name' => 'Vita a colori', 'minAge' => 18, 'maxAge' => 70, 'minLen' => 5, 'maxLen' => 30, 'warnings' => [
                'product' => ['Il capitale assicurato in caso di decesso per qualsiasi causa è decrescente',],
            ]],

            7 => ['id' => 7, 'company_id' => 3, 'enabled' => true, 'code' => 'NETPROREVTP', 'name' => 'Protezione eventi naturali Più', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => []],
            
            8 => ['id' => 8, 'company_id' => 4, 'enabled' => true, 'code' => 'AXACFPROSAP', 'name' => 'Protezione Salute Più', 'minAge' => 18, 'maxAge' => 69, 'minLen' => 5, 'maxLen' => 5, 'warnings' => [
                'product' => ['Prodotto non adeguato per persone fisiche che svolgono attività professionali rischiose'],
            ],
                'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/PlaceholderTBD",
                ],
                'processType' => 'direct',
            ],

            9 => ['id' => 9, 'company_id' => 5, 'enabled' => true, 'code' => 'AFIVIVE4100', 'name' => 'Vivendo 4 100', 'minAge' => 18, 'maxAge' => 62, 'minLen' => 3, 'maxLen' => 9, 'warnings' => [
                'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',]
            ]],
            
            10 => ['id' => 10, 'company_id' => 5, 'enabled' => true, 'code' => 'AFIVIVEE100', 'name' => 'Vivendo Elite 100', 'minAge' => 18, 'maxAge' => 64, 'minLen' => 1, 'maxLen' => 9, 'warnings' => [
                'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',]
            ]],
            
            11 => ['id' => 11, 'company_id' => 1, 'enabled' => true, 'code' => 'GROCASSENZC', 'name' => 'Casa Senza Confini', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                //'profile' => ['extremeSports' => 'Prodotto non adeguato per coloro che svolgono un’attività lavorativa, sportiva, amatoriale a rischio',],
                'product' => ['La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],

            12 => ['id' => 12, 'company_id' => 2, 'enabled' => true, 'code' => 'CFAINUNCAS1', 'name' => 'Incendio Unico Casa (Opz.1)', 'minAge' => null, 'maxAge' => null, 'minLen' => 5, 'maxLen' => 40, 'warnings' => [
                'product' => ['Opzione Fenomeno elettrico + Ricorso Terzi', 'La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],
            
            13 => ['id' => 13, 'company_id' => 2, 'enabled' => true, 'code' => 'CFAINUNCAS2', 'name' => 'Incendio Unico Casa (Opz.2)', 'minAge' => null, 'maxAge' => null, 'minLen' => 5, 'maxLen' => 40, 'warnings' => [
                'product' => ['Opzione Fenomeno elettrico + Eventi atmosferici', 'La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],

            14 => ['id' => 14, 'company_id' => 6, 'enabled' => true, 'code' => 'NOBCASAMUTU', 'name' => 'Casa Solo Mutui', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => 35, 'warnings' => [
                'product' => ['Prodotto sottoscrivibile solo in caso di mutuo in corso'],
            ]],
            
            15 => ['id' => 15, 'company_id' => 6, 'enabled' => true, 'code' => 'NOBCASAMUT1', 'name' => 'Casa Solo Mutui (Opz.1)', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => 35, 'warnings' => [
                'product' => ['Opzione solo copertura mutuo', 'Prodotto sottoscrivibile solo in caso di mutuo in corso'],
            ]],
            
            16 => ['id' => 16, 'company_id' => 6, 'enabled' => true, 'code' => 'NOBCASAMUT2', 'name' => 'Casa Solo Mutui (Opz.2)', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => 35, 'warnings' => [
                'product' => ['Opzione copertura mutuo + coperture complementari', 'Prodotto sottoscrivibile solo in caso di mutuo in corso'],
            ]],

            17 => ['id' => 17, 'company_id' => 1, 'enabled' => true, 'code' => 'GROCASSENZ1', 'name' => 'Casa Senza Confini (Opz. Completa)', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                'product' => ['Opzione CON ricorso terzi', 'La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],

            18 => ['id' => 18, 'company_id' => 1, 'enabled' => true, 'code' => 'GROCASSENZ2', 'name' => 'Casa Senza Confini (Opz. Base)', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                'product' => ['Opzione SENZA ricorso terzi', 'La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],

            19 => ['id' => 19, 'company_id' => 1, 'enabled' => true, 'code' => 'GROCASSENZ3', 'name' => 'Casa Senza Confini (Incendio 0.41)', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                'product' => ['La durata massima del prodotto deve essere pari alla durata del mutuo'],
            ]],

            // @TBD: CONFIG
            20 => ['id' => 20, 'company_id' => 2, 'enabled' => true, 'code' => 'CFMUTUIEVOL', 'name' => 'Incendio Mutui Evolution', 'minAge' => null, 'maxAge' => null, 'minLen' => 5, 'maxLen' => null, 'warnings' => [
                    'profile' => ['mortgage' => 'Prodotto non adeguato per fabbricati NON oggetto di mutuo',],
                    'product' => [
                        'La durata massima del prodotto deve essere pari alla durata del mutuo',
                        'Solo fabbricati ad uso abitazione o ufficio'
                    ],
                ],
                'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/CFMutuiEvolution",
                ],
                'processType' => 'deferred',
            ],

            21 => ['id' => 21, 'company_id' => 7, 'enabled' => true, 'code' => 'DUADONATION', 'name' => 'Donation No Problem', 'minAge' => null, 'maxAge' => null, 'minLen' => null, 'maxLen' => null, 'warnings' => [
                'product' => ['L\'immobile donato deve essere situato nel territorio della Repubblica Italiana'],
                ],
                'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/PlaceholderTBD",
                ],
                'processType' => 'download',
            ],

            22 => ['id' => 22, 'company_id' => 4, 'enabled' => true, 'code' => 'AXASIMPLYPR', 'name' => 'Simply Protection', 'minAge' => 18, 'maxAge' => 65, 'minLen' => 5, 'maxLen' => 10, 'warnings' => [
                'product' => [
                    'Non adeguato a non residenti in Italia',
                    'Non adeguato a persone che praticano sport pericolosi',
                    "Non adeguato a persone che, nei casi di copertura per ITT e PII, non esercitino un'attività lavorativa retribuita o produttiva di reddito certificabile",
                ],
            ],
            'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/AXASimplyProtection",
                ],
                'processType' => 'direct',
            ],

            23 => ['id' => 23, 'company_id' => 5, 'enabled' => true, 'code' => 'AFIVIVENDO', 'name' => 'Vivendo', 'minAge' => 18, 'maxAge' => 64, 'minLen' => 1, 'maxLen' => 10, 'warnings' => [
                'product' => [
                    'Non adeguato a non residenti e/o domiciliati in Italia',
                    'Non adeguato a persone che praticano sport pericolosi',
                    "Non adeguato a persone che, nei casi di copertura per ITT e PII, non esercitino un'attività lavorativa retribuita o produttiva di reddito certificabile",
                    "Per la PII si esclude che percepisce altri redditi da lavoro"
                ],
            ],
            'issuanceProcessor' => [
                    'controller' => FormIssuanceController::class,
                    'template' => "Tasks/IssuanceForms/PlaceholderTBD",
                ],
                'processType' => 'download',
            ],


        ];

        return $id ? $products[$id] : $products;
    }

    public static function productCoverages($productId)
    {
        $data = [
            // Incendio Unico Casa
            ['product_id' => 1, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 1, 'coverage_id' => 31, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 1, 'coverage_id' => 9, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 1, 'coverage_id' => 10, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 1, 'coverage_id' => 7, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Sei Coperto
            ['product_id' => 2, 'coverage_id' => 22, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic', 'excludedJobs' => null,],
            ['product_id' => 2, 'coverage_id' => 23, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic', 'excludedJobs' => null,],
            ['product_id' => 2, 'coverage_id' => 24, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePublic', 'excludedJobs' => null,],
            ['product_id' => 2, 'coverage_id' => 25, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePublic', 'excludedJobs' => null,],
            ['product_id' => 2, 'coverage_id' => 14, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'employeePrivate', 'excludedJobs' => null,],

            // Sei Coperto Easy
            ['product_id' => 3, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Nice Life
            ['product_id' => 4, 'coverage_id' => 29, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 4, 'coverage_id' => 22, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 4, 'coverage_id' => 23, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Protezione massima
            ['product_id' => 5, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 22, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 23, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 24, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 25, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 17, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePrivateLimited|employeePublic|businessOwner', 'excludedJobs' => 'unemployed',],
            ['product_id' => 5, 'coverage_id' => 14, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'employeePrivate', 'excludedJobs' => 'unemployed',],
            
            // Vita a colori
            ['product_id' => 6, 'coverage_id' => 29, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Protezione eventi naturali piu
            ['product_id' => 7, 'coverage_id' => 18, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 7, 'coverage_id' => 19, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 7, 'coverage_id' => 20, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Protezione salute piu
            ['product_id' => 8, 'coverage_id' => 16, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 8, 'coverage_id' => 22, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 8, 'coverage_id' => 23, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 8, 'coverage_id' => 27, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 8, 'coverage_id' => 28, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Vivendo 4 100
            ['product_id' => 9, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited', 'excludedJobs' => null,],
            ['product_id' => 9, 'coverage_id' => 22, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited', 'excludedJobs' => null,],
            ['product_id' => 9, 'coverage_id' => 23, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited', 'excludedJobs' => null,],
            ['product_id' => 9, 'coverage_id' => 24, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited', 'excludedJobs' => null,],
            ['product_id' => 9, 'coverage_id' => 25, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited', 'excludedJobs' => null,],
            ['product_id' => 9, 'coverage_id' => 14, 'setup' => 'optional', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'employeePrivate|employeePrivateLimited', 'excludedJobs' => null,],
            
            // Vivendo Elite 100
            ['product_id' => 10, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited|businessOwner|retired|unemployed', 'excludedJobs' => null,],
            ['product_id' => 10, 'coverage_id' => 22, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited|businessOwner|retired|unemployed', 'excludedJobs' => null,],
            ['product_id' => 10, 'coverage_id' => 23, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited|businessOwner|retired|unemployed', 'excludedJobs' => null,],
            ['product_id' => 10, 'coverage_id' => 24, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited|businessOwner', 'excludedJobs' => null,],
            ['product_id' => 10, 'coverage_id' => 25, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'selfEmployed|freelance|employeePrivate|employeePublic|employeePrivateLimited|businessOwner', 'excludedJobs' => null,],
            ['product_id' => 10, 'coverage_id' => 14, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivate|employeePrivateLimited', 'excludedJobs' => 'unemployed',],
            
            // Casa senza confini
            ['product_id' => 11, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 11, 'coverage_id' => 9, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 11, 'coverage_id' => 10, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 11, 'coverage_id' => 31, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Incendio Unico Casa (Dorotea Opz.1)
            ['product_id' => 12, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 12, 'coverage_id' => 2, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 12, 'coverage_id' => 31, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Incendio Unico Casa (Dorotea Opz.2)
            ['product_id' => 13, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 13, 'coverage_id' => 2, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 13, 'coverage_id' => 9, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 13, 'coverage_id' => 10, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 13, 'coverage_id' => 7, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Casa Solo Mutui 
            ['product_id' => 14, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 14, 'coverage_id' => 9, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 14, 'coverage_id' => 2, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 14, 'coverage_id' => 32, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            
            // Casa Solo Mutui (Dorotea Opz.1)
            ['product_id' => 15, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Casa Solo Mutui (Dorotea Opz.2)
            ['product_id' => 16, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 16, 'coverage_id' => 9, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 16, 'coverage_id' => 2, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 16, 'coverage_id' => 31, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 16, 'coverage_id' => 32, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Casa senza confini (Opz. 1)
            ['product_id' => 17, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 17, 'coverage_id' => 9, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 17, 'coverage_id' => 10, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 17, 'coverage_id' => 31, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Casa senza confini (Opz. 2)
            ['product_id' => 18, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 18, 'coverage_id' => 9, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 18, 'coverage_id' => 10, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // Casa senza confini (Opz. 3)
            ['product_id' => 19, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // CF Incendio Mutui Evolution
            ['product_id' => 20, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 20, 'coverage_id' => 2, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 20, 'coverage_id' => 8, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 20, 'coverage_id' => 9, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 20, 'coverage_id' => 10, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],
            ['product_id' => 20, 'coverage_id' => 40, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // DUAL Donation No Problem
            ['product_id' => 21, 'coverage_id' => 39, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null,],

            // AXA Simply Protection
            ['product_id' => 22, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // IPT
            ['product_id' => 22, 'coverage_id' => 22, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            ['product_id' => 22, 'coverage_id' => 23, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // ITT, RO
            ['product_id' => 22, 'coverage_id' => 24, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            ['product_id' => 22, 'coverage_id' => 25, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            ['product_id' => 22, 'coverage_id' => 41, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // PII
            ['product_id' => 22, 'coverage_id' => 14, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate', 'excludedJobs' => null,],

            // AFI ESCA Vivendo
            ['product_id' => 23, 'coverage_id' => 15, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // IPT
            ['product_id' => 23, 'coverage_id' => 22, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            ['product_id' => 23, 'coverage_id' => 23, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // ITT
            ['product_id' => 23, 'coverage_id' => 24, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            ['product_id' => 23, 'coverage_id' => 25, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePublic|selfEmployed|unemployed', 'excludedJobs' => null,],
            // PII
            ['product_id' => 23, 'coverage_id' => 14, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'employeePrivateLimited|employeePrivate', 'excludedJobs' => null,],
            
        ];

        // Return item filtering by productId
        return array_filter($data, function($item) use ($productId) { return $item['product_id'] === $productId; });
    }

    public static function productOptions($productId)
    {
        $data = [
            //
            // Sei Coperto
            //
            // IPT+PII
            ['product_id'=> 2, 'coverage_id' => 22, 'option' => 1, ],
            ['product_id'=> 2, 'coverage_id' => 23, 'option' => 1, ],
            ['product_id'=> 2, 'coverage_id' => 14, 'option' => 1, ],
            // IPT + ITT
            ['product_id'=> 2, 'coverage_id' => 22, 'option' => 2, ],
            ['product_id'=> 2, 'coverage_id' => 23, 'option' => 2, ],
            ['product_id'=> 2, 'coverage_id' => 24, 'option' => 2, ],
            ['product_id'=> 2, 'coverage_id' => 25, 'option' => 2, ],

            //
            // Nice Life
            //
            // TCM + IPT
            ['product_id'=> 4, 'coverage_id' => 29, 'option' => 1, ],
            ['product_id'=> 4, 'coverage_id' => 22, 'option' => 1, ],
            ['product_id'=> 4, 'coverage_id' => 23, 'option' => 1, ],

            //
            // Protezione Massima
            //
            // TCM + IPT + LTC + PII
            ['product_id'=> 5, 'coverage_id' => 15, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 22, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 23, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 17, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 14, 'option' => 1, ],
            // TCM + IPT + LTC + ITT
            ['product_id'=> 5, 'coverage_id' => 15, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 22, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 23, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 17, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 24, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 25, 'option' => 2, ],

            //
            // Protezione Salute Più
            //
            // TCM + IPT + Grandi Interventi
            ['product_id'=> 8, 'coverage_id' => 16, 'option' => 1, ],
            ['product_id'=> 8, 'coverage_id' => 22, 'option' => 1, ],
            ['product_id'=> 8, 'coverage_id' => 23, 'option' => 1, ],
            ['product_id'=> 8, 'coverage_id' => 27, 'option' => 1, ],
            ['product_id'=> 8, 'coverage_id' => 28, 'option' => 1, ],

            //
            // Vivendo 4 100
            //
            // TCM + IPT + ITT
            ['product_id'=> 9, 'coverage_id' => 15, 'option' => 1, ],
            ['product_id'=> 9, 'coverage_id' => 22, 'option' => 1, ],
            ['product_id'=> 9, 'coverage_id' => 23, 'option' => 1, ],
            ['product_id'=> 9, 'coverage_id' => 24, 'option' => 1, ],
            ['product_id'=> 9, 'coverage_id' => 25, 'option' => 1, ],
            // TCM + IPT + ITT + PII
            ['product_id'=> 9, 'coverage_id' => 15, 'option' => 2, ],
            ['product_id'=> 9, 'coverage_id' => 22, 'option' => 2, ],
            ['product_id'=> 9, 'coverage_id' => 23, 'option' => 2, ],
            ['product_id'=> 9, 'coverage_id' => 24, 'option' => 2, ],
            ['product_id'=> 9, 'coverage_id' => 25, 'option' => 2, ],
            ['product_id'=> 9, 'coverage_id' => 14, 'option' => 2, ],

            //
            // Incendio Mutui Evolution
            //
            // Solo Incendio
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 1, ],
            // Incendio + RC Terzi
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 2, ],
            ['product_id'=> 20, 'coverage_id' => 8, 'option' => 2, ],
            // Opz.3: Incendio + Eventi sociopolitici + Acqua condotta + Fenomeno elettrico
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 3, ],
            ['product_id'=> 20, 'coverage_id' => 10, 'option' => 3, ],
            ['product_id'=> 20, 'coverage_id' => 40, 'option' => 3, ],
            ['product_id'=> 20, 'coverage_id' => 2, 'option' => 3, ],
            // Opz.4: Incendio + Eventi sociopolitici + Acqua condotta + Fenomeno elettrico + Ricorso terzi da incendio
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 4, ],
            ['product_id'=> 20, 'coverage_id' => 10, 'option' => 4, ],
            ['product_id'=> 20, 'coverage_id' => 40, 'option' => 4, ],
            ['product_id'=> 20, 'coverage_id' => 2, 'option' => 4, ],
            ['product_id'=> 20, 'coverage_id' => 8, 'option' => 4, ],
            // Opz.5: Incendio + Eventi sociopolitici + Acqua condotta + Fenomeno elettrico + Eventi atmosferici
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 5, ],
            ['product_id'=> 20, 'coverage_id' => 10, 'option' => 5, ],
            ['product_id'=> 20, 'coverage_id' => 40, 'option' => 5, ],
            ['product_id'=> 20, 'coverage_id' => 2, 'option' => 5, ],
            ['product_id'=> 20, 'coverage_id' => 9, 'option' => 5, ],
            // Opz.6: Incendio + Eventi sociopolitici + Acqua condotta + Fenomeno elettrico + Ricorso terzi da incendio + Eventi atmosferici
            ['product_id'=> 20, 'coverage_id' => 1, 'option' => 6, ],
            ['product_id'=> 20, 'coverage_id' => 10, 'option' => 6, ],
            ['product_id'=> 20, 'coverage_id' => 40, 'option' => 6, ],
            ['product_id'=> 20, 'coverage_id' => 2, 'option' => 6, ],
            ['product_id'=> 20, 'coverage_id' => 8, 'option' => 6, ],
            ['product_id'=> 20, 'coverage_id' => 9, 'option' => 6, ],

            // AFI ESCA VIVENDO 
            // TCM
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 1, ],
            // TCM + IPT
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 2, ],
            ['product_id'=> 23, 'coverage_id' => 22, 'option' => 2, ],
            ['product_id'=> 23, 'coverage_id' => 23, 'option' => 2, ],
            // TCM + ITT
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 3, ],
            ['product_id'=> 23, 'coverage_id' => 24, 'option' => 3, ],
            ['product_id'=> 23, 'coverage_id' => 25, 'option' => 3, ],
            // TCM + IPT + ITT
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 4, ],
            ['product_id'=> 23, 'coverage_id' => 22, 'option' => 4, ],
            ['product_id'=> 23, 'coverage_id' => 23, 'option' => 4, ],
            ['product_id'=> 23, 'coverage_id' => 24, 'option' => 4, ],
            ['product_id'=> 23, 'coverage_id' => 25, 'option' => 4, ],
            // TCM + ITT + PII
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 5, ],
            ['product_id'=> 23, 'coverage_id' => 24, 'option' => 5, ],
            ['product_id'=> 23, 'coverage_id' => 25, 'option' => 5, ],
            ['product_id'=> 23, 'coverage_id' => 14, 'option' => 5, ],
            // TCM + ITT + IPT + PII
            ['product_id'=> 23, 'coverage_id' => 15, 'option' => 6, ],
            ['product_id'=> 23, 'coverage_id' => 24, 'option' => 6, ],
            ['product_id'=> 23, 'coverage_id' => 25, 'option' => 6, ],
            ['product_id'=> 23, 'coverage_id' => 22, 'option' => 6, ],
            ['product_id'=> 23, 'coverage_id' => 23, 'option' => 6, ],
            ['product_id'=> 23, 'coverage_id' => 14, 'option' => 6, ],

        ];

        // Return by productId
        return array_filter($data, function($item) use ($productId) { return $item['product_id'] === $productId; });
    }
}