<?php namespace Database\Seeders;

use App\Models\NetworkNode;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DoroteaNetworkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (app('currentTenant')->name == 'dorotea') {

            $root = new NetworkNode();
            $root->code = 'Dorotea';
            $root->name = 'Dorotea';
            $root->save();

            $pmc = new NetworkNode();
            $pmc->code = 'PMC';
            $pmc->name = 'Più Mutui Casa';
            $pmc->save();
            $root->appendNode($pmc);
        }
    }
}
