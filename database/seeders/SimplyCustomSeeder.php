<?php namespace Database\Seeders;

use App\Models\Address;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Product;
use App\Models\Role;
use App\Models\User;
use Database\Factories\UserFactory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Pdf\PdfProcessorDN;

class SimplyCustomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (app('currentTenant')->name == 'simply') {
            
            Product::find(1)->update(['enabled' => false]);
            Product::find(3)->update(['enabled' => false]);
            Product::find(4)->update(['enabled' => false]);
            Product::find(5)->update(['enabled' => false]);
            Product::find(6)->update(['enabled' => false]);
            Product::find(7)->update(['enabled' => false]);
            Product::find(9)->update(['enabled' => false]);
            Product::find(10)->update(['enabled' => false]);
            Product::find(11)->update(['enabled' => false]);
            Product::find(12)->update(['enabled' => false]);
            Product::find(13)->update(['enabled' => false]);
            Product::find(14)->update(['enabled' => false]);
            Product::find(15)->update(['enabled' => false]);
            Product::find(16)->update(['enabled' => false]);
            Product::find(17)->update(['enabled' => false]);
            Product::find(18)->update(['enabled' => false]);
            Product::find(19)->update(['enabled' => false]);

            if (env('APP_ENV') === 'local') {
                $role = Role::where('name', 'salesman')->first();

                $users = User::factory()
                    ->count(15)
                    ->create([
                        'node_id' => 1,
                        'active' => 1,
                    ]);

                // Associa il ruolo tramite la tabella pivot
                foreach ($users as $user) {
                    $user->roles()->attach($role->id);
                }
            }
        }
    }
}
