<?php namespace Database\Seeders;

use App\Models\Address;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Product;
use App\Models\Rui;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Pdf\PdfProcessorDN;

class SimplyNetworkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (app('currentTenant')->name == 'simply') {

            //
            // Network
            //

            $rui = Rui::factory()->create([
                'section' => 'B',
                'code' => '000540905',
                'subscribed_at' => \Carbon\Carbon::createFromFormat('d M Y', '25 Jan 2016'),
            ]);
            
            $root = new NetworkNode();
            $root->rui_id = $rui->id; 
            $root->code = 'SIMPLY';
            $root->name = 'Simply Broker';
            $root->save();

            $rui = Rui::factory()->create([
                'section' => 'E',
                'code' => '000233503',
                'subscribed_at' => \Carbon\Carbon::createFromFormat('d M Y', '14 Dec 2007'),
            ]);

            $network = new NetworkNode();
            $network->rui_id = $rui->id;
            $network->code = 'EUROANSA';
            $network->name = 'Euroansa';
            $network->save();
            $root->appendNode($network);
        }
    }
}
