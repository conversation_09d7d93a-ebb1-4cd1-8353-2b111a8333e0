<?php namespace Database\Seeders;

use App\Models\NetworkNode;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DoroteaCustomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (app('currentTenant')->name == 'dorotea') {

            if (env('APP_ENV') != 'production') {
                DB::connection('tenant')->table('egg_products')->insert([
                    ['id' => 283, 'product_id'=> 12, 'field' => 'c__cfincendio'],
                    ['id' => 284, 'product_id'=> 13, 'field' => 'c__cfincuca2'],
                    ['id' => 281, 'product_id'=> 2, 'field' => 'c__cfrataprot'],
                    ['id' => 282, 'product_id'=> 3, 'field' => 'c__cf6copeasy'],
                    ['id' => 285, 'product_id'=> 4, 'field' => 'c__cfnicelife'],
                    ['id' => 286, 'product_id'=> 5, 'field' => 'c__cfprotmax'],
                    ['id' => 199, 'product_id'=> 6, 'field' => 'c__cfvitaacol'],
                    ['id' => 263, 'product_id'=> 7, 'field' => 'c__netinevent'],
                    ['id' => 197, 'product_id'=> 8, 'field' => 'c__axasalutep'],
                    ['id' => 58, 'product_id'=> 9, 'field' => 'c__afivivquat'],
                    ['id' => 196, 'product_id'=> 10, 'field' => 'c__afivivelit'],

                    // @FIXME! Egg codes (product split)
                    ['id' => 271, 'product_id'=> 11, 'field' => 'c__groupcasc'],

                    ['id' => 289, 'product_id'=> 15, 'field' => 'c__nobcsmbase'],
                    ['id' => 290, 'product_id'=> 16, 'field' => 'c__nobcsmplus'],
                ]);

                $pmc = NetworkNode::where('code', 'PMC')->first();

                $user = new User();

                $user->forceFill([
                    'password' => Hash::make(\Str::random(32)),
                    'node_id' => $pmc->id,
                    'name' => fake()->name(), 
                    'lastname' => fake()->lastName(), 
                    'email' => fake()->email(), 
                    'active' => 1, 
                    'egg_id' => 'fake0',
                ])->save();
                $user->addRole('salesman');

                $user = new User();

                $user->forceFill([
                    'password' => Hash::make(\Str::random(32)),
                    'node_id' => $pmc->id,
                    'name' => fake()->name(), 
                    'lastname' => fake()->lastName(), 
                    'email' => fake()->email(), 
                    'active' => 1, 
                    'egg_id' => 'fake1',
                ])->save();
                $user->addRole('manager');

                $user->save();


            } else {
                DB::connection('tenant')->table('egg_products')->insert([
                    ['id' => 301, 'product_id'=> 12, 'field' => 'c__cfincendio'],
                    ['id' => 302, 'product_id'=> 13, 'field' => 'c__cfincuca2'],
                    ['id' => 306, 'product_id'=> 2, 'field' => 'c__cfrataprot'],
                    ['id' => 309, 'product_id'=> 3, 'field' => 'c__cf6copeasy'],
                    ['id' => 308, 'product_id'=> 4, 'field' => 'c__cfnicelife'],
                    ['id' => 307, 'product_id'=> 5, 'field' => 'c__cfprotmax'],
                    ['id' => 199, 'product_id'=> 6, 'field' => 'c__cfvitaacol'],
                    ['id' => 263, 'product_id'=> 7, 'field' => 'c__netinevent'],
                    ['id' => 305, 'product_id'=> 8, 'field' => 'c__axasalutep'],
                    ['id' => 58, 'product_id'=> 9, 'field' => 'c__afivivquat'],
                    ['id' => 196, 'product_id'=> 10, 'field' => 'c__afivivelit'],
                    
                    // @FIXME! Egg codes (product split)
                    ['id' => 271, 'product_id'=> 11, 'field' => 'c__groupcasc'],
                    
                    ['id' => 329, 'product_id'=> 15, 'field' => 'c__nobcsmbase'],
                    ['id' => 330, 'product_id'=> 16, 'field' => 'c__nobcsmplus'],
                ]);
            }

            // Disable product.
            Product::find(1)->update(['enabled' => false]);
            Product::find(3)->update(['enabled' => false]);
            Product::find(4)->update(['enabled' => false]);
            Product::find(5)->update(['enabled' => false]);
            Product::find(11)->update(['enabled' => false]);
        }
    }
}
