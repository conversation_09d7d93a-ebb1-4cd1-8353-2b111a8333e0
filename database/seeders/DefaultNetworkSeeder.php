<?php namespace Database\Seeders;

use App\Models\Address;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\NetworkNode;
use App\Models\Person;
use App\Models\Product;
use App\Models\Rui;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Upnovation\Easyprofile\Modules\Documents\DocumentsInstaller;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Pdf\PdfProcessorDN;

class DefaultNetworkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (in_array(app('currentTenant')->name, ['default', 'tenant1', 'tenant2'])) {

            //
            // Network
            //

            $rui = Rui::factory()->create();
            
            $root = new NetworkNode();
            $root->rui_id = $rui->id; 
            $root->code = 'Default Broker';
            $root->name = 'Default Broker';
            $root->save();

            $rui = Rui::factory()->create();

            $network = new NetworkNode();
            $network->rui_id = $rui->id;
            $network->code = 'NET1';
            $network->name = 'Rete 1';
            $network->save();
            $root->appendNode($network);

            $rui = Rui::factory()->create();

            $area = new NetworkNode();
            // same rui as the network
            $area->rui_id = $rui->id;
            $area->code = 'AREA1';  
            $area->name = 'Area 1';
            $area->save();
            $network->appendNode($area);

            $pos1 = new NetworkNode();
            // same rui as the network
            $area->rui_id = $rui->id;
            $pos1->code = 'POS1';
            $pos1->name = 'Punto Vendita 1';
            $pos1->save();
            $area->appendNode($pos1);

            // make pos2
            $pos2 = new NetworkNode();
            // same rui as the network
            $area->rui_id = $rui->id;
            $pos2->code = 'POS2';
            $pos2->name = 'Punto Vendita 2';
            $pos2->save();
            $area->appendNode($pos2);
        }
    }
}
