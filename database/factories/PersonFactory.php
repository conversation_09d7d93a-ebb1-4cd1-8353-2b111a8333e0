<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Person>
 */
class PersonFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => fake()->name,
            'lastname' => fake()->lastname,
            'email' => fake()->unique()->safeEmail,
            'phone' => fake()->phoneNumber,
            'taxCode' => fake()->unique()->numerify('#########'),
            'birthdate' => fake()->dateTimeBetween('-50 years', '-18 years'),
            'birthplaceCountry' => fake()->country,
            'birthplaceCity' => fake()->city,
            'birthplaceProvince' => fake()->stateAbbr,
            'documents' => [],
        ];
    }
}
