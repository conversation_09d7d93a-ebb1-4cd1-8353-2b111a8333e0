<?php

namespace Database\Factories;

use App\Models\Rui;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class NetworkNodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'rui_id' => Rui::factory(), 
            'code' => fake()->uuid(),
            'name' => fake()->text('32'),
        ];
    }
}
