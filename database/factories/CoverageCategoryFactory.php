<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CoverageCategory>
 */
class CoverageCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->category(),
            'branch' => ['non-life', 'life'][rand(0,1)],
        ];
    }

    protected function category() : string {
        return [
            'Protezione Beni',
            'Protezione Persona',
            'Protezione Reddito',
        ][rand(0, 1)];
    }
}
