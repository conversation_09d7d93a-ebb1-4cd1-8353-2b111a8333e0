<?php

namespace Database\Factories;

use App\Models\CoverageCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Coverage>
 */
class CoverageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => $this->coverage(),
            'label' => fake()->text(16),
            'category_id' => CoverageCategory::factory(),
            'shortname' => fake()->text(),
            'target' => 'retail',
        ];
    }

    protected function coverage() : string 
    {
        $coverages = [
            "RC Auto",
            "Responsabilità Civile",
            "Spese Sanitarie",
            "Long term care",
            "Incendio Abitazione",
        ];

        return $coverages[rand(0,3)];
    }
}
