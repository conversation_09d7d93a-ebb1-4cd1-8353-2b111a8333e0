<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Enterprise>
 */
class EnterpriseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'name' => fake()->company,
            'vat' => fake()->unique()->numerify('#########'),
            'legalForm' => fake()->randomElement(['srl', 'spa', 'sas', 'snc', 'srls',]),
        ];
    }
}
