<?php

namespace Database\Factories;

use App\Models\Company;
use App\Models\Coverage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'company_id' => Company::factory(),
            'name' => ucfirst(fake()->word()) . " " . ucfirst(fake()->word()),
            'companyProductId' => null,
        ];
    }
}
