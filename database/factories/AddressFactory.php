<?php

namespace Database\Factories;

use App\Models\Zip;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class AddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $zip = Zip::factory()->create();

        return [
            'zip' => $zip->cap,
            'type' => fake()->randomElement(['birthplace', 'residence', 'headquarters', 'risk']),
            'street' => fake()->streetName,
            'number' => fake()->buildingNumber,
            'country' => 'IT',
        ];
    }
}
