<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class JobFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'id' => fake()->uuid(),
            'collab' => fake()->name(),
            'opened_at' => fake()->date(),
            'closed_at' => fake()->date(),
            'client' => fake()->name(),
            'state' => 'new',
            'actions' => []
        ];
    }

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function salesmen()
    {
        return [
            'id' => fake()->uuid(),
            'name' => fake()->name(),
            'network' => fake()->company(),
            'active' => fake()->boolean(),
        ];
    }

}
