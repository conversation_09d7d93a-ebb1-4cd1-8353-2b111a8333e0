<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pipeline>
 */
class PipelineFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $data = [
            //'id' => fake()->uuid(),
            'user_id' => User::factory(),
            //'stage' => 'open', 
        ];

        if (app('currentTenant')->name == 'dorotea') {
            $data['egg_opportunity_id'] = fake()->uuid();
        }

        return $data;
    }
}
