<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Address>
 */
class ZipFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $fakeCity = fake()->city;
        
        return [
            'codice_istat' => fake()->unique()->numerify('########'),
            'denominazione_ita_altra' => $fakeCity,
            'denominazione_ita' => $fakeCity,
            'denominazione_altra' => "",
            'cap' => fake()->postcode,
            'sigla_provincia' => fake()->stateAbbr,
            'denominazione_provincia' => fake()->state,
            'tipologia_provincia' => 'città',
            'codice_regione' => fake()->numberBetween(1, 20),
            'denominazione_regione' => 'regione',
            'tipologia_regione' => 'tipo regione',
            'ripartizione_geografica' => 'test',
            'flag_capoluogo' => 'NO',
            'codice_belfiore' => 'xyz',
            'lat' => fake()->latitude,
            'lon' => fake()->longitude,
            'superficie_kmq' => fake()->randomFloat(2, 1, 1000),
        ];
    }
}
