<?php

namespace Database\Factories;

use App\Models\Pipeline;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Task>
 */
class TaskFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            //'id' => fake()->uuid(),
            'pipeline_id' => Pipeline::factory(),
            'priority' => 0,
            'type' => 'client',
            'state' => 'open',
            'navigation' => 'always',
            'manager' => 'Foo',
            'controller' => 'Bar',
            'template' => 'Baz',
            'displayName' => 'display name',
        ];
    }
}
