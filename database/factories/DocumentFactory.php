<?php

namespace Database\Factories;

use App\Models\File;
use App\Models\NetworkNode;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Document>
 */
class DocumentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'node_id' => 1,
            'file_id' => File::factory(),
            'title' => fake()->text('32'),
            'type' => 'all-3',
            'version' => '1.0.0',
            'description' => 'This is the first document',
        ];
    }

    public function randomType()
    {
        return fake()->randomElement(config('easyprofile.modules.documents.docTypes'));
    }
}
