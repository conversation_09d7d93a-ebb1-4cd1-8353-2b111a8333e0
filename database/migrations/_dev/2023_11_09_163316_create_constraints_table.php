<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('constraints', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->enum('type', ['age', 'length', 'job']);
            $table->text('values');
        });

        Schema::create('constraint_product', function (Blueprint $table) {
            $table->integer('product_id')->unsigned();
            $table->integer('constraint_id')->unsigned();

            $table->primary(['product_id', 'constraint_id']);
            
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('constraint_id')->references('id')->on('constraints');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('constraint_product');
        Schema::dropIfExists('constraints');
    }
};
