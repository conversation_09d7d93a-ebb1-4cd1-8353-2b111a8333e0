<?php

use App\Models\NetworkNode;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Setup network.
        $root = new NetworkNode();
        $root->code = '<PERSON>rote<PERSON>';
        $root->name = '<PERSON><PERSON><PERSON>';
        $root->save();

        $pmc = new NetworkNode();
        $pmc->code = 'PMC';
        $pmc->name = 'Più Mutui Casa';
        $pmc->save();
        $root->appendNode($pmc);

        $users = User::whereHas('roles', function($query) {
            $query->whereName('salesman');
        })->get();

        $users->each(function($user) use ($pmc) {
            $user->node_id = $pmc->id;
            $user->save();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Delete all network nodes.
        NetworkNode::whereNotNull('id')->delete();
    }
};
