<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('company_id')->unsigned();
            $table->boolean('enabled')->default(true);
            $table->string('companyProductId')->nullable();
            $table->string('code')->unique();
            $table->string('name')->unique();
            $table->smallInteger('minAge')->nullable();
            $table->smallInteger('maxAge')->nullable();
            $table->smallInteger('minLen')->nullable();
            $table->smallInteger('maxLen')->nullable();
            $table->text('warnings')->nullable();

            $table->foreign('company_id')->references('id')->on('companies');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
};
