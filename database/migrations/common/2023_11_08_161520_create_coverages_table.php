<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coverages', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->integer('category_id')->unsigned();
            $table->string('label')->unique();
            $table->string('shortname')->nullable();
            $table->string('name');
            $table->enum('type', ['main', 'complementary']);
            $table->enum('target', ['retail', 'corporate']);

            $table->foreign('category_id')->references('id')->on('coverage_categories');
        });

        Schema::create('coverage_product', function (Blueprint $table) {
            $table->integer('product_id')->unsigned();
            $table->integer('coverage_id')->unsigned();

            $table->enum('setup', ['standard', 'optional']);
            
            // KEEP AS PLAN B <----------------------------------------
            // True: coverage belongs to options and cannot be sold separately
            // False: coverage can be sold separately
            
            // @TODO: remove, not used anymore.
            //---------------------------------------------------------
            $table->boolean('inOptions')->default(false);

            $table->float('weight')->unsigned();
            $table->text('requiredJobs')->nullable();
            $table->text('excludedJobs')->nullable();

            $table->primary(['product_id', 'coverage_id']);

            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('coverage_id')->references('id')->on('coverages');
        });

        Schema::create('coverage_options', function (Blueprint $table) {
            $table->integer('product_id')->unsigned();
            $table->integer('coverage_id')->unsigned();
            $table->smallInteger('option');
 
            $table->primary(['product_id', 'coverage_id', 'option']);

            // So that when a coverage is removed from product, the relative option is removed too.
            $table->foreign(['product_id', 'coverage_id'])->references(['product_id', 'coverage_id'])->on('coverage_product')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coverage_options');
        Schema::dropIfExists('coverage_product');
        Schema::dropIfExists('coverages');
    }
};
