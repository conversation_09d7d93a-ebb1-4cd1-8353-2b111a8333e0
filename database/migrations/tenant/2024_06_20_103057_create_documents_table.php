<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->integer('node_id')->unsigned()->nullable();
            //$table->integer('file_id')->unsigned();
            $table->string('title');
            $table->enum('type', config('easyprofile.modules.documents.docTypes'));
            $table->string('version');
            $table->text('description');
            $table->string('processor')->nullable()->default(null);
            $table->json('signers')->nullable()->default(null);
            $table->json('signatures')->nullable()->default(null);
            $table->json('overlays')->nullable()->default(null);
            $table->timestamps();

            $table->foreign('node_id')->references('id')->on('network_nodes')->onDelete('set null');
            //$table->foreign('file_id')->references('id')->on('files')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('documents');
    }
};
