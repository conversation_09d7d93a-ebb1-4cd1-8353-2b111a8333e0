<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pipelines', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('user_id')->unsigned();
            //$table->bigInteger('client_id')->unsigned()->nullable()->default(null);
            $table->enum('state', ['open', 'closed', 'canceled'])->default('open');
            $table->timestamps();
            $table->dateTime('closed_at')->nullable();

            $table->foreign('user_id')->references('id')->on('users');
            //$table->foreign('client_id')->references('id')->on('clients');
        });

        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('pipeline_id')->unsigned();
            $table->smallInteger('priority')->unsigned();
            $table->string('type');
            $table->enum('state', ['open', 'progress', 'closed'])->default('open');
            $table->string('dependson')->nullable();
            $table->string('navigation');
            $table->boolean('accessible')->default(false);
            $table->string('stateData')->nullable()->default(null); // i.e. optional JSON state data
            $table->string('manager'); 
            $table->string('controller'); 
            $table->string('template'); 
            $table->timestamps();

            $table->foreign('pipeline_id')->references('id')->on('pipelines')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tasks');
        Schema::dropIfExists('pipelines');
    }
};
