<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('profiles', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('client_id')->unsigned();
            $table->bigInteger('pipeline_id')->unsigned();

            $table->enum('status', ['married', 'single', 'divorced', 'widow'])->nullable();
            $table->enum('choice', ['free', 'constrained'])->nullable();
            $table->string("currentExpense")->nullable()->default(null);

            $table->string("job")->nullable()->default(null);
            $table->string("customJob")->nullable()->default(null);
            $table->string('length')->nullable()->default(null);
            //$table->enum('length', ['short', 'medium', 'long'])->nullable()->default(null);

            // @TODO: link with categories?
            $table->boolean('areaHouse')->nullable()->default(false);
            $table->boolean('areaAssets')->nullable()->default(false);
            $table->boolean('areaPerson')->nullable()->default(false);

            $table->tinyInteger('familyMembers')->nullable()->default(1);
            $table->tinyInteger('familyEarners')->nullable()->default(1);
            $table->tinyInteger('cohabitants')->nullable()->default(0);
            $table->tinyInteger('cohabitantsMinors')->nullable()->default(0);

            //$table->enum('realEstate', ['none', 'firstHouse', 'manyProperties'])->nullable();
            $table->string('realEstate')->nullable()->default(null);

            $table->boolean('smoker')->nullable()->default(false);
            $table->boolean('extremeSports')->nullable()->default(false);

            //$table->enum('annualBudget', ['small', 'medium', 'large'])->nullable();
            $table->string('budget')->nullable()->default(false);

            $table->boolean('mortgage')->nullable()->default(false);
            $table->boolean('currentInsuranceCar')->nullable()->default(false);
            $table->boolean('currentInsuranceHouse')->nullable()->default(false);
            $table->boolean('currentInsuranceInjury')->nullable()->default(false);
            $table->boolean('currentInsuranceIllness')->nullable()->default(false);
            $table->boolean('currentInsuranceDeath')->nullable()->default(false);
            $table->boolean('currentInsuranceRC')->nullable()->default(false);
            $table->boolean('currentInsuranceOther')->nullable()->default(false);

            $table->timestamps();
            $table->dateTime('closed_at')->nullable()->default(null);

            $table->foreign('client_id')->references('id')->on('clients');
            $table->foreign('pipeline_id')->references('id')->on('pipelines')->onDelete('cascade');
        });

        Schema::create('coverage_profile', function (Blueprint $table) {
            $table->bigInteger('profile_id')->unsigned();
            $table->integer('coverage_id')->unsigned();

            $table->primary(['profile_id', 'coverage_id']);

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
            $table->foreign('coverage_id')->references('id')->on('coverages');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coverage_profile');
        Schema::dropIfExists('profiles');
    }
};
