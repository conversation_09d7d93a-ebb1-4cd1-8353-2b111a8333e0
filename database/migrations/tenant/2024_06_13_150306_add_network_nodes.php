<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('network_nodes', function(Blueprint $table){
            $table->increments('id')->unsigned();
            $table->string('code')->unique();
            $table->string('name');
            $table->nestedSet();
            $table->timestamps();
        });

        Schema::table('users', function (Blueprint $table) {
            $table->integer('node_id')->unsigned()->nullable()->after('id');

            $table->foreign('node_id')->references('id')->on('network_nodes')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign('users_node_id_foreign');
            $table->dropColumn('node_id');
        });

        Schema::dropIfExists('network_nodes');
    }
};
