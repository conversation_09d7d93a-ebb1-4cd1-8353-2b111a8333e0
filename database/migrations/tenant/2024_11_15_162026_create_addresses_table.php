<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('addresses', function (Blueprint $table) {
            $table->id();

            // Address is either owned by person or enterprise.
            $table->unsignedBigInteger('person_id')->nullable();
            $table->unsignedBigInteger('enterprise_id')->nullable();
            
            $table->string('zip');
            $table->enum('type', ['birthplace', 'residence', 'risk', 'headquarters']);

            $table->string('street');
            $table->string('number')->nullable();
            
            $table->string('country')->default('IT');
            $table->timestamps();
            
            $table->foreign('zip')->references('cap')->on('gi_comuni_cap');
            $table->foreign('person_id')->references('id')->on('people')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('addresses');
    }
};
