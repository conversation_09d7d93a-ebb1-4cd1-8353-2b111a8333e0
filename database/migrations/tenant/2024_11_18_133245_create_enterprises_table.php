<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('enterprises', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('rep_id')->unsigned();
            $table->enum("legalForm", ['srl', 'spa', 'sas', 'snc', 'srls', 'cooperativa', 'associazione', 'fondazione', 'piva', 'piva_forfettaria']);
            $table->string("name");
            $table->string("vat");
            $table->timestamps();

            $table->foreign('rep_id')->references('id')->on('people');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('enterprises');
    }
};
