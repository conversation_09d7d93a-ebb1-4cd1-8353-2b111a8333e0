<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
         Schema::create('rui', function (Blueprint $table) {
            $table->id();
            $table->char('section');
            $table->string('code');
            $table->date('subscribed_at');
         });

         Schema::table('network_nodes', function (Blueprint $table) {
            $table->bigInteger('rui_id')->unsigned()->nullable()->after('id');

            $table->foreign('rui_id')->references('id')->on('rui')->onDelete('set null');
         });

         Schema::table('users', function (Blueprint $table) {
            $table->bigInteger('rui_id')->unsigned()->nullable()->after('node_id');

            $table->foreign('rui_id')->references('id')->on('rui')->onDelete('set null');
         });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('network_nodes', function (Blueprint $table) {
            $table->dropForeign(['rui_id']);
            $table->dropColumn('rui_id');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['rui_id']);
            $table->dropColumn('rui_id');
        });

        Schema::dropIfExists('rui');
    }
};
