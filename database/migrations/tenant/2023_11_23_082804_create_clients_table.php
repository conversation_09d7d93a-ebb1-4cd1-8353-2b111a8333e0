<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            
            // La relazione di "appartenenza" tra utente
            // e clienti è implementata dalla tabella
            // pipelines.
            // Mantenere questa relazione sulla tabella
            // clients non avrebbe permesso di legare
            // lo stesso cliente a più utenti diversi, 
            // e per evitare di usare una ulteriore tabella
            // di cross (user_client) abbiamo sfruttato
            // la tabella pipelines.
            //$table->bigInteger('user_id')->unsigned() non è piu necessaria

            //$table->enum("type", ['retail', 'corporate']);
            //$table->text("notes")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('clients');
    }
};
