<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('clients', function (Blueprint $table) {
            $table->bigInteger('pipeline_id')->unsigned()->nullable()->after('id');
            $table->bigInteger('person_id')->unsigned()->nullable()->after('pipeline_id');
            $table->bigInteger('enterprise_id')->unsigned()->nullable()->after('person_id');
            $table->string('name')->after('enterprise_id');
            $table->enum('role', ['contractor', 'insured'])->default('contractor')->after('name');

            $table->foreign('pipeline_id')->references('id')->on('pipelines');
            $table->foreign('person_id')->references('id')->on('people');
            $table->foreign('enterprise_id')->references('id')->on('enterprises');

            $table->unique(['pipeline_id', 'person_id', 'role']);
            $table->unique(['pipeline_id', 'enterprise_id', 'role']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // todo: migrate and drop pipelines.client_id


        Schema::table('clients', function (Blueprint $table) {
            $table->dropForeign(['pipeline_id']);
            $table->dropForeign(['person_id']);
            $table->dropForeign(['enterprise_id']);

            $table->dropColumn('pipeline_id');
            $table->dropColumn('person_id');
            $table->dropColumn('enterprise_id');
            $table->dropColumn('name');
            $table->dropColumn('role');
        });
    }
};
