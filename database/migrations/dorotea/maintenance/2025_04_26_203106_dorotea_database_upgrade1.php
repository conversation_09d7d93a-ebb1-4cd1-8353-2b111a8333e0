<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $data;

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //
        // STEP 1: Prepare database
        // Update people table according to Dorotea database
        // Alter tables (not allowed in transcactions).
        //

        Schema::table('people', function (Blueprint $table) {
            $table->string('egg_client_id')->nullable()->unique()->after('id'); 
        });   
        
        $connection = DB::connection('tenant');

        $connection->statement('ALTER TABLE people DROP COLUMN name;');
        $connection->statement('ALTER TABLE people DROP COLUMN lastname;');
        $connection->statement('ALTER TABLE people DROP COLUMN taxCode;');
        $connection->statement('ALTER TABLE people DROP COLUMN birthplace;');
        $connection->statement('ALTER TABLE people DROP COLUMN sex;');
        $connection->statement('ALTER TABLE people DROP COLUMN email;');
        $connection->statement('ALTER TABLE people DROP COLUMN pec;');
        $connection->statement('ALTER TABLE people DROP COLUMN phone;');
        $connection->statement('ALTER TABLE people DROP COLUMN iban;');
        $connection->statement('ALTER TABLE people DROP COLUMN sdi;');
        $connection->statement('ALTER TABLE people DROP COLUMN privacy_accepted_at;');

        // @README commenting the following line because:
        // - breaks dev:init
        // - this upgrade has already been launched in production and doesn't make sense anymore
        //$connection->statement('ALTER TABLE pipelines DROP FOREIGN KEY pipelines_client_id_foreign;');
        
        //$connection->statement('ALTER TABLE profiles DROP FOREIGN KEY profiles_client_id_foreign;');

        //
        // Open transaction.
        //
        $connection->beginTransaction();
        

        //
        // STEP 2: Create people
        // One person for each unique egg_client_id in clients.
        //

        /*

        $legacyClients = $connection
            ->table('clients')
            ->select('id', 'egg_client_id', 'birthdate', 'created_at', 'updated_at')
            ->distinct()
            ->get();

        foreach ($legacyClients as $clientRow) {
            $eggClientId = $clientRow->egg_client_id;

            // Weird but anyway
            if (! $eggClientId) {
                throw new \Exception("egg_client_id is empty.");
            }

            // Shouldn't be possible, but anyway
            $exists = $connection->table('people')->where('egg_client_id', $eggClientId)->exists();

            if (! $exists) {
                $connection->table('people')->insert([
                    'egg_client_id' => $eggClientId,
                    'birthdate' => $clientRow->birthdate,
                    'created_at' => $clientRow->created_at,
                    'updated_at' => $clientRow->updated_at,
                ]);
            }
        }

        //
        // STEP 3: Reverse pipelines - clients relationship
        //

        // STEP 3.A: Recreate clients with reverse fk.
        $pipelines = $connection->table('pipelines')
            ->join('clients as old_clients', 'pipelines.client_id', '=', 'old_clients.id')
            ->select('pipelines.id as pipeline_id', 'old_clients.egg_client_id', 'pipelines.created_at', 'pipelines.updated_at', 'pipelines.client_id as client_id', 'pipelines.state')
            ->get();

        foreach ($pipelines as $row) {
            $person = $connection->table('people')->where('egg_client_id', $row->egg_client_id)->first();

            if (! $person) {
                throw new \Exception("Person non trovata per egg_client_id: {$row->egg_client_id}");
            }

            $connection->table('clients')->insert([
                'pipeline_id' => $row->pipeline_id,
                'person_id' => $person->id,
                'name' => $person->egg_client_id,
                'role' => 'contractor',
                'created_at' => $row->created_at,
                'updated_at' => $row->updated_at,
                'updated_at' => now(),
            ]);   
        }

        $updatedClients = $connection->table('clients')
            ->whereNotIn('id', $legacyClients->pluck('id')->toArray())
            ->get();

        $people = $connection->table('people')->get();

        $this->checkData($pipelines, $people, $legacyClients, $updatedClients);
        
        // STEP 3.B: Drop legacy client records.
        $connection
            ->table('clients')
            ->whereIn('id', $legacyClients->pluck('id')->toArray())
            ->delete();

        $connection->commit();

        */

        //
        // STEP 3: Drop old columns, this must be outside the transaction because mysql doesn't support DDL in transactions
        //
        
        $connection->statement('ALTER TABLE pipelines DROP COLUMN client_id;');
        //$connection->statement('ALTER TABLE profiles DROP COLUMN client_id;');
        $connection->statement('ALTER TABLE clients DROP COLUMN egg_client_id;');
        //$connection->statement('ALTER TABLE clients DROP COLUMN birthdate;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // This migration is irreversible
    }

    public function checkData($pipelines, $people, $legacyClients, $updatedClients)
    {
        if ($updatedClients->count() != $pipelines->count()) {
            throw new \Exception("Bad clients count: abort.");
        }

        if ($legacyClients->count() != $people->count()) {
            throw new \Exception("Bad people count: abort.");
        }
        
        foreach($updatedClients as $client) {
            if (! $pipeline = DB::connection('tenant')->table('pipelines')->where('id', $client->pipeline_id)->first()) {
                throw new \Exception("Pipeline not found for client_id: {$client->id}");
            }   

            $legacyClient = DB::connection('tenant')
                ->table('clients')
                ->where('id', $pipeline->client_id)
                ->first();

            $person = DB::connection('tenant')
                ->table('people')
                ->where('id', $client->person_id)
                ->first();
            
            if ($legacyClient->egg_client_id != $person->egg_client_id) {
                throw new \Exception("Person/Client egg_client_id mismatch: C {$legacyClient->egg_client_id} != P {$person->egg_client_id}");
            }

            if ($legacyClient->birthdate != $person->birthdate) {
                throw new \Exception("Person/Client birthdate mismatch: C {$legacyClient->birthdate} != P {$person->birthdate}");
            }

            if ($client->name != $legacyClient->egg_client_id || $client->name != $person->egg_client_id) {
                throw new \Exception("Pipeline/Client name mismatch: P {$pipeline->name} != C {$legacyClient->egg_client_id}");
            }
        }
    }
};
