<?php

use App\Models\Company;
use App\Models\Coverage;
use App\Models\CoverageProduct;
use App\Models\EggProduct;
use App\Models\Product;
use Database\Seeders\Catalog;
use Database\Seeders\CatalogSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::connection('tenant')->beginTransaction();

        if (Coverage::whereLabel('vandalism')->doesntExist()) {
            Coverage::insert(Catalog::coverages('vandalism'));
        }
        
        if (Company::whereId(6)->doesntExist()) {
            Company::insert(Catalog::companies(6));
        }

        //
        // Nobis
        //
        // ========================================
        if (Product::whereId(15)->doesntExist()) {
            (new CatalogSeeder)->setupProduct(15);
        }

        if (Product::whereId(16)->doesntExist()) {
            (new CatalogSeeder)->setupProduct(16);
        }

        if (env('APP_ENV') != 'production') {
            if (EggProduct::whereId(289)->doesntExist()) {
                DB::connection('tenant')->table('egg_products')->insert([['id' => 289, 'product_id'=> 15, 'field' => 'c__nobcsmbase']]);
            }
            if (EggProduct::whereId(290)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 290, 'product_id'=> 16, 'field' => 'c__nobcsmplus']]);
            }
        } else {
            if (EggProduct::whereId(329)->doesntExist()) {
                DB::connection('tenant')->table('egg_products')->insert([['id' => 329, 'product_id'=> 15, 'field' => 'c__nobcsmbase']]);
            }
            if (EggProduct::whereId(330)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 330, 'product_id'=> 16, 'field' => 'c__nobcsmplus']]);
            }
        }

        // ========================================

        //
        // Groupama
        //
        // ========================================
        
        // Disable product 11
        Product::whereId(11)->update(['enabled' => false]);

        if (Product::whereId(17)->doesntExist()) {
            (new CatalogSeeder)->setupProduct(17);
        }

        if (Product::whereId(18)->doesntExist()) {
            (new CatalogSeeder)->setupProduct(18);
        }

        if (Product::whereId(19)->doesntExist()) {
            (new CatalogSeeder)->setupProduct(19);
        }

        if (env('APP_ENV') != 'production') {
            if (EggProduct::whereId(287)->doesntExist()) {
                DB::connection('tenant')->table('egg_products')->insert([['id' => 287, 'product_id'=> 17, 'field' => 'c__groupcsc1']]);
            }
            if (EggProduct::whereId(288)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 288, 'product_id'=> 18, 'field' => 'c__groupcsc2']]);
            }
            if (EggProduct::whereId(291)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 291, 'product_id'=> 19, 'field' => 'c__groupcsc3']]);
            }
        } else {
            if (EggProduct::whereId(341)->doesntExist()) {
                DB::connection('tenant')->table('egg_products')->insert([['id' => 341, 'product_id'=> 17, 'field' => 'c__groupcsc1']]);
            }
            if (EggProduct::whereId(342)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 342, 'product_id'=> 18, 'field' => 'c__groupcsc2']]);
            }
            if (EggProduct::whereId(343)->doesntExist()) {  
                DB::connection('tenant')->table('egg_products')->insert([['id' => 343, 'product_id'=> 19, 'field' => 'c__groupcsc3']]);
            }
        }

        // ========================================

        DB::connection('tenant')->commit();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Nobis
        EggProduct::whereId(289)->delete();
        EggProduct::whereId(290)->delete();
        EggProduct::whereId(329)->delete();
        EggProduct::whereId(330)->delete();

        // Groupama
        EggProduct::whereId(287)->delete();
        EggProduct::whereId(288)->delete();
        EggProduct::whereId(291)->delete();
        EggProduct::whereId(341)->delete();
        EggProduct::whereId(342)->delete();
        EggProduct::whereId(343)->delete();

        // Nobis
        CoverageProduct::whereProductId(15)->delete();
        CoverageProduct::whereProductId(16)->delete();

        // Groupama
        CoverageProduct::whereProductId(17)->delete();
        CoverageProduct::whereProductId(18)->delete();
        CoverageProduct::whereProductId(19)->delete();

        Product::whereId(15)->delete();
        Product::whereId(16)->delete();
        Product::whereId(17)->delete();
        Product::whereId(18)->delete();
        Product::whereId(19)->delete();
        
        Company::whereId(6)->delete();  
        Coverage::whereLabel('vandalism')->delete();
    }
};
