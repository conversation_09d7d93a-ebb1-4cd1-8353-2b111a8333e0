<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement('ALTER TABLE people MODIFY birthdate DATE NULL');
        DB::statement('ALTER TABLE people MODIFY documents DATE NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('ALTER TABLE people MODIFY birthdate DATE NOT NULL');
        DB::statement('ALTER TABLE people MODIFY documents DATE NOT NULL');
    }
};
