<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->bigInteger('client_id')->unsigned()->nullable()->after('user_id')->default(null);
            $table->string('egg_opportunity_id')->nullable()->after('state')->unique();

            $table->foreign('client_id')->references('id')->on('clients')->name('pipelines_client_id_foreign');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pipelines', function (Blueprint $table) {
            $table->dropColumn('egg_opportunity_id');
            $table->dropForeign(['client_id']);
            $table->dropColumn('client_id');
        });
    }
};
