{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.11", "kalnoy/nestedset": "^6.0", "laravel/fortify": "^1.18", "laravel/framework": "^9.19", "laravel/sail": "^1.26", "laravel/sanctum": "^3.0", "laravel/tinker": "^2.7", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.6", "setasign/fpdi_pdf-parser": "^2.1", "spatie/db-dumper": "^3.6", "spatie/laravel-multitenancy": "3.0", "upnovation/document-reader": "0.0.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/envoy": "^2.9", "laravel/pint": "^1.0", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "repositories": [{"type": "path", "url": "upnovation/easyprofile/src/DocumentReader"}, {"type": "composer", "url": "https://www.setasign.com/downloads/"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Upnovation\\Easyprofile\\": "upnovation/easyprofile/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}