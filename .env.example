APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:/Hoh8IKw1uvj9Fat/g73a8ejbH7rWbYKYFSM2VOC/RI=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=pleasedontshowthewarning
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=sail
DB_PASSWORD=password

#LANDLORD_DB_CONNECTION=landlord actually read from config/multitenancy.php
LANDLORD_DB_HOST=mysql
LANDLORD_DB_PORT=3306
LANDLORD_DB_DATABASE=laravel
LANDLORD_DB_USERNAME=sail
LANDLORD_DB_PASSWORD=password

#TENANT_DB_CONNECTION=tenant actually read from config/multitenancy.php
TENANT_DB_HOST=mysql
TENANT_DB_PORT=3306
TENANT_DB_DATABASE=null
TENANT_DB_USERNAME=ep_tenant
TENANT_DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_CONNECTION=tenant

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

EGG_ENDPOINT=https://finance.blackbird71.com/api/v2
EGG_CLIENTID=foo
EGG_SECRET=bar
EGG_USERNAME=baz
EGG_PASSWORD=foo.bar
EGG_REDIRECT=https://finance.blackbird71.com/a/index2.php?modulo=pratiche&azione=apri&opzione=%s
EGG_USER_QUERY='{\"entity\":\"user\",\"select\":[\"utenteID\",\"nomevisualizzato\",\"nome\",\"cognome\",\"ruolo\",\"c__NomeFinPoi\",\"email1\",\"ruolo_accesso\",\"tipo\",\"limita_prodotto\"],\"groupBy\":null,\"aggregation\":null,\"where\":[{\"ruolo_accesso\":\"Si\"},{\"email1\":{\"$isEmpty\":false}},{\"limita_prodotto\":{\"$contains\":\"Assicurazione Generica\"}},{\"tipo\":{\"$match\":\"Direttori Sede|Tutor|K2\"}}],\"sort\":\"tipo:asc\",\"skip\":0,\"limit\":1000}'

DOROTEA_MAX_USERS=350
DOROTEA_MAX_PIPELINES=1000
DOROTEA_CREATE_USERS_ON_LOGIN=true
DOROTEA_ABORT_SYNC_ON_ERROR=false
DOROTEA_LOGIN_IPS=*******,*******

EGG_TESTING_TOKEN=7327821625-3635031418-2942868279
EGG_TESTING_USERID=1030

VITE_STAGING_KEY=PrnzAAkMkNfTlbtEfDBGQ1CXtHK4NSxl