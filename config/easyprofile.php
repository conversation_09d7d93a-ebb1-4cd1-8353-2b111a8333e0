<?php

use App\Events\PrivacySigned;
use App\Models\Enterprise;
use App\Models\Person;
use App\Models\Rules\ExistsInTenant;
use App\Models\Rules\UniqueInTenant;
use App\Models\User;
use App\Models\Zip;
use Illuminate\Validation\Rule;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
use Upnovation\Easyprofile\Signature\IGSignClient;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;

return [
    'mapperLog' => true,
    
    'templateOverrides' => [
        'dorotea' => [
            'Dashboard' => 'Dorotea/Dashboard',
            'Clients' => 'Dorotea/Clients',
            'Salesmen' => 'Dorotea/Salesmen'
        ],
    ],

    // @TODO
    'authOverrides' => [
        'dorotea' => '',
    ],

    'clients' => [
        'dorotea' => [
            //'model' => App\Models\Dorotea\Client::class,
        ],
    ],

    'modules' => [
        'documents' => [
            'enabledTenants' => 'default,simply',
            'docTypes' => ['all-3', 'all-4', 'mup', 'privacy', 'demands-and-needs', 'consent-digital-sending', 'receipt-statement', 'assignment', 'product-form', 'product-policy'],
        ],

        'signature' => [
            'enabledTenants' => 'default,simply',
        ]
    ],

    'clientProfileExpirationInDays' => env('CLIENT_PROFILE_EXPIRATION_IN_DAYS', 30),

    'tenants' => [
        'default' => [
            'ui' => [
                // *** Must be declared in MasterLayout components ***
                'header' => "Header",
                'navigation' => "Navigation",
            ],

            'tasks' => [
                'client' => [
                    'rules' => [
                        // Custom rules here
                    ]
                ],
                'survey' => [
                    'rules' => [
                        'client/birthdate' => 'required|date_format:Y-m-d',
                        "profile/familyMembers" => 'required|integer',
                        "profile/familyEarners" => 'required|integer',
                        "profile/cohabitants" => 'required|integer',
                        "profile/cohabitantsMinors" => 'required|integer',
                        //
                    ]
                ]
            ],

            'allowedIP' => ['**********',],
        ],

        'simply' => [
            'maxUsers' => env('__TODO__MAX_USERS'),
            'maxPipelines' => env('__TODO__MAX_PIPELINES'),
        ],

        'dorotea' => [
            // @TODO refactor to global (just above) templateOverrides config value
            'ui' => [
                // *** Must be declared in MasterLayout components ***
                'header' => "HeaderDorotea"
            ],
            'maxUsers' => env('DOROTEA_MAX_USERS'),
            'createUserOnLogin' => env('DOROTEA_CREATE_USERS_ON_LOGIN'),
            'abortSyncUsersOnError' => env('DOROTEA_ABORT_SYNC_ON_ERROR'),
            'maxPipelines' => env('DOROTEA_MAX_PIPELINES'),
            'allowedIP' => explode(",", env('DOROTEA_LOGIN_IPS')),

            'tasks' => [
                'survey' => [
                    'rules' => [
                        'client/birthdate' => 'required|date_format:Y-m-d|after:date0|before:date1',
                        'profile/status' => 'required|in:married,single,divorced,widow',
                        "profile/familyMembers" => 'required|integer',
                        "profile/familyEarners" => 'required|integer',
                        "profile/cohabitants" => 'required|integer',
                        "profile/cohabitantsMinors" => 'required|integer',
                        "profile/realEstate" => 'required',
                        "profile/smoker" => 'required',
                        "profile/extremeSports" => 'required',
                        "profile/job" => 'required',

                        // @FIXME
                        //"profile.job.other" => 'required',

                        //"profile/areaAssets" => 'required',
                        //"profile/areaHouse" => 'required',
                        //"profile/areaPerson" => 'required',
                        "profile/budget" => 'required',
                        "profile/choice" => 'required',
                        /*"profile/currentInsuranceCar" => 'required',
                        "profile/currentInsuranceDeath" => 'required',
                        "profile/currentInsuranceHouse" => 'required',
                        "profile/currentInsuranceIllness" => 'required',
                        "profile/currentInsuranceInjury" => 'required',
                        "profile/currentInsuranceOther" => 'required',
                        "profile/currentInsuranceRC" => 'required',
                        "profile/currentMortgage" => 'required',*/
                        "profile/length" => 'required',
                        "profile/mortgage" => 'required',
                    ]
                ]
            ]
        ]
    ],

    'values' => [
        'tenants' => [
            'default' => [
                'jobs' => [
                    'job1' => 'asdasd',
                    'job2' => 'asdasd',
                ],
                'length' => [
                    'short' => [
                        'label' => "Breve termine (fino a 5 anni)",
                        'min' => 0,
                        'max' => 5,
                    ],
                    'medium' => [
                        'label' => "Medio termine (tra 6 e 10 anni)",
                        'min' => 6,
                        'max' => 10,
                    ],
                    'long' => [
                        'label' => "Lungo termine (oltre 10 anni)",
                        'min' => 10,
                        'max' => INF,
                    ],
                ]
            ],

            'dorotea' => [
                'jobs' => [
                    'job1' => 'asdasd',
                    'job2' => 'asdasd',
                ],
                'length' => [
                    'short' => [
                        'label' => "Breve termine (fino a 5 anni)",
                        'min' => 0,
                        'max' => 5,
                    ],
                    'medium' => [
                        'label' => "Medio termine (tra 6 e 10 anni)",
                        'min' => 6,
                        'max' => 10,
                    ],
                    'long' => [
                        'label' => "Lungo termine (oltre 10 anni)",
                        'min' => 10,
                        'max' => INF,
                    ],
                ]
            ]
        ],

            
    ],

    'rules' => [
        // This is for default rules NOT FOR DEFAULT TENANT!
        'default' => [
            'client' => [
                'person' => [
                    'person.name' => 'required|string|max:255',
                    'person.lastname' => 'required|string|max:255',
                    
                    // These rules need direct data injection so they are handled directly in controller.
                    /*'person.taxCode' => [
                        'required',
                        'string',
                        'max:16',
                        new UniqueInTenant(new Person(), 'taxCode'),
                    ],*/
                    /*'person.email' => [
                        'required',
                        'email',
                        new UniqueInTenant(new Person(), 'email'),
                    ],*/
                    /*'person.phone' => [
                        'required',
                        'digits_between:9,16',
                        new UniqueInTenant(new Person(), 'phone'),
                    ],*/
                    
                    'person.birthdate' => 'required|date_format:Y-m-d',

                    'person.birthplace.country' => 'required|string|max:255',
                    'person.birthplace.city' => 'required|string|max:255',
                    'person.birthplace.province' => 'required|string|max:255',

                    

                    'person.phonePrefix' => 'required|regex:/^\+\d{1,3}$/',

                    

                    'person.addresses.residence.type' => 'required|string|in:residence',
                    'person.addresses.residence.street' => 'required|string',
                    'person.addresses.residence.number' => 'required|string',
                    
                    'person.addresses.residence.zip' => [
                        'required',
                        'string',
                        new ExistsInTenant(new Zip(), 'cap'),
                    ],

                    'person.addresses.residence.city' => 'required|string',
                    'person.addresses.residence.province' => 'required|string',
                    'person.addresses.residence.region' => 'required|string',
                    //'person.addresses.residence.country' => 'required|string|size:2',
                ],

                'enterprise' => [
                    /*
                    'enterprise.name' => [
                        'required',
                        'string',
                        'max:255',
                        new UniqueInTenant(new Enterprise(), 'name'),
                    ],
                    
                    'enterprise.vat' => [
                        'required',
                        'string',
                        'size:11',
                        new UniqueInTenant(new Enterprise(), 'vat'),
                    ],
                    */

                    'enterprise.addresses.headquarters.type' => 'required|string|in:headquarters',
                    'enterprise.addresses.headquarters.street' => 'required|string',
                    'enterprise.addresses.headquarters.number' => 'required|string',

                    'enterprise.addresses.headquarters.zip' => [
                        'required',
                        'string',
                        new ExistsInTenant(new Zip(), 'cap'),
                    ],

                    'enterprise.addresses.headquarters.city' => 'required|string',
                    'enterprise.addresses.headquarters.province' => 'required|string',
                ],

                'identityDocument' => [
                    'person.documents.id.type' => 'required|string|in:id,passport,driving_license',
                    'person.documents.id.files' => 'required|array|min:1',
                    'person.documents.id.files.*' => [
                        'required',
                        'file',
                        'mimes:pdf,jpg,jpeg,png',
                        'max:2048', // 2MB
                    ],
                    'person.documents.id.number' => [
                        'required',
                        'string',
                        'max:255',
                    ],
                    'person.documents.id.issuer' => [
                        'required',
                        'string',
                        'max:255',
                    ],
                    'person.documents.id.issuerCountry' => [
                        'required',
                    ],
                    'person.documents.id.issuerDate' => [
                        'required',
                        'date_format:Y-m-d',
                    ],
                    'person.documents.id.expiry' => [
                        'required',
                        'date_format:Y-m-d',
                    ],

                ]
            ],
        ]
    ],
];