<?php

use App\Models\File;
use Dom\Text;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'assignment',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'mandato-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'assignment',
        'title' => "Mandato",
        'version' => '1.0.0',
        'description' => "Mandato di brokeraggio assicurativo",
        'processor' => PdfProcessor::class,
        'signers' => ['contractor'],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new SubjectOverlay(1, 40, 78, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['name', 'lastname'],
            ]),
            new SubjectOverlay(1, 40, 80, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 32, 89.5, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(1, 104, 89.5, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['vat'],
            ]),
            new SubjectOverlay(1, 62, 95, [
                'role' => 'contractor',
                'method' => 'getSubjectAddress',
            ]),
            new SubjectOverlay(1, 34, 100, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['phone'],
            ]),
            new SubjectOverlay(1, 103, 100, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['email'],
            ]),
            //new TextOverlay(1, 34, 171, [], '@placeholder'),
            new ArrayOverlay(1, 34, 171, [
                'key' => 'fee',
                'fmtCurrency' => true,
            ]),
        ],
    ],
];