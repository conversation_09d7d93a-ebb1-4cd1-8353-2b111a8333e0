<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'AXACFPROSAP',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'axa-protezione-salute-piu-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'AXA Protezione Salute Piu',
        'version' => '1.0.0',
        'description' => "Scheda Adesione AXA Protezione Salute Piu",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],

        //
        // This is not stored in the database anymore.
        //
        'overlayArray' => [
            new UserOverlay(1, 40, 42, ['properties' => ['name', 'lastname'],]),
            new TextOverlay(1, 144, 42, [], '@todo phone'),
            new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),
            new TextOverlay(1, 110, 64.5, [], "@todo"),

            
        ],
    ],

    'policy' => [
        'title' => 'Axa Protezione Salute Piu - Proposta di adesione',
        'signers' => ["contractor"], 
        'description' => "Proposta assicurativa da firmare.",
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    //
    // This defines the form data to be collected for this product and THIS document.
    // Defines the validation rules.
    //
    'formRules' =>[
        
    ],
];