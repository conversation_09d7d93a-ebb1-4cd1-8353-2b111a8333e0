<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'CFASEICOPES',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'sei-coperto-02-2024.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'CF Sei Coperto',
        'version' => '1.0.0',
        'description' => "Scheda Raccolta Dati CF Sei Coperto",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],

        //
        // This is not stored in the database anymore.
        //
        'overlayArray' => [
            new UserOverlay(1, 40, 42, ['properties' => ['name', 'lastname'],]),
            new TextOverlay(1, 144, 42, [], '@todo phone'),
            new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),
            new TextOverlay(1, 110, 64.5, [], "@todo"),

            // just a test here - configuration required.
            new RadioOverlay(1, 100, 70, [
                'key' => 'option',
                'options' => [
                    '5-1' => new TextOverlay(1, 67.5, 168.5, [], "x"),
                    '5-2' => new TextOverlay(1, 76, 100.5, [], "x"),
                    '5-3' => new TextOverlay(1, 80, 75, [], "x"),
                    '5-4' => new TextOverlay(1, 90, 75, [], "x"),
                ],
            ]),
            
        ],
    ],

    'policy' => [
        'title' => 'CF Sei Coperto - Proposta assicurativa',
        'signers' => ["contractor"], 
        'description' => "Proposta assicurativa da firmare.",
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    //
    // This defines the form data to be collected for this product and THIS document.
    // Defines the validation rules.
    //
    'formRules' =>[
        'option' => 'required|string', // es: "5-1" oppure "10-2"
        'applyFee' => 'required|boolean',
        'fee' => 'nullable|numeric|min:0|required_if:applyFee,true',
        'health.hospitalization' => 'required|boolean',
        'health.hospitalizationW' => 'required|boolean',
        'health.pathologies' => 'required|boolean',
        'health.void' => 'required|boolean',
        'health.disability' => 'required|boolean',
    ],
];