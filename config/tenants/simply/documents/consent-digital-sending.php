<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'consent-digital-sending',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'consenso-invio-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'consent-digital-sending',
        'title' => "Consenso Invio Telematico",
        'version' => '1.0.0',
        'description' => "Consenso all'invio telematico della documentazione",
        'processor' => PdfProcessor::class,
        'signers' => ['contractor'],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new SubjectOverlay(1, 44, 72, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['name', 'lastname'],
            ]),
            new SubjectOverlay(1, 66, 79, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 31, 86, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['taxCode'],
            ]),
            new SubjectOverlay(1, 118, 86, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'contractor',
                        'individual' => null,
                    ],
                ],
                'properties' => ['vat'],
            ]),
            new SubjectOverlay(1, 67, 93, [
                'role' => 'contractor',
                'method' => 'getSubjectAddress',
            ]),
            new TextOverlay(1, 26, 110.5, [], 'x'),
            new TextOverlay(1, 26, 144, [], 'x'),
            new SubjectOverlay(1, 77, 144, [
                'conditionalRole' => [
                    'field' => 'type',
                    'options' => [
                        'legal' => 'rep',
                        'individual' => 'contractor',
                    ],
                ],
                'properties' => ['email'],
            ]),
        ],
    ],
];