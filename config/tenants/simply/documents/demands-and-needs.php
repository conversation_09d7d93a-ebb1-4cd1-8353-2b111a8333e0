<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Pdf\PdfProcessorDN;

return [
    'code' => 'demands-and-needs',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'blank.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'demands-and-needs',
        'title' => "Demands And Needs",
        'version' => '1.0.0',
        'description' => "Questionario di Valutazione delle Esigenze e dei Bisogni del Cliente",
        'processor' => PdfProcessorDN::class,
        'signers' => ["contractor"],  
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 150,
                'y' => 17.5,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            // new TextOverlay(1, 150, 17.5, [], 'test'), test signature position
        ],
    ],
];