<?php 
return [

    'tasks' => [
        [
            'type' => 'survey', 
            'dependson' => null,

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
            'template' => 'Tasks/Dorotea/Survey',
        ],
        [
            'type' => 'mapper', 
            'dependson' => 'survey',
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
            'template' => 'Tasks/Dorotea/ProductMapper'
        ],
        [
            'type' => 'summary', 
            'dependson' => 'mapper',
            'navigation' => 'pipeline.open|pipeline.closed',
            'controller' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Summary\SummaryManager', 
            'template' => 'Tasks/Dorotea/Summary'
        ],
    ],
    
];