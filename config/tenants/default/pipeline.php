<?php

use App\Events\PrivacySigned;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;

return [
    'tasks' => [

        [
            'type' => 'client', 
            'dependson' => null,

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Client\ClientController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Client\ClientManager', 
            'template' => 'Tasks/Client',
            'name' => 'Anagrafica',

            'config' => [
                'canSkipTo' => 'issuance', 
                'blacklist' => ['signature', 'issuance'],
                'compile' => ['mup', 'privacy', 'assignment', 'consent-digital-sending'],
            ]
        ],
        [
            'type' => 'signature', 
            'dependson' => 'client',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_SIMPLE, // or SignatureManager::$SIGNATURE_FEA
                'folderTitle' => 'Documentazione precontrattuale',
                'folderDescription' => 'Documentazione precontrattuale',
                'documents' => ['privacy', 'assignment', 'consent-digital-sending'],
                'attachments' => ['mup'],
                'finalizationEvents' => [PrivacySigned::class],
            ]
        ],
        [
            'type' => 'survey', 
            'dependson' => 'client',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Survey\SurveyManager', 
            'template' => 'Tasks/Survey',
        ],
        [
            'type' => 'mapper', 
            'dependson' => 'survey',
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperManager', 
            'template' => 'Tasks/ProductMapper',
            
            'config' => [
                'compile' => ['demands-and-needs'],
            ]
        ],
        [
            // FEA 2
            'type' => 'signature', 
            'dependson' => 'survey',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_FEA,
                'folderTitle' => 'Demands and Needs',
                'folderDescription' => 'Demands and Needs',
                'documents' => ['demands-and-needs'],
                'attachments' => null,
            ]
        ],
        [
            'type' => 'issuance', 
            'dependson' => 'signature',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager', 
            'template' => 'Tasks/Issuance',

            'config' => [
                // 'compile' is implicit here, it's done natively by the IssuanceManager.
                // maybe add some mechanism to check in task.finalize that all documents are finalized.
            ]
        ],
        // FEA 3
        [
            'type' => 'signature', 
            'dependson' => 'issuance',

            // Possible options: as defined in PipelineManager.
            'navigation' => 'pipeline.open',
            'controller' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureController', 
            'manager' => 'Upnovation\Easyprofile\Tasks\Signature\SignatureManager', 
            'template' => 'Tasks/Signature',

            'config' => [
                // 'signers' => ['contractor'], maybe should be in the Document / SignatureBatch architeture.
                'service' => IGSign::class,
                'signatureDeadlineInDays' => 3,
                'signatureType' => SignatureManager::$SIGNATURE_FEA, // or SignatureManager::$SIGNATURE_FEA
                'folderTitle' => 'Documentazione contrattuale',
                'folderDescription' => 'Documentazione contrattuale',
                'documents' => ['product-policy'],
                'attachments' => [],

                // maybe dump this one.
                // @check
                'dataInjection' => 'getDataForDocument'
            ]
        ]
    ]
];