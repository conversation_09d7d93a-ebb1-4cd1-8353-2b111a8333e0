<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'mup',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'mup-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'mup',
        'title' => "Allegato 3 (MUP)",
        'version' => '1.0.0',
        'description' => "Allegato 3 (Modello Unico Precontrattuale)",
        'processor' => PdfProcessor::class,
        'signers' => null,    
        'signatures' => null,
        'overlayArray' => [
            new UserOverlay(1, 52, 100, [
                'properties' => ['lastname', 'name'],
            ]),
            new UserOverlay(1, 73.5, 104.5, [
                'method' => 'ruiCode',
            ]),
            new UserOverlay(1, 101.5, 104.5, [
                'method' => 'ruiDate',
            ]),
            new UserOverlay(1, 63, 112.5, [
                'properties' => ['node'],
            ]),
            new UserOverlay(1, 115, 112.5, [
                'method' => 'nodeRuiCode',
            ]),
            new UserOverlay(1, 147, 112.5, [
                'method' => 'nodeRuiDate',
            ]),
        ],
    ],
];