<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\AddressOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\ArrayOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\RadioOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'CFMUTUIEVOL',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates/products',
        'filename' => 'cf-mutui-evolution-06-2025.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'product-form',
        'title' => 'CF Mutui Evolution',
        'version' => '1.0.0',
        'description' => "Scheda Raccolta dati CF Mutui Evolution",
        'processor' => PdfProcessor::class,
        'signers' => ["contractor"],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new UserOverlay(1, 43, 45, ['properties' => ['name', 'lastname'],]),
            new TextOverlay(1, 120, 45, [], "@telefono_coll"),
            /*new SubjectOverlay(1, 37, 60, [
                'role' => 'contractor',
                'properties' => ['lastname'],
            ]),
            new SubjectOverlay(1, 120, 60, [
                'role' => 'contractor',
                'properties' => ['name'],
            ]),
            new SubjectOverlay(1, 38.5, 64.5, [
                'role' => 'contractor',
                'method' => 'getPrintableBirthdate',
            ]),*/

            new RadioOverlay(1, 100, 70, [
                'key' => 'combinazione',
                'options' => [
                    '1' => new TextOverlay(1, 20.25, 61.75, [], "x"),
                    '3' => new TextOverlay(1, 55.25, 61.75, [], "x"),
                    '5' => new TextOverlay(1, 20.25, 67.5, [], "x"),
                    '6' => new TextOverlay(1, 55.25, 67.5, [], "x"),
                    '8' => new TextOverlay(1, 20, 72.25, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 100, 70, [
                'key' => 'intermediazione',
                'options' => [
                    '0' => new TextOverlay(1, 164.50, 61.75, [], "x"),
                    '1' => new TextOverlay(1, 153.25, 61.75, [], "x"),
                ],
            ]),
            new ArrayOverlay(1, 165, 67.25, [
                'key' => 'importoIntermediazione',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 67, 88, [
                'key' => 'importoFinanziato',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 134, 88, [
                'key' => 'banca',
            ]),
            new ArrayOverlay(1, 50, 93.5, [
                'key' => 'durata',
            ]),
            new ArrayOverlay(1, 94.5, 93.5, [
                'key' => 'dataErogazione',
                'fmtDate' => true
            ]),
            new ArrayOverlay(1, 145, 93.5, [
                'key' => 'notaio',
            ]),
            new RadioOverlay(1, 100, 101, [
                'key' => 'tipoAbitazione',
                'options' => [
                    'appartamento' => new TextOverlay(1, 47.5, 104.5, [], "x"),
                    'villa' => new TextOverlay(1, 100, 104.5, [], "x"),
                ],
            ]),
            new ArrayOverlay(1, 40, 110, [
                'key' => 'indirizzoImmobile.street',
            ]),
            new ArrayOverlay(1, 148, 110, [
                'key' => 'indirizzoImmobile.number',
            ]),
            new ArrayOverlay(1, 173.5, 110, [
                'key' => 'indirizzoImmobile.zip',
            ]),
            new ArrayOverlay(1, 33, 115.5, [
                'key' => 'indirizzoImmobile.city',
            ]),
            new ArrayOverlay(1, 122, 115.5, [
                'key' => 'indirizzoImmobile.province',
            ]),
            new ArrayOverlay(1, 144, 115.5, [
                'key' => 'piano',
            ]),
            new ArrayOverlay(1, 171, 115.5, [
                'key' => 'interno',
            ]),
            new ArrayOverlay(1, 114, 120.5, [
                'key' => 'importoDaAssicurare',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 46.5, 131.5, [
                'key' => 'datiCatastaliImmobile.foglio'
            ]),
            new ArrayOverlay(1, 75.5, 131.5, [
                'key' => 'datiCatastaliImmobile.part'
            ]),
            new ArrayOverlay(1, 105, 131.5, [
                'key' => 'datiCatastaliImmobile.sub'
            ]),
            new ArrayOverlay(1, 136, 131.5, [
                'key' => 'datiCatastaliImmobile.cat'
            ]),
            new ArrayOverlay(1, 173, 131.5, [
                'key' => 'datiCatastaliImmobile.classe'
            ]),
            new ArrayOverlay(1, 33, 137, [
                'key' => 'datiCatastaliImmobile.consist'
            ]),
            new ArrayOverlay(1, 79.5, 137, [
                'key' => 'datiCatastaliImmobile.rendita',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 48.5, 142.5, [
                'key' => 'datiCatastaliPertinenza.foglio'
            ]),
            new ArrayOverlay(1, 77.5, 142.5, [
                'key' => 'datiCatastaliPertinenza.part'
            ]),
            new ArrayOverlay(1, 107, 142.5, [
                'key' => 'datiCatastaliPertinenza.sub'
            ]),
            new ArrayOverlay(1, 138, 142.5, [
                'key' => 'datiCatastaliPertinenza.cat'
            ]),
            new ArrayOverlay(1, 175, 142.5, [
                'key' => 'datiCatastaliPertinenza.classe'
            ]),
            new ArrayOverlay(1, 33, 147.5, [
                'key' => 'datiCatastaliPertinenza.consist'
            ]),
            new ArrayOverlay(1, 79.5, 147.5, [
                'key' => 'datiCatastaliPertinenza.rendita',
                'fmtCurrency' => true
            ]),
            new ArrayOverlay(1, 36, 159.5, [
                'key' => 'datiAssicurato.cognome'
            ]),
            new ArrayOverlay(1, 118, 159.5, [
                'key' => 'datiAssicurato.nome'
            ]),
            new ArrayOverlay(1, 37, 165, [
                'key' => 'datiAssicurato.via'
            ]),
            new ArrayOverlay(1, 79.5, 165, [
                'key' => 'datiAssicurato.numVia'
            ]),
            new ArrayOverlay(1, 100, 165, [
                'key' => 'datiAssicurato.cap'
            ]),
            new ArrayOverlay(1, 128, 165, [
                'key' => 'datiAssicurato.localita'
            ]),
            new ArrayOverlay(1, 176.5, 165, [
                'key' => 'datiAssicurato.provincia'
            ]),
            new ArrayOverlay(1, 52, 170, [
                'key' => 'datiAssicurato.telefono'
            ]),
            new ArrayOverlay(1, 113, 169.5, [
                'key' => 'datiAssicurato.email'
            ]),
            new RadioOverlay(1, 100, 200, [
                'key' => 'dichiarazione1',
                'options' => [
                    '0' => new TextOverlay(1, 161.4, 243, [], "x"),
                    '1' => new TextOverlay(1, 147.25, 243, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 100, 200, [
                'key' => 'dichiarazione2',
                'options' => [
                    '0' => new TextOverlay(1, 161.4, 251, [], "x"),
                    '1' => new TextOverlay(1, 147.25, 251, [], "x"),
                ],
            ]),
            new RadioOverlay(1, 100, 200, [
                'key' => 'dichiarazione3',
                'options' => [
                    '0' => new TextOverlay(1, 161.4, 262.75, [], "x"),
                    '1' => new TextOverlay(1, 147.25, 262.75, [], "x"),
                ],
            ]),
        ],
    ],

    'policy' => [
        'title' => 'CF Mutui Evolution - Proposta assicurativa',
        'signers' => ["contractor"], 
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ]
        ],
    ],

    /*'quote' => [
        'net' => true,

        'fields' => [
            'combinazione',
            'importoFinanziato',
        ],
    ],*/

    'formRules' =>[
        // Dati generali
        'combinazione' => 'required|string',
        'intermediazione' => 'required|string',
        'importoIntermediazione' => 'nullable|numeric|min:0|required_if:intermediazione,1',
        'emissione' => 'nullable|numeric|min:0|required_if:intermediazione,1',

        // Dati mutuo
        'importoFinanziato' => 'required|numeric|min:0',
        'banca' => 'required|string',
        'durata' => 'required|numeric|min:0',
        'dataErogazione' => 'required|date',
        'notaio' => 'required|string',

        // Dati immobile
        'tipoAbitazione' => 'required|string',
        'indirizzoImmobile.type' => 'required|string',
        'indirizzoImmobile.street' => 'required|string',
        'indirizzoImmobile.number' => 'required|string',
        'indirizzoImmobile.zip' => 'required|string',
        'indirizzoImmobile.city' => 'required|string',
        'indirizzoImmobile.province' => 'required|string',
        'indirizzoImmobile.region' => 'required|string',
        'indirizzoImmobile.country' => 'required|string',
        'piano' => 'required|numeric|min:0',
        'interno' => 'required|string',
        'importoDaAssicurare' => 'required|numeric|min:0',

        // Dati catastali
        'datiCatastaliImmobile.foglio' => 'required|string',
        'datiCatastaliImmobile.part' => 'required|string',
        'datiCatastaliImmobile.sub' => 'required|string',
        'datiCatastaliImmobile.cat' => 'required|string',
        'datiCatastaliImmobile.classe' => 'required|string',
        'datiCatastaliImmobile.consist' => 'required|string',
        'datiCatastaliImmobile.rendita' => 'required|numeric|min:0',
        'datiCatastaliPertinenza.foglio' => 'string',
        'datiCatastaliPertinenza.part' => 'string',
        'datiCatastaliPertinenza.sub' => 'string',
        'datiCatastaliPertinenza.cat' => 'string',
        'datiCatastaliPertinenza.classe' => 'string',
        'datiCatastaliPertinenza.consist' => 'string',
        'datiCatastaliPertinenza.rendita' => 'numeric|min:0',

        // Dati assicurato
        'datiAssicurato.nome' => 'required|string',
        'datiAssicurato.cognome' => 'required|string',
        'datiAssicurato.via' => 'required|string',
        'datiAssicurato.numVia' => 'required|string',
        'datiAssicurato.cap' => 'required|string',
        'datiAssicurato.localita' => 'required|string',
        'datiAssicurato.provincia' => 'required|string',
        'datiAssicurato.telefono' => 'required|string',
        'datiAssicurato.email' => 'required|email',

        // Dichiara inoltre
        'dichiarazione1' => 'required|string',
        'dichiarazione2' => 'required|string',
        'dichiarazione3' => 'required|string',
    ]
];