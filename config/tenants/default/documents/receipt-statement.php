<?php

use App\Models\File;
use Upnovation\Easyprofile\Pdf\Overlays\TextOverlay;
use Upnovation\Easyprofile\Pdf\Overlays\UserOverlay;
use Upnovation\Easyprofile\Pdf\PdfProcessor;

return [
    'code' => 'receipt-statement',

    'file' => new File([
        'type' => 'template',
        'disk' => 'documents',
        'path' => 'templates',
        'filename' => 'mandato-v1.pdf',
    ]),

    'document' => [
        'node_id' => '1', // @todo
        'type' => 'receipt-statement',
        'title' => "Avvenuta ricezione",
        'version' => '1.0.0',
        'description' => "Dichiarazione di avvenuta ricezione della documentazione",
        'processor' => PdfProcessor::class,
        'signers' => ['contractor'],    
        'signatures' => [
            [
                'name' => 'Signature 1',
                'page' => 1,
                'x' => 33,
                'y' => 180,
                'cx' => 250,
                'cy' => 60
            ],
        ],
        'overlayArray' => [
            new UserOverlay(1, 52, 100, [
                'properties' => ['lastname', 'name'],
            ]),
            new TextOverlay(1, 70, 104, [], '@rui'),
            new TextOverlay(1, 101, 104, [], '@ruidate'),
            new TextOverlay(1, 63, 112, [], '@int1'),
            new TextOverlay(1, 115, 112, [], '@rui1'),
            new TextOverlay(1, 147, 112, [], '@ruidate'),
        ],
    ],
];