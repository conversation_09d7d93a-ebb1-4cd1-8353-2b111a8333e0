export default class FormUtils {
    errorCss(errors, field, otherClasses = null) {
        const base = {'error-border': true};

        if (otherClasses) {
            for (const cls of otherClasses) {
                base[cls] = true;
            }
        }

        return errors && errors[field] ? base : null;
    }

    filterErrors(errors, field) {
        if (! errors || ! errors.messages || ! errors.key) {
            return {};
        }

        return Object.fromEntries(
            Object.entries(errors.messages).filter(([key]) =>
                key.startsWith(field)
            )
        );
    }
}