export default class TaxCode {
    checkName(name, fiscalCode) {
        const nameCode = this.generateCodeForName(name);

        return fiscalCode.slice(3, 6).toUpperCase() === nameCode.toUpperCase();
    }
    
    checkLastname(surname, fiscalCode) {
        const surnameCode = this.generateCodeForSurname(surname);

        return fiscalCode.slice(0, 3).toUpperCase() === surnameCode.toUpperCase();
    }

    getBirthDate(taxCode) {
        if (! taxCode) {
            return null;
        }

        if (taxCode.length !== 16) {
            throw new Error("The tax code must be 16 characters long.");
        }
        
        // Extract the characters representing the year, month, and day
        const year = parseInt(taxCode.substring(6, 8), 10);
        const monthChar = taxCode[8];
        let day = parseInt(taxCode.substring(9, 11), 10);
    
        // Calculate the full year (adding '19' or '20' based on common logic)
        const currentYear = new Date().getFullYear() % 100;
        const fullYear = (year > currentYear) ? 1900 + year : 2000 + year;
    
        // Convert the month from the character
        const months = {
            'A': '01', 'B': '02', 'C': '03', 'D': '04', 'E': '05', 'H': '06',
            'L': '07', 'M': '08', 'P': '09', 'R': '10', 'S': '11', 'T': '12'
        };
    
        const numericMonth = months[monthChar.toUpperCase()];
        if (!numericMonth) {
            throw new Error("Invalid month in the tax code.");
        }
    
        // Determine the day (if > 31, it means female and subtract 40)
        if (day > 31) {
            day -= 40;
        }
    
        // Format the date as a string
        const formattedDay = day.toString().padStart(2, '0');
        return `${fullYear}-${numericMonth}-${formattedDay}`;
    }

    //
    // Utility methods
    //

    getConsonants(str) {
        return str.replace(/[^BCDFGHJKLMNPQRSTVWXYZ]/gi, '');
    }
    
    getVowels(str) {
        return str.replace(/[^AEIOU]/gi, '');
    }
    
    generateCodeForName(name) {
        const consonants = this.getConsonants(name);
        const vowels = this.getVowels(name);
        
        if (consonants.length >= 4) {
            return consonants[0] + consonants[2] + consonants[3];
        }
        
        const code = (consonants + vowels).slice(0, 3);
        return code.padEnd(3, 'X');
    }
    
    generateCodeForSurname(surname) {
        const consonants = this.getConsonants(surname);
        const vowels = this.getVowels(surname);
        
        const code = (consonants + vowels).slice(0, 3);
        return code.padEnd(3, 'X');
    }
}