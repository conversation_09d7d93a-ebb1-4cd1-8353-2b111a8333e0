import { toDateInputValue } from "../dateFormatter";

export default class Person {
    constructor(props = {}) {
        this.id = props?.id ?? null;
        this.name = props?.name || '';
        this.lastname = props?.lastname || '';
        this.sex = props?.sex || '';
        this.taxCode = props?.taxCode || '';
        this.birthdate = props?.birthdate ? toDateInputValue(props.birthdate) : '';
        this.birthplace = { 
            country: props?.birthplaceCountry || '', 
            city: props?.birthplaceCity || '', 
            province: props?.birthplaceProvince || '' 
        };

        // @fixme split in database.
        this.phonePrefix = props?.phone ? props.phone.slice(0, 3) : '+39';
        this.phone = props?.phone ? props.phone.slice(3) : '';
        this.email = props?.email || '';

        this.addresses = {
            residence: {
                type: 'residence',
                street: '',
                number: '',
                zip: '',
                city: '',
                province: '',
                region: '',
                country: '',
            },
        };

        if (props?.addresses) {
            for (const key in props.addresses) {
                let type = props.addresses[key].type;
                this.addresses[type] = props.addresses[key];
            }
        }

        this.documents = props?.documents || {
            id: {
                type: 'id',
                number: '',
                issuer: '',
                issuerCountry: '',
                issuerDate: '',
                expiry: '',
                files: [],
            },
        };

        this.preferences = props?.preferences || {
            marketing: false,
        };
    }

    static address(type, person){
        let addresses = person.addresses?.map((address) => {
            if (address.type === type) {
                return address;
            }
        });

        return addresses?.length ? addresses[0] : null;
    }

    static make(props = {}) {
        return new Person(props);
    }

    static factory() {
        return new Person({
            name: 'Mario',
            lastname: 'Rossi',
            sex: 'M',
            taxCode: 'RSSMRA80A01H501U',
            birthdate: '1980-01-01',
            email: '<EMAIL>',
            phonePrefix: null,
            phone: '+393331234567',
            idType: 'CI',
            idNumber: 'AA1234567',
            idFile__placeholder: null,
            birthplace: { country: 'Italia', city: 'Roma', province: 'RM' },
            addresses: {
                residence: {
                    type: 'residence',
                    street: 'Via Roma',
                    number: '1',
                    zip: '00100',
                    city: 'Roma',
                    province: 'RM',
                    region: 'Lazio',
                    country: 'Italia',
                },
            },
            documents: {
                id: {
                    type: 'id',
                    number: 'AA1234567',
                    issuer: 'Comune di Roma',
                    issuerCountry: 'Italia',
                    issuerDate: '2015-01-01',
                    expiry: '2025-01-01',
                    files: [],
                },
            },
            preferences: {
                marketing: true,
            },
        });
    }
}