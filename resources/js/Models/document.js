export default class Document {

    static type(type) {
        switch (type) {
            case 'mup':
            case 'privacy':
            case 'assignment':
            case 'demands-and-needs':
            case 'consent-digital-sending':
            case 'receipt-statement':
                return "Precontrattuale";
            
            case 'product-form':
                return "Scheda prodotto";

            case 'product-policy':
                return "Modello polizza / proposta";
            default:
                return type;
        }
    }
}