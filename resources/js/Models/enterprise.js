export default class Enterprise {
    constructor(props = {}) {
        this.id = props?.id ?? null;
        this.name = props?.name || '';
        this.vat = props?.vat || '';
        this.legalForm = props?.legalForm || '';
        this.pec = props?.pec || '';
        this.email = props?.email || '';
        this.phone = props?.phone || '';

        this.addresses = {
            headquarters: {
                type: 'headquarters',
                street: '',
                number: '',
                zip: '',
                city: '',
                province: '',
                region: '',
                country: '',
            },
        };

        if (props?.addresses) {
            for (const key in props.addresses) {
                let type = props.addresses[key].type;
                this.addresses[type] = props.addresses[key];
            }
        }

        this.rep = props?.rep || {};

        this.preferences = props?.preferences || {
            marketing: false,
        };
    }

    static make(props = {}) {
        return new Enterprise(props);
    }

    static factory() {
        return new Enterprise({
            name: 'Azienda Demo S.p.A.',
            vat: '12345678901',
            legalForm: 'S.p.A.',
            pec: '<EMAIL>',
            email: '<EMAIL>',
            phone: '0612345678',
            address: {
                street: 'Via Industria',
                number: '10',
                zip: '20100',
                city: 'Milano',
                province: 'MI',
                region: 'Lombardia',
                country: 'Italia',
            },
            preferences: {
                marketing: true,
            }
        });
    }
}