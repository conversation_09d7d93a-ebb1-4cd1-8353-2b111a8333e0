<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import Card from 'primevue/card';
import Button from 'primevue/button';
import { dateFormatter } from "../dateFormatter.js";
import Obscure from '../Components/Obscure.vue';
import Identity from '../Models/identity.js';
import Debug from '../Components/Debug.vue';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import User from '../user';
import Timeline from 'primevue/timeline';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import State from '../Components/State.vue';
import PipelineShort from '../Components/Content/PipelineShort.vue';
import ProfileShort from '../Components/Content/ProfileShort.vue';
import IssuanceList from '../Components/Content/IssuanceList.vue';
import File from '../Components/Content/File.vue';
import FileList from '../Components/Content/FileList.vue';
import Person from '../Models/person.js';
import Product from '../Components/Content/Product.vue';
import PipelineList from '../Components/Content/PipelineList.vue';


export default {
    props: {
        user: Object,
        client: Object,
        referral: Object,
        pipelines: Array,
        profile: Object,
        issuances: Array,
        files: Array
    },
    components: {
        MasterLayout,
        Card,
        Button,
        Obscure,
        Debug,
        TabView,
        TabPanel,
        Timeline,
        DataTable,
        Column,
        State,
        PipelineShort,
        ProfileShort,
        IssuanceList,
        File,
        FileList,
        Product,
        PipelineList,
    },
    data() {
        let activePipelines = this.pipelines.filter(pipeline => pipeline.state === 'open');

        let address = Person.address('residence', this.client.person);

        return {
            docTypes: Identity.docTypes,
            dateFormatter,
            User,
            mockTasks: [
                { name: 'Compilazione', state: 'completed' },
                { name: 'Verifica', state: 'progress' },
                { name: 'Firma', state: 'pending' },
                { name: 'Conferma', state: 'pending' },
            ],
            activePipelines,
            address,
        }
    },
    methods: {
        docType(docType) {
            const type = this.docTypes.find(type => type.code === docType);
            return type ? type.name : 'Sconosciuto';
        },
    },  
}
</script>

<template>
    <MasterLayout>

        <Debug :show="0" :float="1" :pre="1">{{ client.person }}</Debug>

        <!-- make a grid with 2 columns with ratio 3/9 (tailwind) -->
        <div class="grid grid-cols-12 gap-3">
            <div class="col-span-3 flex flex-col gap-3">
                <Card>
                    <template #title><i class="pi pi-user-edit mr-2"></i> Contraente</template>

                    <template #content>
                        <h4 class="mt-0">Nominativo</h4>
                        <p>{{ client.person.name }} {{ client.person.lastname }}</p>

                        <h4>Dati</h4>

                        <h5>Codice fiscale</h5>
                        <p><Obscure>{{ client.person.taxCode }}</Obscure></p>
                        <h5>Data di nascita</h5>
                        <p><Obscure>{{ dateFormatter(client.person.birthdate) }}</Obscure></p>

                        <template v-if="address">
                            <h5>Luogo</h5>
                            <p><Obscure>{{ address.zip_record?.denominazione_ita }} ({{ address.zip_record?.sigla_provincia }})</Obscure></p>
                        </template>

                        <h4>Contatti</h4>
                        <p>
                        <Obscure>
                            {{ client.person.email }}
                        </Obscure>
                        </p>
                        <p>
                        <Obscure>
                            {{ client.person.phone }}
                        </Obscure>
                        </p>

                        <template v-if="User.is(user, 'manager')">
                            <h4 class="m-0">Referral</h4>
                            <p>
                                <a :href="`/salesmen/${referral.id}`">
                                    {{ referral.name }} {{ referral.lastname }}
                                </a><br>
                                <span class="text-sm">({{ referral.node?.name }})</span>
                            </p>
                        </template>
                        
                    </template>
                </Card>

                <Card>
                    <template #title><i class="pi pi-id-card mr-2"></i> Documenti</template>

                    <template #content>
                        <template v-for="(document, index) in client.person.documents" :key="index">
                            <h4 class="m-0">{{ docType(document.type) }}</h4>
                            <Obscure>
                                <p>N. {{ document.number }}</p>
                                <p>Scadenza: {{ dateFormatter(document.expiry) }}</p>
                            </Obscure>
                            <h5 class="m-0">Allegati / scansioni</h5>
                            <template v-for="(file, index) in document.files" :key="index">
                                    <a :href="`/clients/${client.id}/document/${document.type}/${index}`" target="_blank" class="text-blue-600 hover:underline text-sm cursor-pointer">
                                        File {{ index + 1 }}
                                    </a>
                            </template>
                        </template>
                    </template>
                </Card>
            </div>
            <div class="col-span-9 flex flex-col gap-3">
                <Card v-if="0">
                    <template #title><i class="pi pi-spinner mr-2"></i> Posizioni in corso</template>
                    <template #content>
                        <!-- Timeline orizzontale dei task della prima pipeline -->
                        <template v-for="pipeline in pipelines" :key="pipeline.id">
                            <Timeline
                                v-if="pipeline.state == 'open'"
                                :value="pipeline.tasks"
                                layout="horizontal"
                                align="top"
                            >
                                
                                <template #content="slotProps">
                                    <div class="">
                                        <span class="text-sm font-bold">{{ slotProps.item.displayName }}</span>
                                        <div class="text-xs text-gray-500">
                                            {{ slotProps.item.state }}
                                        </div>
                                    </div>
                                </template>
                            </Timeline>
                        </template>
                    </template>
                </Card>
                                
                <Card v-if="activePipelines.length">
                    <template #title><i class="pi pi-spinner mr-2"></i> Posizioni in corso</template>
                    <template #content>
                        <div v-for="pipeline in activePipelines" :key="pipeline.id" class="mb-4">
                            <PipelineShort :user="user" :pipeline="pipeline" />
                        </div>
                    </template>
                </Card>

                <Card>
                    <template #content>
                        <TabView>
                            <TabPanel key="t1">
                                <template #header>
                                    <i class="pi pi-list mr-2"></i>
                                    Emissioni
                                </template>

                                <template v-if="issuances.length === 0">
                                    <p>Non c'è stata ancora nessuna emissione per questo cliente.</p>
                                </template>

                                <template v-else v-for="(issuance, idx) in Object.values(issuances)" :key="issuance[0]?.id">
                                    <div class="mb-10">
                                        <h4 class="mb-2">
                                            Posizione #{{ issuance[0]?.task?.pipeline?.id }}
                                            del {{ dateFormatter(issuance[0]?.task?.pipeline?.created_at) }}
                                        </h4>
                                        <IssuanceList :issuances="issuance" />
                                    </div>
                                </template>
                            </TabPanel>


                            <TabPanel key="t2">
                                <template #header>
                                    <i class="pi pi-tags mr-2"></i>
                                    Profilo cliente
                                </template>

                                <ProfileShort :profile="profile" />
                            </TabPanel>


                            <TabPanel key="t3">
                                <template #header>
                                    <i class="pi pi-user mr-2"></i>
                                    Storico posizioni
                                </template>
                                
                                <PipelineList :pipelines="pipelines" />
                            </TabPanel>

                            <TabPanel key="t4">
                                <template #header>
                                    <i class="pi pi-folder mr-2"></i>
                                    Documentazione
                                </template>

                                <template v-for="group in files">
                                    <div class="mb-10">
                                        <h4>
                                            Posizione # {{ group[0]?.task?.pipeline?.id }}
                                            del {{ dateFormatter(group[0]?.task?.pipeline?.created_at) }}
                                        </h4>
                                        <div v-if="group[0]?.task?.pipeline?.issuedProducts?.length" class="my-2 flex flex-wrap gap-2 items-center">
                                            <span v-for="product in group[0]?.task?.pipeline?.issuedProducts" :key="product.id">
                                                <Product :product="product" asBadge="1" />
                                            </span>
                                        </div>

                                        <FileList :list="group" />
                                    </div>
                                    
                                </template>

                            </TabPanel>
                        </TabView>
                    </template>
                </Card>
            </div>
        </div>

    </MasterLayout>

</template>

<style scoped>
    h4 {
        @apply mt-3;
    }
</style>