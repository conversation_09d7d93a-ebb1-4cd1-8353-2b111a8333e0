<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import State from '../Components/State.vue';
import Card from 'primevue/card';
import SalesmanShort from '../Components/Content/SalesmanShort.vue';
import PipelineList from '../Components/Content/PipelineList.vue';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import ClientList from '../Components/Content/ClientList.vue';
import InputText from 'primevue/inputtext';
import FormMessage from '../Components/Form/FormMessage.vue';
import { useForm } from '@inertiajs/vue3';
import Select from 'primevue/select';
import Button from 'primevue/button';
import { ref } from 'vue';

export default {
    props: {
        salesman: Object,
        network: Array
    },

    components: {
        MasterLayout,
        State,
        Card,
        SalesmanShort,
        PipelineList,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON>lientList,
        InputText,
        FormMessage,
        Select,
        Button
    },

    setup(props) {
        const loading = ref(false);

        const ruiSections = [
            { label: 'A', value: 'A' },
            { label: 'B', value: 'B' },
            { label: 'C', value: 'C' },
            { label: 'D', value: 'D' },
            { label: 'E', value: 'E' },
        ];
        return {
            form: useForm({
                name: props.salesman.name || '',
                lastname: props.salesman.lastname || '',
                email: props.salesman.email || '',
                phone: props.salesman.phone || '',
                rui: {
                    section: props.salesman.rui?.section || '',
                    code: props.salesman.rui?.code || '',
                    subscribed_at: props.salesman.rui?.subscribed_at || null,
                }
            }),

            ruiSections,

            loading,
        }
    },

    methods: {
        submit(form) {
            this.loading = true;

            form.put(`/salesman/${this.salesman.id}`, {
                onSuccess: () => {
                    this.$inertia.reload({ preserveState: true, preserveScroll: true });
                },
                onFinish: () => {
                    this.loading = false;
                }
            });            
        }
    }
}
</script>

<template>
    <MasterLayout title="Scheda Consulente">

        <div class="flex flex-col gap-5">
            <div class="grid grid-cols-12 gap-6">
                <div class="col-span-4 flex flex-col gap-4">
                    <!-- Client -->
                    <Card>
                        <template #title>
                            <i class="pi pi-briefcase"></i>
                            Consulente
                        </template>

                        <template #content>
                            <SalesmanShort :salesman="salesman"></SalesmanShort>
                        </template>
                    </Card>
                </div>

                <div class="col-span-8">
                    <TabView>
                        <TabPanel header="Scheda">
                            <div>
                                <form @submit.prevent="submit(form)">
                                    <div class="flex flex-col gap-4">
                                        <h3>Dati anagrafici</h3>
                                        <div>
                                            <InputText v-model="form.name" placeholder="Nome" class="w-full" />
                                            <FormMessage :errors="form.errors" field="name">Nome</FormMessage>
                                        </div>

                                        <div>
                                            <InputText v-model="form.lastname" placeholder="Cognome" class="w-full" />
                                            <FormMessage :errors="form.errors" field="lastname">Cognome</FormMessage>
                                        </div>

                                        <h3>Dati contatto</h3>
                                        <div>
                                            <InputText v-model="form.email" placeholder="Email" class="w-full" />
                                            <FormMessage :errors="form.errors" field="email">Email</FormMessage>
                                        </div>

                                        <div>
                                            <InputText v-model="form.phone" placeholder="Telefono" class="w-full" />
                                            <FormMessage :errors="form.errors" field="phone">Telefono</FormMessage>
                                        </div>

                                        <h3>Iscrizione RUI</h3>
                                        <div>
                                            <Select v-model="form.rui.section" :options="ruiSections" optionLabel="label" optionValue="value" class="w-full">
  
                                            </Select>
                                            <FormMessage :errors="form.errors" field="rui.section">Sezione RUI</FormMessage>
                                        </div>

                                        <div>
                                            <InputText v-model="form.rui.code" placeholder="Codice RUI" class="w-full" />
                                            <FormMessage :errors="form.errors" field="rui.code">Codice</FormMessage>
                                        </div>

                                        <div>
                                            <input type="date" v-model="form.rui.subscribed_at" class="w-full p-inputtext p-component" placeholder="Data di iscrizione RUI" />
                                            <FormMessage :errors="form.errors" field="rui.subscribed_at">Data di iscrizione rui</FormMessage>
                                        </div>

                                        <Button 
                                            label="Salva" 
                                            type="submit" 
                                            icon="pi pi-save" 
                                            severity="primary" 
                                            class="w-40 mx-auto"
                                            :loading="loading" />
                                    </div>
                                </form>
                            </div>
                        </TabPanel>

                        <TabPanel header="Posizioni">
                            <div>
                                <PipelineList :pipelines="salesman.pipelines" />
                            </div>
                        </TabPanel>

                        <TabPanel header="Clienti">
                            <div>
                                <ClientList :clients="salesman.clients" />
                            </div>
                        </TabPanel>
                    </TabView>
                </div>
            </div>
        </div>
    </MasterLayout>
</template>


