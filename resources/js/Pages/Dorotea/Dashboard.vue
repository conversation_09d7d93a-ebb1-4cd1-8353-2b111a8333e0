<script setup>
import MasterLayout from '../../Layouts/MasterLayout.vue';
import State from '../../Components/State.vue';
import {TrashIcon, ChevronDownIcon, PencilIcon, MagnifyingGlassIcon, PlusCircleIcon} from '@heroicons/vue/24/outline';
import {Menu, MenuButton, MenuItems, MenuItem} from '@headlessui/vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Toolbar from 'primevue/toolbar';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';

defineProps({
    pipelines: Object,
    user: Object
})

let f = ref({
    'global': {
        value: '',
        matchMode: 'contains'
    },
    'state': {
        value: '',
        matchMode: 'contains'
    },
    'user.lastname': {
        value: '',
        matchMode: 'contains'
    },
    'egg_opportunity_id': {
        value: '',
        matchMode: 'contains'
    },
    'client.egg_client_id': {
        value: '',
        matchMode: 'contains'
    },
    'currentTask.displayName': {
        value: '',
        matchMode: 'contains'
    }
})
</script>

<template>
    <MasterLayout title="Dashboard">
        <div class="card">

            <div class="card-header">
                <Toolbar style="border: 0">
                    <template #start>
                        <h1 class="mb-0">Lista posizioni</h1>
                    </template>
                </Toolbar>
            </div>

            <div class="card-body">

                <div class="max-md:overflow-x-auto pb-10">
                    <DataTable 
                        dataKey="id"
                        :value="pipelines"
                        v-model:filters="f" 
                        filterdisplay="menu"
                        :globalFilterFields="['state', 'user.lastname', 'egg_opportunity_id', 'client.person.egg_client_id', 'currentTask.displayName']"
                        paginator 
                        :rows="50" 
                        tableStyle="min-width: 50rem" 
                        class="text-sm"
                        >

                        <template #header>
                            <div class="flex justify-end">
                                <IconField>
                                    <InputIcon>
                                        <i class="pi pi-search" />
                                    </InputIcon>
                                    <InputText v-model="f['global'].value" placeholder="Cerca ovunque..." />
                                </IconField>
                            </div>
                        </template>

                        <Column field="id" header="#" sortable style="width: 5%;"></Column>

                        <Column field="state" header="Posizione" sortable>
                            <template #body="data">
                                <State :pipeline="data.data"></State>
                            </template>
                            <template #filter="{ filterModel, filterCallback }" class="w-full">
                                <select v-model="filterModel.value" @change="filterCallback()" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                    <option value="">Tutti</option>
                                    <option value="open">Aperta</option>
                                    <option value="closed">Chiusa</option>
                                </select>
                            </template>
                        </Column>

                        <Column field="user.lastname" v-if="user.roles[0].name == 'manager'" header="Utente" sortable>
                            <template #body="data">
                                <span>{{ data.data.user.lastname }}, {{ data.data.user.name }}</span>
                            </template>
                            <template #filter="{ filterModel, filterCallback }">
                                <InputText v-model="filterModel.value" size="small" type="text"  placeholder="Cognome..." />
                            </template>
                        </Column>

                        <Column field="egg_opportunity_id" header="ID EGG Pratica" sortable>
                            <template #body="data">
                                <span>{{ data.data.egg_opportunity_id }}</span>
                            </template>
                            <template #filter="{ filterModel, filterCallback }">
                                <InputText v-model="filterModel.value" size="small" type="text" @input="filterCallback()" placeholder="ID EGG Pratica..." />
                            </template>
                        </Column>

                        <Column field="client.person.egg_client_id" header="ID EGG Cliente" sortable>
                            <template #body="data">
                                <span>{{ data.data.client.person.egg_client_id }}</span>
                            </template>
                            <template #filter="{ filterModel, filterCallback }">
                                <InputText v-model="filterModel.value" size="small" type="text" @input="filterCallback()" placeholder="ID EGG Cliente..." />
                            </template>
                        </Column>

                        <Column field="currentTask.displayName" header="Fase" sortable>
                            <template #body="data">
                                <span>{{ data.data.currentTask && data.data.state != 'closed' ? data.data.currentTask.displayName : '-' }}</span>  
                            </template>

                            <template #filter="{ filterModel, filterCallback }">
                                <InputText v-model="filterModel.value" size="small" type="text" @input="filterCallback()" placeholder="Fase..." />
                            </template>
                        </Column>

                        <Column field="created_at" header="Data apertura" sortable>
                            <template #body="data">
                                {{ formatDateTime(data.data.created_at) }}
                            </template>
                        </Column>

                        <Column field="closed_at" header="Data chiusura" sortable>
                            <template #body="data">
                                {{ data.data.closed_at ? formatDateTime(data.data.closed_at) : "-" }}
                            </template>
                        </Column>

                        <Column>
                            <template #body="data">
                                <Menu v-if="(user.roles[0].name == 'manager' && data.data.state == 'closed') || user.roles[0].name == 'salesman'" as="div" class="relative inline-block text-left">
                                    <div>
                                        <MenuButton class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                                            Azioni
                                            <ChevronDownIcon class="-mr-1 h-5 w-5 text-gray-400" aria-hidden="true" />
                                        </MenuButton>
                                    </div>

                                    <MenuItems v-if="data.data.state != 'closed'" class="overflow-visible absolute right-0 z-10 mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                        <div class="py-1">
                                            <MenuItem v-slot="{ active }">
                                                <a class="flex items-center cursor-pointer" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700', 'block px-4 py-2 text-sm']" @click="exec('resume', data.data.id)">
                                                    <PencilIcon class="h-4 w-4 mr-1" /> Riprendi
                                                </a>
                                            </MenuItem>
                                            <MenuItem v-slot="{ active }">
                                                <a class="flex items-center cursor-pointer" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700', 'block px-4 py-2 text-sm']" @click="exec('remove', data.data.id)"><TrashIcon class="h-4 w-4 mr-1" />Elimina</a>
                                            </MenuItem>
                                        </div>
                                    </MenuItems>

                                    <MenuItems v-if="data.data.state == 'closed'" class="overflow-visible absolute right-0 z-10 mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                        <div class="py-1">
                                            <MenuItem v-slot="{ active }">
                                                <a class="flex items-center cursor-pointer" :class="[active ? 'bg-gray-100 text-gray-900' : 'text-gray-700', 'block px-4 py-2 text-sm']" @click="exec('resume', data.data.id)">
                                                    <MagnifyingGlassIcon class="h-4 w-4 mr-1" /> Visualizza
                                                </a>
                                            </MenuItem>
                                        </div>
                                    </MenuItems>
                                </Menu>
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>
        </div>
    </MasterLayout>

</template>
<script>

import {directive} from 'vue-tippy'
import {router} from '@inertiajs/core';
import Badge from '../../Components/Badge.vue';

export default {
    directives: {
        tippy: directive,
    },
    methods: {
        resume: function (id) {
            location.href = `pipeline/${id}/resume`;
        },
        remove: function (id) {
            if (!confirm("Sicuro?")) {
                return;
            }

            router.delete(`pipeline/${id}`, {only: ["pipelines"], preserveScroll: true})
        },
        exec: function (action, id) {
            this[action](id);
        },
        formatDateTime: function (date) {
            return new Date(date).toLocaleDateString('it-IT');
        }
    },
    components: {Badge}
}
</script>
