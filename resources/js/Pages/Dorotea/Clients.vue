<script setup>
import MasterLayout from '../../Layouts/MasterLayout.vue';
import {dateFormatter} from "@/dateFormatter";
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';

defineProps({
    clients: Array
})

const filters = ref({
    'global': {
        value: '',
        matchMode: 'contains'
    },
    'egg_client_id': {
        value: '',
        matchMode: 'contains'
    },
    'birthdate': {
        value: '',
        matchMode: 'contains'
    },
    'pipeline': {
        value: '',
        matchMode: 'contains'
    }
})
</script>

<template>
    <MasterLayout title="Clienti">

        <div class="card">

            <div class="card-header">
                <h1 class="mb-0">Clienti</h1>
            </div>

            <div class="card-body">
                <div class="overflow-x-auto">
                    <DataTable 
                        :value="clients"
                        v-model:filters="filters" 
                        filterDisplay="menu" 
                        :globalFilterFields="['egg_client_id', 'birthdate']"
                        paginator 
                        :rows="50" 
                        tableStyle="min-width: 50rem" 
                        class="text-sm">

                        <template #header>
                            <div class="flex justify-end">
                                <IconField>
                                    <InputIcon>
                                        <i class="pi pi-search" />
                                    </InputIcon>
                                    <InputText v-model="filters['global'].value" placeholder="Cerca ovunque..." />
                                </IconField>
                            </div>
                        </template>

                        <Column field="egg_client_id" header="EGG ID" sortable></Column>
                        <Column field="birthdate" header="Data di nascita" sortable>
                            <template #body="rowData">
                                {{ dateFormatter(rowData.data.birthdate) }}
                            </template>
                        </Column>
                    </DataTable>
                </div>
            </div>
        </div>
        
    </MasterLayout>
</template>