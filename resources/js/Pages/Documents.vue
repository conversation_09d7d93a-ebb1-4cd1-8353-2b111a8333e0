<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import Button from 'primevue/button';
import { ref } from 'vue';
import Document from '../Models/document.js';

const filters = ref({
    'global': {
        value: '',
        matchMode: 'contains'
    },
    // tood: add more filters
})

export default {
    props: {
        docs: Array
    },

    data() {
        return {
            list: ref(this.docs),
            filters: filters
        }
    },

    setup(){
        let filters = ref({
            'global': {
                value: '',
                matchMode: 'contains'
            },

        })

        return {
            filters
        }
    },

    methods: {
        type(type) {
            return Document.type(type);
        }
    },

    components: {
        MasterLayout,
        DataTable,
        Column,
        InputText,
        Button,
    }
}
</script>

<template>
    <MasterLayout title="Documenti">

        <div class="card">

            <div class="card-header">
                <h1 class="mb-0">Documenti</h1>
            </div>

            <div class="card-body">
                <DataTable 
                    :value="list" 
                    paginator 
                    :rows="25" 
                    class="text-sm" 
                    v-model:filters="filters" 
                    filterDisplay="menu"
                    :globalFilterFields="['title']">

                    <template #header>
                        <div class="flex justify-end">
                            
                                <InputText v-model="filters['global'].value" placeholder="Cerca ovunque..." />
                        </div>
                    </template>

                    <Column field="id" header="ID" sortable style="width: 5%;"></Column>
                    <Column field="node.name" header="Nodo rete" sortable style="width: 15%;"></Column>
                    <Column field="type" header="Tipo" sortable style="width: 15%;">
                        <template #body="data">
                            <span>{{ type(data.data.type) }}</span>
                        </template>
                    </Column>
                    <Column field="title" header="Titolo" sortable>
                        <template #body="data">
                            <span>{{ data.data.title }}</span><br>
                            <small>{{ data.data.description }}</small>
                        </template>
                    </Column>
                    <Column field="version" header="Versione" sortable style="width: 8%;"></Column>
                    <Column field="link" header="" style="width: 15%;" bodyClass="text-right">
                        <template #body="data">
                            <div class="text-right">
                                <a
                                    :href="`/documents/${data.data.id}/template`"
                                    class="btn btn-sm mr-1"
                                    title="Visualizza"
                                >
                                    <span class="pi pi-search"></span>
                                </a>
                                <a
                                    :href="`/documents/${data.data.id}/template?download`"
                                    class="btn btn-sm"
                                    title="Scarica"
                                >
                                    <span class="pi pi-download"></span>
                                </a>
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </div>
        </div>
    </MasterLayout>
</template>