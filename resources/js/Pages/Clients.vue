<script>
import MasterLayout from '../Layouts/MasterLayout.vue';
import { dateFormatter } from "@/dateFormatter";
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';
import Button from 'primevue/button';
import TabView from 'primevue/tabview';
import TabPanel from 'primevue/tabpanel';
import { router } from '@inertiajs/vue3';
import Debug from '../Components/Debug.vue';
import HeroEmpty from '../Components/HeroEmpty.vue';
import User from '../user';
import Pipeline from '../Models/pipeline';

export default {
    props: {
        clients: Array,
        user: Object,
    },
    
    data() {
        return {
            dateFormatter,
            filters: {
                'global': { value: '', matchMode: 'contains' },
                'name': { value: '', matchMode: 'contains' },
                'lastname': { value: '', matchMode: 'contains' },
                'birthdate': { value: '', matchMode: 'contains' },
                'pipeline': { value: '', matchMode: 'contains' }
            },
            enterpriseFilters: {
                'global': { value: '', matchMode: 'contains' },
                'name': { value: '', matchMode: 'contains' },
                'vat': { value: '', matchMode: 'contains' },
                'pipeline': { value: '', matchMode: 'contains' }
            }
        }
    },

    components: {
        MasterLayout,
        DataTable,
        Column,
        InputText,
        Button,
        TabView,
        TabPanel,
        Debug,
        HeroEmpty
    },

    setup() {
        return {
            User,
            Pipeline,
        }
    },

    methods: {
        goToClient(id) {
            router.get(`/clients/${id}`);
        }
    }
}
</script>

<template>
    <MasterLayout title="Clienti">
        <Debug :show="0" :pre="1">{{ clients[0] }}</Debug>
        
        <div v-if="! clients.length" class="card min-h-[60vh] flex items-center justify-center">
            <div class="card-body">
                <HeroEmpty title="Nessun cliente trovato" message="Non hai ancora clienti registrati.">

                    <Button
                        v-if="User.is(user, 'salesman')"
                        severity="primary"
                        class="w-60 mt-10"
                        label="Apri una nuova posizione"
                        @click="Pipeline.create()"
                    />
                </HeroEmpty>
            </div>
        </div>
        
        <div v-else class="card">
            <div class="card-header">
                <h1 class="mb-0">Clienti</h1>
            </div>
            <div class="card-body">
                <DataTable 
                    :value="clients"
                    v-model:filters="filters" 
                    filterDisplay="menu" 
                    :globalFilterFields="['name', 'code', 'type']"
                    paginator 
                    :rows="50" 
                    tableStyle="min-width: 50rem" 
                    class="text-sm">

                    <template #header>
                        <div class="flex justify-end">
                            <IconField>
                                <InputIcon>
                                    <i class="pi pi-search" />
                                </InputIcon>
                                <InputText v-model="filters['global'].value" placeholder="Cerca ovunque..." />
                            </IconField>
                        </div>
                    </template>

                    <Column field="id" header="#" style="width: 5%" sortable></Column>
                    <Column field="privacy" header="Privacy" sortable>
                        <template #body="rowData">
                            <template v-if="rowData.data.pipeline?.type == 'individual'">
                                <span v-if="rowData.data.person?.privacy" class="text-green-500">
                                    <i class="pi pi-check" />
                                </span>
                                <span v-else class="text-yellow-500">
                                    <i class="pi pi-hourglass" v-tooltip.top="'In attesa della firma cliente'" />
                                </span>
                            </template>
                            <template v-else>
                                -
                            </template>
                        </template>
                    </Column>

                    <Column field="name" header="Nome" sortable>
                        <template #body="rowData">
                            <span>{{ rowData.data.name }}</span>
                        </template>
                    </Column>
                    
                    <Column field="type" header="Tipo" sortable>
                        <template #body="rowData">
                            <span v-if="rowData.data.pipeline?.type == 'legal'">
                                Azienda
                            </span>
                            <span v-else>
                                Persona fisica
                            </span>
                        </template>
                    </Column>

                    <Column field="code" header="CF/PIVA" sortable>
                        <template #body="rowData">
                            {{ rowData.data.person ? rowData.data.person?.taxCode : rowData.data.enterprise ? rowData.data.enterprise?.vat : '-' }}
                        </template>
                    </Column>

                    

                    <Column field="" header="" style="width: 20%">
                        <template #body="rowData">
                            <div style="text-align: right;">
                                <Button
                                    severity="contrast"
                                    label="Scheda Cliente"
                                    size="small"
                                    :disabled="rowData.data.enterprise_id"
                                    @click="goToClient(rowData.data.id)"
                                />
                            </div>
                        </template>
                    </Column>
                </DataTable>
            </div>
        </div>
    </MasterLayout>
</template>