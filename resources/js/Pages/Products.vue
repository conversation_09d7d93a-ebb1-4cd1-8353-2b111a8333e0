<script setup>
import MasterLayout from "@/Layouts/MasterLayout.vue";

defineProps({
    products: Array,
})
</script>

<template>
    <MasterLayout title="Prodotti">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <a class="card p-6 border-2 hover:border-indigo-500 cursor-pointer duration-100" v-for="product in products" :href="`/products/${product.id}`">
                <div class="flex justify-between items-center">
                    <div>
                        <div class="text-lg font-bold">{{ product.name }}</div>
                        <p class="text-neutral-500">{{product.company.name}}</p>
                    </div>
                    <img :src="'/assets/companies/' + product.company.logo" alt="" class="h-14 w-14 rounded-full">
                </div>
            </a>
        </div>
    </MasterLayout>
</template>

<style scoped>

</style>
