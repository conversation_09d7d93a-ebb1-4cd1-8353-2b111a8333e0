<script>
import { useForm } from '@inertiajs/vue3'
import MasterLayout from '../Layouts/MasterLayout.vue'
import Card from 'primevue/card'
import FileUpload from 'primevue/fileupload'
import Obscure from '../Components/Obscure.vue'
import FileList from '../Components/Content/FileList.vue'
import PipelineShort from '../Components/Content/PipelineShort.vue'
import Debug from '../Components/Debug.vue'
import IssuanceList from '../Components/Content/IssuanceList.vue'
import User from '../user'
import IssuanceUpload from '../Components/Content/IssuanceUpload.vue'
import SalesmanShort from '../Components/Content/SalesmanShort.vue'
import PipelineTimeline from '../Components/Content/PipelineTimeline.vue'
import { dateFormatter } from '../dateFormatter'
import EPMessage from '../Components/EPMessage.vue'

export default {
    props: {
        pipeline: Object,
        subjects: Array,
        files: Array,
        products: Array,
        issuances: Array,
        mapperResult: Object,
        user: Object,
    },
    components: {
        MasterLayout,
        Card,
        FileUpload,
        Obscure,
        FileList,
        PipelineShort,
        Debug,
        IssuanceList,
        IssuanceUpload,
        SalesmanShort,
        PipelineTimeline,
        EPMessage,
    },
    setup() {
        return {
            form: useForm({
                file: null,
                issuance_id: null,
            }),
            User,
            dateFormatter,
        }
    },
    methods: {
        submit(form) {
            form.post(`/tasks/issuance/${this.pipeline.id}/${this.pipeline.currentTask.id}/upload`, {
                forceFormData: true,
                
                onFinish: () => {
                    this.$inertia.reload({ preserveState: true, preserveScroll: true });
                }
            });
        }
    },
}
</script>

<template>
    <MasterLayout :title="`Scheda Posizione #${pipeline.id} del ${dateFormatter(pipeline.created_at)}`" :user="user">
        <div class="flex flex-col gap-5">
            <div class="grid grid-cols-12 gap-6">
                <div class="col-span-4 flex flex-col gap-4">
                    <!-- Client -->
                    <Card>
                        <template #title>
                            <i class="pi pi-users"></i>
                            Soggetti
                        </template>

                        <template #content>
                            <div v-for="client in subjects">
                                <h4 v-if="client.role == 'contractor'">
                                    Contraente
                                </h4>
                                <div v-if="client.person" class="mt-2">
                                    <a :href="`/clients/${client.person.id}`" v-tooltip.top="'Vai alla scheda cliente'">
                                        {{ client.person.name }} {{ client.person.lastname }}
                                    </a><br>

                                    <Obscure>
                                        {{ client.person.taxCode }}<br>
                                        {{ client.person.phone }}<br>
                                        {{ client.person.email }}
                                    </Obscure>
                                </div>
                            </div>
                        </template>
                    </Card>

                    <!-- Salesman -->
                    <Card>
                        <template #title>
                            <i class="pi pi-briefcase"></i>
                            Consulente
                        </template>
                        <template #content>
                            <SalesmanShort :salesman="pipeline.user"></SalesmanShort>
                        </template>
                    </Card>

                    <!-- Documents -->
                    <Card>
                        <template #title>
                            <i class="pi pi-folder"></i>
                            Documenti
                        </template>
                        <template #content>
                            <span v-if="files.length == 0">Nessun documento caricato.</span>
                            <FileList :list="files"></FileList>
                        </template>
                    </Card>
                </div>

                <!-- Center -->
                <div class="col-span-8 flex flex-col gap-4">
                    <Card v-if="pipeline.currentTask?.type == 'issuance' && issuances.length">
                        <template #title>
                           Emissioni
                        </template>
                        <template #content>
                            <EPMessage v-if="! pipeline.currentTask.data?.taskCompleted" severity="info" title="Caricamento documenti" icon="pi pi-info-circle">
                                I prodotti elencati sono in fase di emissione. Se richiesto carica la proposta o polizza assicurativa per proseguire con il processo.
                            </EPMessage>

                            <EPMessage v-else severity="success" title="Compilazione completata" icon="pi pi-check">
                                Le schede sono state completate e i documenti di polizza sono pronti. Il consulente deve procedere con la firma per completare il processo.
                            </EPMessage>

                            <IssuanceUpload :pipeline="pipeline" :issuances="issuances" />
                        </template>
                    </Card>

                    <Card>
                        <template #title>
                            Dettaglio posizione
                        </template>
                        <template #content>
                            <!-- Pipeline Timeline -->
                            <PipelineTimeline :pipeline="pipeline" :mapperResult="mapperResult" :issuances="issuances" />
                        </template>
                    </Card>
                </div>
            </div>
        </div>
    </MasterLayout>
</template>
