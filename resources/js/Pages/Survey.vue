<script setup>
import {ref} from "vue";
import { useForm } from "@inertiajs/vue3";
import PipelineLayout from '../Layouts/PipelineLayout.vue';
import MasterLayout from "../Layouts/MasterLayout.vue";
import {ArrowSmallRightIcon, ArrowSmallLeftIcon, QuestionMarkCircleIcon, EllipsisHorizontalIcon, CheckIcon} from "@heroicons/vue/24/outline";
import Button from "@/Components/Button.vue";
import Text from "../Components/Form/Text.vue";
import Date from "../Components/Form/Date.vue";
import Checkbox from "../Components/Form/Checkbox.vue";
import CheckboxGroup from "@/Components/Form/CheckboxGroup.vue";
import RadioGroup from "../Components/Form/RadioGroup.vue";
import Markup from "../Components/Form/Markup.vue";
import PipelineInfo from "@/Components/PipelineInfo.vue";

let props = defineProps({
    fd: Object,
    fv: Object,
    task: Object,
    pipeline: Object
})

</script>

<template>
    <PipelineLayout :current="task">

        todo
        
    </PipelineLayout>
</template>

<script>

import {directive} from "vue-tippy";
import {ref} from "vue";
import {router} from "@inertiajs/core";
import {map} from "lodash";

let step = ref(0)
let direction = ref("");

export default {
    components: {
        Text,
        Date,
        Checkbox,
        CheckboxGroup,
        RadioGroup,
        Markup,
        MasterLayout
    },
    props: ['fd'],
    directives: {
        tippy: directive,
    },
    methods: {
        
    }
}
</script>
