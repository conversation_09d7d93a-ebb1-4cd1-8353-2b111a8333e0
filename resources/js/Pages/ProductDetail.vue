<script setup>
import MasterLayout from "@/Layouts/MasterLayout.vue";

defineProps({
    product: Object,
})
</script>

<template>
    <MasterLayout title="Dettaglio Prodotto">
        <div class="flex">
            <div class="card w-full">
                <div class="card-body !p-8">

                    <div class="flex items-center mb-5">
                        <div class="mr-3">
                            <img :src="'/assets/companies/' + product.company.logo" alt="" class="h-16 w-16 rounded-full">
                        </div>
                        <div>
                            <div class="text-2xl font-bold">{{ product.name }}</div>
                            <p class="text-neutral-500">{{product.company.name}}</p>
                        </div>
                    </div>

                    <div class="text-xl text-center text-blue-800 pb-3 border-b-2">Garanzie / coperture base</div>

                    <div class="flex flex-row mb-8">
                        <div class="basis-1/2 border-r-2 p-5">
                            <div class="font-semibold pb-1">Obbligatorie</div>
                            <ul class="list-disc pl-5">
                                <li v-for="coverage in filterCoverageByType(product.coverages, 'main', 'standard')">{{ coverage.name }}</li>
                            </ul>
                        </div>
                        <div class="basis-1/2 p-5">
                            <div class="font-semibold  pb-1">Facoltative</div>
                            <ul class="list-disc pl-5">
                                <li v-for="coverage in filterCoverageByType(product.coverages, 'main', 'optional')">{{ coverage.name }}</li>
                            </ul>
                        </div>
                    </div>

                    <div class="text-xl text-center text-blue-400 pb-3 border-b-2">Garanzie e coperture accessorie / complementari</div>

                    <div class="p-5 mb-8">
                        <ul class="list-disc pl-5">
                            <li v-for="coverage in filterCoverageByType(product.coverages, 'complementary')">{{ coverage.name }}</li>
                        </ul>
                    </div>

                    <div class="flex flex-row">
                        <div class="basis-1/2 border-r-2">
                            <div class="text-xl text-center text-red-700 pb-3 border-b-2 mb-5">Vincoli Assuntivi</div>
                            <div class="flex mb-1" v-if="product.minAge || product.maxAge">
                                <div class="font-bold mr-1">Età:</div>
                                <div>Min. {{product.minAge}} -> Max. {{product.maxAge}}</div>
                            </div>
                            <div class="flex" v-if="product.minLen || product.maxLen">
                                <div class="font-bold mr-1">Durata:</div>
                                <div>Min. {{product.minLen}} -> Max. {{product.maxLen}}</div>
                            </div>
                        </div>
                        <div class="basis-1/2">
                            <div class="text-xl text-center text-red-700 pb-3 border-b-2">Target Market negativo</div>

                        </div>
                    </div>


                </div>
            </div>
        </div>
    </MasterLayout>
</template>

<script>
export default {
    methods: {
        filterCoverageByType(coverages, type, subtype = null) {
            let coveragesClone = [];
            coverages.forEach(function (item, index) {
                if (!subtype) {
                    if (item.type === type) {
                        coveragesClone.push(item);
                    }
                }
                else {
                    if ( item.type === type && item.pivot.setup === subtype ) {
                        coveragesClone.push(item);
                    }
                }
            })

            return coveragesClone;
        }
    }
}
</script>
