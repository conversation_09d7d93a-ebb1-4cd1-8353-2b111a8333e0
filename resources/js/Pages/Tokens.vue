
<script setup>
import MasterLayout from '../Layouts/MasterLayout.vue';
import { router } from '@inertiajs/vue3'
import { Link } from '@inertiajs/vue3';

defineProps({
    user: Object,
    token: String,
    foo: String,
})
</script>

<template>
    <MasterLayout>

        <div class="border-2 rounded-lg shadow-md p-5">
            <h1 class="font-bold text-lg">Tokens</h1>

            <div class="font-bold">state: {{ foo || "invalid" }}</div>

            <!--<a href="/tokens?make=1">Get Token</a>-->
            <button class="btn btn-secondary" @click="reload()">Reload</button><br>
            <button class="btn btn-primary" @click="post()">Create Token</button><br>
            <button class="btn btn-secondary" @click="fooTest()">Call another endpoint</button><br>

            <div>
                Tokens:
                <ul>
                    <li v-for="token in user.tokens">
                        <strong>{{ token.name }}</strong> // expires:{{ token.expires_at || "never" }}
                        <Link :href="`/tokens/${token.id}`" as="button" :only="['user']" method="delete">Delete</Link>
                    </li>
                </ul>
            </div>

            Token: <span>{{ token }}</span>
        </div>

    </MasterLayout>

</template>

<script>
export default {
    methods: {
        del() {
            router.delete('/tokens');
        },
        reload() {
            router.reload({ only: ['tech'] })
        },
        post() {
            router.post('/tokens', {}, {only: ["user", "token"]})
        },
        fooTest() {
            router.post('/foo', {}, {only: ["foo"]})
        }
    }
}
</script>

