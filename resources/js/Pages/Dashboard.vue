<script setup>
import MasterLayout from '../Layouts/MasterLayout.vue';
import Button from 'primevue/button';
import State from '../Components/State.vue';
import {TrashIcon, ChevronDownIcon, PencilIcon, MagnifyingGlassIcon, PlusIcon} from '@heroicons/vue/24/outline';
import {Menu, MenuButton, MenuItems, MenuItem} from '@headlessui/vue';
import InputText from 'primevue/inputtext';
import { ref } from 'vue';
import User from '../user';
import Pipeline from '../Models/pipeline';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Toolbar from 'primevue/toolbar';
import HeroEmpty from '../Components/HeroEmpty.vue';
import SplitButton from 'primevue/splitbutton';

defineProps({
    pipelines: Object,
    user: Object
})

let f = ref({
    'global': {
        value: '',
        matchMode: 'contains'
    },
    'state': {
        value: '',
        matchMode: 'contains'
    },
    'user.lastname': {
        value: '',
        matchMode: 'contains'
    },
    'client.lastname': {
        value: '',
        matchMode: 'contains'
    },
    'currentTask.displayName': {
        value: '',
        matchMode: 'contains'
    }
})

</script>

<template>
    <MasterLayout title="Dashboard">
        <div v-if="!pipelines.length" class="card min-h-[60vh] flex items-center justify-center">
            <HeroEmpty :title="`Ciao ${user.name}!`" message="Qui puoi gestire tutte le tue posizioni e attività.">
                <Button
                    severity="primary"
                    class="w-60 mt-10"
                    label="Apri una nuova posizione"
                    @click="Pipeline.create()"
                />
            </HeroEmpty>
        </div>
        <div v-else class="card">

            <div class="card-header">
                <Toolbar style="border: 0">
                    <template #start>
                        <h1 class="mb-0">Lista posizioni</h1>
                    </template>

                    <template #end>
                        <Button v-if="User.is(user, 'salesman')" severity="primary" icon="pi pi-plus" label="Nuova posizione" @click="Pipeline.create()" />
                    </template>
                </Toolbar>
                
            </div>

            <div class="card-body">

                <div class="max-md:overflow-x-auto pb-10">
                    <DataTable 
                            dataKey="id"
                            :value="pipelines"
                            v-model:filters="f" 
                            filterDisplay="menu" 
                            :globalFilterFields="['state', 'user.lastname', 'client.name', 'currentTask.displayName']"
                            paginator 
                            :rows="50" 
                            tableStyle="min-width: 50rem" 
                            class="text-sm"
                            >

                            <template #header>
                                <div class="flex justify-end">
                                    <IconField>
                                        <InputIcon>
                                            <i class="pi pi-search" />
                                        </InputIcon>
                                        <InputText v-model="f['global'].value" placeholder="Cerca ovunque..." />
                                    </IconField>

                                    <!--<button type="link" class="btn btn-primary btn-icon mr-2" @click="exportCSV()">Download</button>-->
                                </div>
                            </template>

                    <Column field="id" header="#" sortable style="width: 5%;"></Column>

                    <Column field="state" header="Stato" style="width: 15%;">
                        <template #body="data">
                            <State v-if="data.data.state == 'closed'" :entity="data.data" />
                            <State v-else :text="data.data.currentTask?.displayName" severity="warn" />
                        </template>
                        <!--
                        <template #filter="{ filterModel, filterCallback }" class="w-full">
                            <select v-model="filterModel.value" @change="filterCallback()" class="border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                                <option value="">Tutti</option>
                                <option value="open">Aperta</option>
                                <option value="closed">Chiusa</option>
                            </select>
                        </template>
                    -->
                    </Column>

                    <Column field="user.lastname" v-if="User.is(user, 'manager')" header="Utente" style="width: 20%" sortable>
                        <template #body="data">
                            <a :href="`/salesmen/${data.data.user.id}`">{{ data.data.user.lastname }}, {{ data.data.user.name }}</a>
                            <br />
                            <span class="text-xs text-gray-400">{{ data.data.user.node?.name }}</span>
                        </template>
                        <template #filter="{ filterModel, filterCallback }">
                            <InputText v-model="filterModel.value" size="small" type="text"  placeholder="Cognome..." />
                        </template>
                    </Column>

                    <Column field="client.name" header="Cliente" style="width: 25%" sortable>
                        <!--
                        <template #filter="{ filterModel, filterCallback }">
                            <InputText v-model="filterModel.value" size="small" style="width: 100px;" @input="filterCallback()" type="text"  placeholder="Cognome..." />
                        </template>
                        -->
                        <template #body="data">
                            <span v-if="data.data.client">
                                <a :href="`/clients/${data.data.client?.id}`">{{ data.data.client?.name }}</a><br>
                                <span class="text-xs text-gray-400">{{ data.data.type == 'individual' ? 'Persona fisica' : 'Persona giuridica' }}</span>
                            </span>
                            <span v-else>
                                -
                            </span>
                        </template>
                    </Column>

                    <Column field="updated_at" header="Ultimo aggiornamento" style="width: 7%" sortable>
                        <template #body="data">
                            {{ formatDateTime(data.data.updated_at) }}
                        </template>
                    </Column>

                    <Column>
                        <template #body="data">
                            <div class="flex justify-end">
                                <SplitButton
                                    label="Azioni"
                                    severity="contrast"
                                    icon="pi pi-cog"
                                    size="small"
                                    :model="
                                        [
                                            ...(User.is(user, 'salesman') && data.data.state != 'closed' ? [{
                                                label: 'Riprendi',
                                                icon: 'pi pi-pencil',
                                                command: () => exec('resume', data.data.id)
                                            }] : []),
                                            ...(data.data.state != 'closed' ? [{
                                                label: 'Scheda',
                                                icon: 'pi pi-search',
                                                command: () => exec('view', data.data.id)
                                            },
                                            {
                                                label: 'Elimina',
                                                icon: 'pi pi-trash',
                                                command: () => exec('remove', data.data.id)
                                            }] : [{
                                                label: 'Visualizza',
                                                icon: 'pi pi-eye',
                                                command: () => exec('view', data.data.id)
                                            }]),
                                            ...(data.data.state == 'closed' && data.data.files && data.data.files.length ? [
                                                { separator: true },
                                                { label: 'Documenti', disabled: true },
                                                ...data.data.files.map(file => ({
                                                    label: file.displayName ?? file.document?.title,
                                                    icon: 'pi pi-download',
                                                    url: () => `/documents/${file.uuid}`,
                                                    target: '_blank'
                                                }))
                                            ] : [])
                                        ]
                                    "
                                />
                            </div>
                        </template>
                    </Column>
                    </DataTable>
                </div>
            </div>
        </div>
    </MasterLayout>

</template>
<script>

import {directive} from 'vue-tippy'
import {router} from '@inertiajs/core';
import Badge from '../Components/Badge.vue';


export default {
    directives: {
        tippy: directive,
    },
    methods: {
        resume: function (id) {
            location.href = `pipeline/${id}/resume`;
        },
        remove: function (id) {
            if (!confirm("Sicuro?")) {
                return;
            }

            router.delete(`pipeline/${id}`, {only: ["pipelines"], preserveScroll: true})
        },
        
        view: function (id) {
            location.href = `pipeline/${id}`;
        },

        exec: function (action, id) {
            this[action](id);
        },

        formatDateTime: function (date) {
            return new Date(date).toLocaleDateString('it-IT');
        }
    },
    components: {Badge}
}
</script>
