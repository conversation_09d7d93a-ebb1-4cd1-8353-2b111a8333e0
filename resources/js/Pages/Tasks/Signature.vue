<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import Message from 'primevue/message';
import {router} from "@inertiajs/core";
import { ref } from 'vue';

import <PERSON><PERSON> from 'primevue/button';
import Debug from '../../Components/Debug.vue';
import File from '../../Components/Content/File.vue';
import EPMessage from '../../Components/EPMessage.vue';
import HeroEmpty from '../../Components/HeroEmpty.vue';
import PipelineModel from '../../Models/pipeline.js';
import { dateFormatter } from '../../dateFormatter.js';


const env = import.meta.env;

export default {
    props: {
        pipeline: Object,
        task: Object,
        signers: Array,
        documents: Array,
        foo: Object,
        folderId: Number,
    },

    components: {
        PipelineLayout,
        Button,
        Message,
        Debug,
        File,
        EPMessage,
        HeroEmpty,
        PipelineModel,
    },

    data() {
        return {
            loading: ref(false),
            userConfirmed: false,

        }
    },

    setup() {
        return {
            env,
            PipelineModel,
            dateFormatter,
        }
    },  

    methods: {
        next: function() {
            this.loading = true;
            
            router.get(`/pipeline/${this.pipeline.id}/next`, {}, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    this.loading = false;
                },
                onError: () => {
                    this.loading = false;
                },
            });
        },

        sign: function() {
            this.loading = true;

            router.post(`/sign/${this.pipeline.id}/${this.task.id}`, {}, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    this.loading = false;
                },
                onError: () => {
                    this.loading = false;
                },
            });
        }
    }
}

</script>

<template>
    <PipelineLayout :current="task">

        <Debug :float="true">
            <a :href="`/sign/${pipeline.id}/${task.id}`">
                <i class="pi pi-file-edit"></i>
                Forza nuova firma (Modalità di test)
            </a>
        </Debug>

        <Debug :float="true">
            <a :href="`/sign/fake/${task.id}`">
                <i class="pi pi-file-edit"></i>
                Firma fake
            </a>
        </Debug>

        <div class="flex flex-col gap-5">
            
            <!-- Before start: files ready to sign. -->
            <template v-if="! task.data.signatureStatus">
                <!-- Documents -->
                <div class="card w-full">
                    <div class="card-body !p-8">
                        <div>
                            <h2 class="mb-2">Documenti nel fascicolo</h2>

                            <EPMessage severity="neutral" icon="pi pi-info-circle">
                                <p class="font-light">I documenti sono pronti per la firma. Prima di procedere controlla la correttezza dei dati.</p>
                            </EPMessage>

                            <span v-for="file in documents">
                                <File :file="file"></File>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Signers: check contact data -->
                <div class="card w-full">
                    <div class="card-body !p-8">
                        <div v-for="signer in signers" :key="signer.id" class="text-xl text-gray-700">
                            <HeroEmpty title="Firmatario" message="Controlla attentamente i dati di contatto">
                                <div class="flex items-center gap-2 mt-5">
                                    <i class="pi pi-user"></i>
                                    <span>{{ signer.name }} {{ signer.lastname }}</span>
                                </div>
                                <div class="flex items-center gap-2 mt-1">
                                    <i class="pi pi-envelope"></i>
                                    <span>{{ signer.email }}</span>
                                </div>
                                <div class="flex items-center gap-2 mt-1">
                                    <i class="pi pi-phone"></i>
                                    <span>{{ signer.phone }}</span>
                                </div>

                                <div class="flex mt-1">
                                    <span class="text-sm mt-1">
                                        <a class="cursor-pointer" @click="PipelineModel.previous(pipeline)">Clicca qui se vuoi correggere i dati</a>
                                    </span>
                                </div>
                                
                            </HeroEmpty>
                            
                        </div>
                        <div class="flex justify-center">
                            <Button
                                v-if="! userConfirmed"
                                @click="userConfirmed = true"
                                class="p-button p-component p-button-info mb-5">
                                
                                Ho verificato i dati e sono pronto a procedere
                            </Button>
                        </div>
                    </div>
                </div>

                <!-- Signature action. -->
                <div class="card w-full" v-if="userConfirmed">
                    <div class="card-body !p-8">
                        <div class="mx-auto max-w-md">
                            <div class="hero-title text-center w-full">Firma</div>
                            <EPMessage v-if="!task.data.signatureStatus" severity="neutral" icon="pi pi-info-circle">
                                <p class=p-8>
                                    <p>Il cliente riceverà un messaggio con le indicazioni per firmare i documenti. </p>
                                    <p class="mt-3">Dopo la firma, il fascicolo sarà disponibile per il download e potrai procedere con il prossimo step.</p>
                                </p>
                            </EPMessage>
                            <div class="flex justify-center mt-4">
                                <Button
                                    :loading="loading"
                                    @click="sign()"
                                    severity="info"
                                    :href="`/sign/${pipeline.id}/${task.id}`">
                                    Invia i documenti per la firma
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Signature in progress. -->
            <template v-else>
                <Debug :float="true">
                    <a :href="`/signature/${pipeline.id}/${task.id}/callback/igs/${task.data.folder.id}/11`" target="_blank">Test callback SUCCESS</a>
                </Debug>
                <div class="card w-full">
                    <div class="card-body !p-8">
                        <HeroEmpty v-if="task.data.signatureStatus == 'pending'" title="Il cliente ha ricevuto il fascicolo" message="Attendi che il cliente firmi i documenti." icon="pi pi-file-edit">
                            <p class="text-5xl">
                                <i class="pi pi-hourglass text-5xl !important"></i>
                            </p>
                            <p class="text-sm text-gray-500 mt-5 ">
                                Scadenza fascicolo: {{ dateFormatter(pipeline.currentTask.data?.folder?.expires_at) }}
                            </p>
                        </HeroEmpty>

                        <HeroEmpty v-if="task.data.signatureStatus == 'done'" title="Firmato" message="Il fascicolo è stato firmato correttamente.">
                            <span class="">
                                <i class="pi pi-check "></i>
                            </span>
                        </HeroEmpty>

                        <HeroEmpty v-if="task.data.signatureStatus == 'fail'" title="Errore" message="La firma del fascicolo è fallita o è scaduta.">
                            <span class="">
                                <i class="pi pi-check "></i>
                            </span>
                        </HeroEmpty>
                    </div>
                </div>
            </template>

            <div class="flex justify-between">
                <Button type="button" label="Indietro" :disabled="true"></Button>
                <Button type="button" label="Avanti" @click="next()" :loading="loading" :disabled="task.data.signatureStatus != 'done'"></Button>
            </div>
            
        </div>

    </PipelineLayout>
</template>
