<script setup>
import { ArrowRightIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
import PipelineLayout from '../../../Layouts/PipelineLayout.vue';
import {CheckBadgeIcon} from "@heroicons/vue/24/solid";
 
defineProps({
    pipeline: Object,
    task: Object,
    form: Object,
    results: Object,
    error: String
})
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-row space-x-4">
            <div class="basis-8/12">
                <div class="card">
                    <div class="card-body !p-8">
                        <div v-if="error" class="border-solid border-2 rounded-md border-red-300 p-3 bg-red-100">
                            <ExclamationTriangleIcon class="h-5 w-5 inline-block"></ExclamationTriangleIcon>
                            {{ error }}
                            
                        </div>
                        <hr v-if="error" class="my-5">

                        <div class="text-xl font-bold mb-5">Demands And Needs</div>
                        <template v-for="(section, index) in form.cachedResult.sections">
                            <div class="section-header mb-2">{{ section.title }}</div>

                            <template v-for="qa in section.qa">
                                <div :class="{'flex': qa.a.length == 1}">
                                    <div class=" mr-1">{{ qa.q }}</div>
                                    <div class="font-semibold">{{ qa.a }}</div>
                                    
                                </div>
                                <hr class="my-3">
                            </template>
                        </template>
                    </div>
                </div>
            </div>

            <div class="basis-4/12">
                <div class="card">
                    <div class="card-body !p-8">
                        <div class="text-xl font-bold">Prodotto selezionato</div>
                        <div class="rounded-lg bg-white shadow p-6 transition-all w-full relative mb-3" v-for="product in results">
                            <div>
                                <div class="font-bold">{{ product.product.name }}</div>
                                <p class="text-neutral-500 mb-3">{{product.product.company.name}}</p>
                            </div>
                            <img :src="'/assets/companies/' + product.product.company.logo" alt="" class="absolute top-6 right-6 h-14 w-14 rounded-full">
                        </div>
                        
                        <div v-if="pipeline.state == 'closed'">
                            <button class="btn btn-primary btn-icon w-full mr-2" @click="go(pipeline)">
                                Torna alla pratica EGG
                                <ArrowRightIcon class="h-5 w-5 mr-1" />
                            </button>
                        </div>

                        <div v-if="pipeline.state != 'closed'">
                            <div class="bg-amber-100 mb-3 p-3 rounded-md">
                                Cliccando sul pulsante <strong>il sistema EasyProfile registrerà il processo D&N</strong> per 
                                la verifica del prodotto coerente e sarai reindirizzato sul sistema EGG per la proposta di polizza.<br>
                            </div>
                            <button class="btn btn-primary btn-icon w-full mr-2" @click="go(pipeline, true)">
                                Finalizza scelta prodotto coerente
                                <ArrowRightIcon class="h-5 w-5 mr-1" />
                            </button>
                        </div>
                    </div>

                    
                </div>  
            </div>

        </div>
    </PipelineLayout>
</template>

<script>
import {router} from "@inertiajs/core";

export default {
    methods: {
        go(pipeline, shouldAskConfirm) {
            if (shouldAskConfirm && ! confirm('La posizione sarà finalizzata e non potrai più modificarla. Procedere?')) {
                return;
            }

            router.post(
                `/dorotea/${pipeline.id}/summary`,
                {},
                {
                    only: ['error'],
                    preserveScroll: true
                }
            )
        }
    }
}
</script>