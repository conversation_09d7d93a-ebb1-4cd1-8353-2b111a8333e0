<script>

import {directive} from "vue-tippy";
import {ref} from "vue";
import {router} from "@inertiajs/core";
import {useForm} from "@inertiajs/vue3";
import MasterLayout from "@/Layouts/MasterLayout.vue";
import {ArrowSmallRightIcon, ArrowSmallLeftIcon, EllipsisHorizontalIcon, CheckIcon, CheckCircleIcon, ExclamationTriangleIcon} from "@heroicons/vue/24/outline";
import PipelineLayout from "@/Layouts/PipelineLayout.vue";
import Text from "@/Components/Form/Text.vue";
import Date from "@/Components/Form/Date.vue";
import Checkbox from "@/Components/Form/Checkbox.vue";
import CheckboxGroup from "@/Components/Form/CheckboxGroup.vue";
import RadioGroup from "@/Components/Form/RadioGroup.vue";
import Markup from "@/Components/Form/Markup.vue";

export default {
    props: {
        fd: Object,
        fv: Object,
        task: Object,
        errors: Object,
    },
    components: {
        PipelineLayout,
        Text,
        Date,
        Checkbox,
        CheckboxGroup,
        RadioGroup,
        Markup,
        MasterLayout,
        ArrowSmallRightIcon, ArrowSmallLeftIcon, EllipsisHorizontalIcon, CheckIcon, CheckCircleIcon, ExclamationTriangleIcon
    },
    setup(props) {

        let formDefinition = ref(props.fd)
        let step = ref(0);
        let direction = ref("");
        let showLoader = ref(false);
        const coverageAreas = [996,995,994];

        let vueForm = {}

        function setupSection(section)
        {
            section.items.forEach(element => {

                if (element.component === 'CheckboxGroup') {
                    vueForm[element.name] = props.fv ? props.fv[element.name] : [];

                    return
                }

                vueForm[element.name] = props.fv ? props.fv[element.name] : element.defaultValue ? element.defaultValue : null;
            });
        }

        let sectionIndex = -1;
        formDefinition.value.sections.forEach(section => {
            sectionIndex++;
            section["index"] = sectionIndex;
            if (! section.isLast) {
                setupSection(section)
            }
        })

        // Add a control variable.
        vueForm['sectionFields'] = null;

        const form = useForm(vueForm)

        return {
            formDefinition,
            form,
            step,
            direction,
            showLoader,
            coverageAreas
        }
    },
    directives: {
        tippy: directive,
    },
    mounted() {
        this.step = 0;

        if ( this.form['profile/choice'] === 'constrained' ) {
            let areaHouse = this.$el.querySelector("input[name='profile/areaHouse']");
            let coverageFire = this.$el.querySelector("input[name='fire.building']");
            areaHouse.closest('div').classList.add('disabled');
            areaHouse.closest('div').nextSibling.classList.add('disabled');
            areaHouse.checked = true;
            areaHouse.disabled = true;

            this.formDefinition.sections[3].items[0].isHidden = false;

            coverageFire.parentNode.parentNode.classList.add('disabled');
            coverageFire.checked = true;
            coverageFire.disabled = true;

            // Nascondo i checkbox delle altre area di interesse
            this.formDefinition.sections[2].items[5].isHidden = true;
            this.formDefinition.sections[2].items[6].isHidden = true;
            this.fd.sections[2].items[5].isHidden = true;
            this.fd.sections[2].items[6].isHidden = true;
            // Nascondo i checkbox group delle garanzie non casa
            this.formDefinition.sections[3].items[1].isHidden = true;
            this.formDefinition.sections[3].items[2].isHidden = true;

            // Nascondo i singoli checkbox (dentro il checkbox group della casa) corrispondenti alle garanzie casa non collegate all'obbligo contrattuale
            let that = this;
            this.formDefinition.sections[3].items[0].options.forEach(function (item, index) {
                if (index !== 0) {
                    that.formDefinition.sections[3].items[0].options[index].isHidden = true;
                }
            })

            this.fd.sections[3].items[0].options.forEach(function (item, index) {
                if (index !== 0) {
                    that.fd.sections[3].items[0].options[index].isHidden = true;
                }
            })
        }

    },
    methods: {
        // In pratica ho fatto 2 set di transizioni uno per quando il form si sposta in avanti (left)
        // e uno per quando si sposta indietro (right).
        // I 2 set vengono assegnati dinamicamente ai 4 step della transizione.
        handleAnimation(type) {
            // Prendo le 2 stringe che vengono dinamicamente assegnate nella direttiva
            let transitions = type.split('-');
            // Composizione stringa [enter|exit]-[left|right]-[from|to]
            return `${transitions[0]}-${this.direction}-${transitions[1]}`;
        },
        handleStep(stepDirection, fd, section) {
            if (stepDirection === 'forw') {

                // TESTING UI
                /*this.step++;
                this.direction = 'left';*/

                // Post along current section fields.
                this.form.sectionFields = section.items.map(item => { return item.name })
                this.showLoader = true;

                this.form.post(`/tasks/survey/${fd.pipeline_id}/${fd.task_id}?mode=data`, {

                    // Prevent response to overwrite and invalidate other props.
                    only: ['errors'],

                    onFinish: () => {
                        this.showLoader = false;
                    },

                    onSuccess: () => {
                        this.step++;
                        this.direction = 'left';
                        router.post(
                            `/tasks/survey/${fd.pipeline_id}/${fd.task_id}?mode=meta`,
                            {
                                fd: fd,
                                fv: this.form
                            },
                            {
                                only: ['fd', 'fv'],
                                preserveScroll: true
                            }
                        )
                    }
                })

            }
            else if (stepDirection === 'back') {
                this.step--;
                this.direction = 'right';
            }
        },
        formatTaskLabel(type) {
            const labels = {
                "survey" : "Questionario",
                "mapper" : "Prodotti",
                "summary" : "Sommario profilo",
            }
            return labels[type];
        },
        getStateColors(state) {
            const colors = {
                "progress" : {bgr: "bg-blue-500", text: "font-bold text-blue-600"},
                "closed" : {bgr: "bg-green-600", text: "text-zinc-400"},
                "open" : {bgr: "bg-zinc-400", text: "text-zinc-400"},
            }
            return colors[state];
        },
        handleTriggerValue (triggerValue = null, triggerNegative = null, callBack = null, value = null) {

            if (triggerValue || triggerNegative) {

                let that = this;
                this.formDefinition.sections.map(function(value1, key1) {
                    if (typeof value1.items !== 'undefined') {
                        value1.items.map(function (value2, key2) {
                            if ( value2.id === triggerValue) {
                                that.formDefinition.sections[key1].items[key2].isHidden = !that.formDefinition.sections[key1].items[key2].isHidden
                                that.fd.sections[key1].items[key2].isHidden = !that.fd.sections[key1].items[key2].isHidden
                            }
                            if ( value2.id === triggerNegative) {
                                that.formDefinition.sections[key1].items[key2].isHidden = true
                                that.fd.sections[key1].items[key2].isHidden = true
                            }
                        })
                    }
                });

                // Nel caso in cui l'utente abbia deselezionato un checkbox legato ad un'area di interesse, deseleziono tutte le checkbox (garanzie) legate alla relativa area
                // svuotando il relativo array di valori nel form
                if ( this.coverageAreas.includes(triggerValue) && !value ) {
                    this.uncheckLinkedCoverages(triggerValue);
                }

            }

        },
        handleCustomRule(customCallback = null, value) {

            // Regola custom: se viene selezionato "No" alla domanda su mutui e finanziamenti, resetto tutto simulando un click su "scelta libera"
            if (customCallback && customCallback === 'setNeedToFree') {
                let choiceFree = this.$el.querySelector("input[name='profile.choice.free']");
                choiceFree.click();
            }

            // Regola custom: se viene selezionato "obbligo contrattuale", "protezione abitazione" è obbligatorio come interesse e "incendio" è obbligatorio come garanzia
            if (customCallback && customCallback === 'setMortgageCoverage') {

                // Selettori
                let areaHouse = this.$el.querySelector("input[name='profile/areaHouse']");
                let coverageFire = this.$el.querySelector("input[name='fire.building']");

                // L'utente ha selezionato "obbligo contrattuale"
                if (value === 'constrained') {

                    // Spunto e disabilito la checkbox di interesse su Casa
                    areaHouse.closest('div').classList.add('disabled');
                    areaHouse.closest('div').nextSibling.classList.add('disabled');
                    areaHouse.checked = true;
                    areaHouse.disabled = true;

                    // Mostro la domanda sulle garanzie per la Casa nella sezione successiva
                    this.formDefinition.sections[3].items[0].isHidden = false;
                    this.fd.sections[3].items[0].isHidden = false;

                    // Spunto e disabilito la checkbox di garanzia Incendio
                    coverageFire.parentNode.parentNode.classList.add('disabled');
                    coverageFire.checked = true;
                    coverageFire.disabled = true;

                    // Setto il valore delle garanzie nel form con l'unica garanzia utilizzabile
                    this.form['coverage/house'] = ['fire.building'];
                    this.form['coverage/assets'] = [];
                    this.form['coverage/illness'] = [];
                    this.form['coverage/injury'] = [];
                    this.form['coverage/protection'] = [];
                    this.form['profile/areaHouse'] = true;
                    this.form['profile/areaAssets'] = null;
                    this.form['profile/areaPerson'] = null;

                    // Nascondo i checkbox delle altre area di interesse
                    this.formDefinition.sections[2].items[5].isHidden = true;
                    this.formDefinition.sections[2].items[6].isHidden = true;
                    this.fd.sections[2].items[5].isHidden = true;
                    this.fd.sections[2].items[6].isHidden = true;

                    // Nascondo i checkbox group delle garanzie non casa
                    let that = this;
                    this.formDefinition.sections[3].items.forEach(function (item, index) {
                        if (index !== 0) {
                            that.formDefinition.sections[3].items[index].isHidden = true;
                        }
                    })
                    this.fd.sections[3].items.forEach(function (item, index) {
                        if (index !== 0) {
                            that.fd.sections[3].items[index].isHidden = true;
                        }
                    })

                    // Nascondo i singoli checkbox (dentro il checkbox group della casa) corrispondenti alle garanzie casa non collegate all'obbligo contrattuale
                    this.formDefinition.sections[3].items[0].options.forEach(function (item, index) {
                        if (index !== 0) {
                            that.formDefinition.sections[3].items[0].options[index].isHidden = true;
                        }
                    })

                    this.fd.sections[3].items[0].options.forEach(function (item, index) {
                        if (index !== 0) {
                            that.fd.sections[3].items[0].options[index].isHidden = true;
                        }
                    })

                }
                // L'utente ha selezionato "libera scelta"
                else if (value === 'free') {

                    // Despunto (non so quale sia il contrario di spuntare) e riabilito la checkbox di interesse su Casa
                    areaHouse.closest('div').classList.remove('disabled');
                    areaHouse.closest('div').nextSibling.classList.remove('disabled');
                    areaHouse.checked = false;
                    areaHouse.disabled = false;

                    let that = this;
                    // Nascondo tutte le domande nella sezione successiva
                    this.formDefinition.sections[3].items.forEach(function (item, index) {
                        that.formDefinition.sections[3].items[index].isHidden = true;
                    })
                    this.fd.sections[3].items.forEach(function (item, index) {
                        that.fd.sections[3].items[index].isHidden = true;
                    })

                    // Despunto e riabilito la checkbox di garanzia Incendio
                    coverageFire.parentNode.parentNode.classList.remove('disabled');
                    coverageFire.checked = false;
                    coverageFire.disabled = false;

                    // Resetto array del form contenente le garanzie
                    this.form['coverage/house'] = [];
                    this.form['profile/areaHouse'] = null;

                    // Mostro i checkbox delle altre area di interesse
                    this.formDefinition.sections[2].items[5].isHidden = false;
                    this.formDefinition.sections[2].items[6].isHidden = false;
                    this.fd.sections[2].items[5].isHidden = false;
                    this.fd.sections[2].items[6].isHidden = false;

                    // MOstro i singoli checkbox (dentro il checkbox group della casa) corrispondenti alle garanzie casa non collegate all'obbligo contrattuale
                    this.formDefinition.sections[3].items[0].options.forEach(function (item, index) {
                        that.formDefinition.sections[3].items[0].options[index].isHidden = false;
                    })

                    this.fd.sections[3].items[0].options.forEach(function (item, index) {
                        that.fd.sections[3].items[0].options[index].isHidden = false;
                    })

                }

            }
        },
        uncheckLinkedCoverages(id) {

            switch (id) {
                case 996:
                    this.form['coverage/house'] = [];
                    break;
                case 995:
                    this.form['coverage/assets'] = [];
                    break;
                case 994:
                    this.form['coverage/injury'] = [];
                    this.form['coverage/illness'] = [];
                    this.form['coverage/protection'] = [];
                    break;
            }

        }
    }
}
</script>

<style>
.v-enter-active,
.v-leave-active {
    transition: all 250ms ease
}

.enter-left-from { opacity: 0; transform: translateX(20%); }
.enter-left-to { opacity: 1; transform: translateX(0%); }

.exit-left-from { opacity: 1; transform: translateX(0%); }
.exit-left-to { opacity: 0; transform: translateX(-20%); }


.enter-right-from { opacity: 0; transform: translateX(-20%); }
.enter-right-to { opacity: 1; transform: translateX(0%); }

.exit-right-from { opacity: 1; transform: translateX(0%); }
.exit-right-to { opacity: 0; transform: translateX(20%); }

.fade-enter-active,
.fade-leave-active {
    transition: opacity 250ms ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

</style>

<template>
    <PipelineLayout :current="task">

        <!--        <div class="overflow-scroll bg-white" style="position: absolute; left: 0; top: 25%">
                    <pre>
                        {{fd.sections[3]}}
                    </pre>
                </div>-->

        <div class="card">

            <div class="card-body !p-8">

                <!--                <div class="flex pb-4 mb-4 border-b-2 border-zinc-200">
                                    <PipelineInfo :creation-date="pipeline.created_at" />
                                </div>-->


                <div class="flex flex-row">
                    <div class="basis-3/12 border-r-2 border-zinc-200 pr-5 pt-3">
                        <!--                                                <pre>{{formDefinition.sections}}</pre>-->
                        <div class="px-3">
                            <div class="relative w-full pb-6" v-for="(section, index) in formDefinition.sections">
                                <span class="absolute top-1 left-1 h-full w-0.5 bg-zinc-200" style="margin-left: 7px" v-if=" (index + 2) < formDefinition.sections.length && ! section.isLast"></span>
                                <div class="flex shrink-0 relative" v-if="! section.isLast">
                                    <div class="rounded-full flex shrink-0 items-center justify-center" :class="{'bg-blue-500' : section.index === step, 'bg-green-600' : section.index < step, 'bg-zinc-400' : section.index > step}" style="width: 24px; height: 24px">
                                        <ArrowSmallRightIcon v-if="section.index === step" class="text-white h-4 w-4"></ArrowSmallRightIcon>
                                        <EllipsisHorizontalIcon v-if="section.index > step" class="text-white h-4 w-4"></EllipsisHorizontalIcon>
                                        <CheckIcon v-if="section.index < step" class="text-white h-4 w-4"></CheckIcon>
                                    </div>
                                    <div class="ml-3">
                                        <div :class="section.index === step ? 'font-bold text-blue-600' : 'text-zinc-400'">{{section.title}}</div>
                                        <!--                                        <p class="mt-2 max-w-screen-sm text-sm text-gray-500">Maecenas finibus nec sem ut imperdiet. Ut tincidunt est ac dolor aliquam sodales. Phasellus sed mauris hendrerit, laoreet sem in, lobortis ante.</p>-->
                                        <!--                                        <span class="mt-1 block text-sm font-semibold text-blue-500">2007</span>-->
                                    </div>
                                </div>
                                <!--                                <div v-if="task.state === 'progress'" style="padding-left: 55px">
                                                                    <div v-for="(section, index) in formDefinition.sections">
                                                                        <span class="text-sm flex items-center"
                                                                              :class="{'text-sky-500 font-semibold' : section.index === step, 'text-gray-300' : section.index > step}"><ArrowSmallRightIcon class="h-4 w-4 inline" v-if="section.index === step" />{{ section.title }}</span>
                                                                    </div>
                                                                </div>-->
                            </div>
                        </div>
                    </div>
                    <div class="basis-9/12 pl-5 pt-3 relative">
                        <div v-if="Object.keys(errors).length" class="border-solid border-2 rounded-md border-red-300 p-3 bg-red-100">
                            <ExclamationTriangleIcon class="h-5 w-5 inline-block"></ExclamationTriangleIcon>
                            Correggere gli errori segnalati prima di proseguire.
                        </div>
                        <hr v-if="Object.keys(errors).length" class="my-3">

                        <transition name="fade">
                            <div v-if="showLoader" class="absolute h-full w-full flex items-center justify-center bg-[rgb(255,255,255)]/60 z-10 transition-opacity">
                                <span class="loader"></span>
                            </div>
                        </transition>

                        <template v-for="s in formDefinition.sections">

                            <div class="relative" v-show="s.index === step">

                                <div v-if="s.isLast">
                                    <div class="flex justify-center mb-3">
                                        <CheckCircleIcon class="h-40 w-40 text-green-500" />
                                    </div>
                                    <div class="section-header text-center font-bold">Questionario completato.</div>
                                    <p class="leading-6 text-black font-semibold mb-6 text-center">Nella prossima sezione potrai selezionare i prodotti da preventivare.</p>
                                    <div class="flex justify-center items-center">
                                        <button class="btn btn-secondary btn-icon mr-2" @click="handleStep('back')">
                                            <ArrowSmallLeftIcon class="h-5 w-5 mr-1" />
                                            Indietro
                                        </button>
                                        <a class="btn btn-primary btn-icon mr-2" :href="`/pipeline/${fd.pipeline_id}/next`">
                                            Prosegui
                                            <ArrowSmallRightIcon class="h-5 w-5 ml-1" />
                                        </a>
                                    </div>
                                </div>

                                <div class="section-header" :class="{'mb-2' : !s.description}">{{ s.title }}</div>
                                <p class="text-sm leading-6 text-gray-500 mb-6" v-if="s.description">{{s.description}}</p>

                                <form @submit.prevent="handleStep('forw', fd, s)">
                                    <template v-for="i in s.items">
                                        <component :is="i.component" :field="i" :errors="form.errors" :orientation="form.orientation" v-model="form[i.name]" v-show="!i.isHidden" @custom-change="handleTriggerValue" @custom-rule="handleCustomRule"></component>
                                    </template>
                                    <div class="flex justify-center" v-if="step !== formDefinition.sections.length - 1">
                                        <button type="button" class="btn btn-secondary btn-icon mr-2" v-if="step > 0" @click="handleStep('back')">
                                            <ArrowSmallLeftIcon class="h-5 w-5 mr-1" />
                                            Indietro
                                        </button>
                                        <button type="submit" class="btn btn-primary btn-icon mr-2" >
                                            Avanti
                                            <ArrowSmallRightIcon class="h-5 w-5 ml-1" />
                                        </button>
                                    </div>
                                </form>

                            </div>
                        </template>

                        <!--
                        <div class="flex justify-center" v-if="step !== formDefinition.sections.length - 1">
                            <button class="btn btn-secondary btn-icon mr-2" v-if="step > 0" @click="handleStep('back')">
                                <ArrowSmallLeftIcon class="h-5 w-5 mr-1" />
                                Indietro
                            </button>
                            <button class="btn btn-primary btn-icon mr-2" @click="handleStep('forw', fd)">
                                Avanti
                                <ArrowSmallRightIcon class="h-5 w-5 ml-1" />
                            </button>
                        </div>
                        -->

                    </div>
                </div>

            </div>

        </div>
    </PipelineLayout>
</template>