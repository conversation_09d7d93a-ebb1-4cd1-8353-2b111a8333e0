<script>
import MasterLayout from '../../Layouts/MasterLayout.vue';
import Upload from '@/Services/upload';

export default {
    props: {
        pipeline: Object,
        task: Object,
        data: Object,
    },

    components: {
        MasterLayout,
    },

    methods: {
        onFileChange(event) {
            const file = event.target.files[0];
            if (!file) return;

            Upload.single(
                file,
                `/tasks/aidocs/${this.pipeline.id}/${this.task.id}/`,
                'post',
                {
                    onSuccess: () => {
                        this.$toast?.add?.({
                            severity: 'success',
                            summary: 'Upload completato',
                            detail: 'Il file è stato caricato con successo.',
                            life: 4000
                        });
                    },
                    onError: () => {
                        this.$toast?.add?.({
                            severity: 'error',
                            summary: 'Errore',
                            detail: 'Errore durante il caricamento del file.',
                            life: 4000
                        });
                    },
                    onFinish: () => {
                        event.target.value = '';
                    }
                }
            );
        }
    }
}
</script>

<template>
    <MasterLayout title="foo">
        <pre>{{ data }}</pre>

        <div class="mt-6">
            <label class="block mb-2 font-semibold">Carica documento AI</label>
            <input
                type="file"
                @change="onFileChange"
                class="block"
            />
        </div>
        <a :href="`/pipeline/${this.pipeline.id}/next`">next</a>
    </MasterLayout>
</template>