<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import { router } from "@inertiajs/core";
import { ref } from 'vue';
import Button from 'primevue/button';
import Message from 'primevue/message';
import ToggleSwitch from 'primevue/toggleswitch';
import Tooltip from 'primevue/tooltip';
import Tenant from '../../Components/Tenant.vue';
import InputNumber from 'primevue/inputnumber';
import { useForm } from '@inertiajs/vue3';
import FormMessage from '../../Components/Form/FormMessage.vue';
import InputText from 'primevue/inputtext';
import EPMessage from '../../Components/EPMessage.vue';
import Product from '../../Components/Content/Product.vue';
import CompanyName from '../../Components/Content/CompanyName.vue';
import SplitButton from 'primevue/splitbutton';
import Upload from '@/services/upload';

const env = import.meta.env;

export default {
    props: {
        pipeline: Object,
        task: Object,
        issuances: Array,
    },

    components: {
        PipelineLayout,
        Button,
        Message,
        ToggleSwitch,
        Tenant,
        InputNumber,
        FormMessage,
        InputText,
        EPMessage,
        Product,
        CompanyName,
        SplitButton,
    },

    directives: {
        tooltip: Tooltip,
    },

    data() {
        return {
            loading: ref(false),
            selectedProduct: null,
            hoveredProduct: null,
            toggled: {},
            processType: {
                'direct': 'Immediato',
                'deferred': 'Differito',
                'download': 'Download',
            },
            form: useForm({
                fee: this.task.issuanceData?.fee || null,
            }),
        }
    },

    setup() {
        return {
            env,
            Upload,
        }
    },  

    methods: {
        next: function() {
            this.loading = true;
            
            this.form.post(`/tasks/issuance/${this.pipeline.id}/${this.task.id}`, {
                onFinish: () => { this.loading = false }
            });
        },

        toggleProduct(issuanceId) {
            this.toggled[issuanceId] = !this.toggled[issuanceId];
        },

        go: function(issuanceId) {
            router.get(`/issue/${this.pipeline.id}/${this.task.id}/${issuanceId}`, {}, {
                preserveState: true,
                preserveScroll: true,
            });
        },

        getProcessTypeTooltip(type) {
            switch (type) {
                case 'direct':
                    return 'Per polizze ad adesione. Compila il modulo e procedi con la firma al cliente.';
                case 'deferred':
                    return 'Processo differito. Compila il modulo web e attendi il caricamento della proposta assicurativa da parte del backoffice.';
                case 'download':
                    return 'Scarica, compila e ricarica il pdf compilabile, poi attendi il caricamento della proposta assicurativa da parte del backoffice.';
            }
        },

        /*
        askReload(issuanceId) {
            router.post(`/tasks/issuance/${this.pipeline.id}/${this.task.id}/ask/${issuanceId}`, { }, {
                preserveState: true,
                preserveScroll: true,
            });
        },
        */

        getIssuanceActions(issuance) {
            const actions = [];

            if (issuance.product.processType === 'download') {
                issuance.status === 'pending' && actions.push({
                    label: 'Scarica modulo',
                    icon: 'pi pi-download',
                    url: `/tasks/issuance/${this.pipeline.id}/${this.task.id}/form/${issuance.id}`,
                    target: '_blank'
                });

                issuance.status === 'pending' && actions.push({
                    label: 'Carica documento',
                    icon: 'pi pi-upload',
                    command: () => {
                        // Usa il ref dinamico per questa riga
                        this.$refs['fileInput-' + issuance.id][0].click();
                    }
                });

                issuance.status === 'awaiting' && actions.push({
                    label: 'Rivedi file',
                    icon: 'pi pi-download',
                    url: `/documents/${issuance.form.uuid}`,
                    target: '_blank'
                });
            } else if (issuance.product.processType === 'deferred') {
                issuance.status === 'pending' && actions.push({
                    label: 'Compila modulo',
                    icon: 'pi pi-pencil',
                    command: () => {
                        this.go(issuance.id);
                    }
                });

                issuance.status === 'awaiting' && actions.push({
                    label: 'Rivedi file',
                    icon: 'pi pi-download',
                    url: `/documents/${issuance.form?.uuid}`,
                    target: '_blank'
                });
            } else if (issuance.product.processType === 'direct') {
                actions.push({
                    label: 'Compila modulo',
                    icon: 'pi pi-pencil',
                    command: () => {
                        this.go(issuance.id);
                    }
                });

                if (issuance.contract) {
                    actions.push({
                        label: 'Rivedi file',
                        icon: 'pi pi-search',
                        url: `/documents/${issuance.contract.uuid}`,
                        target: '_blank'
                    });
                }
            }

            if (issuance.product.processType != 'direct' && issuance.status === 'completed') {
                actions.push({
                    label: 'Visualizza il file caricato',
                    icon: 'pi pi-search',
                    url: `/documents/${issuance.contract.uuid}`,
                    target: '_blank'
                });
            }

            /*
            if (issuance.product.processType != 'direct' && issuance.status !== 'pending') {
                actions.push({
                    label: 'Compila di nuovo',
                    icon: 'pi pi-pencil',
                    command: () => {
                        this.go(issuance.id);
                    }
                });
            }
            */

            return actions;
        },

        onUploadFile(event, issuanceId) {
            const file = event.target.files[0];

            if (! file) {
                return;
            }

            this.loading = true;

            Upload.single(
                file,
                `/tasks/issuance/${this.pipeline.id}/${this.task.id}/form/${issuanceId}`,
                'post',
                {
                    onFinish: () => {
                        this.loading = false;
                        event.target.value = '';
                    },
                    onSuccess: () => {
                        this.$toast?.add?.({
                            severity: 'success',
                            summary: 'Upload completato',
                            detail: 'Il file è stato caricato con successo.',
                            life: 4000
                        });
                    }
                }
            );
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <form @submit.prevent="next()">
        <div class="flex flex-col gap-5">

            <div class="card w-full">
                <div class="card-body !p-6">
                    <h2 class="mb-2">Prodotti selezionati</h2>
                    <EPMessage v-if="task.data?.taskCompleted" severity="success">
                        Processo completato. Puoi procedere al prossimo step.
                    </EPMessage>

                    <EPMessage v-else severity="info">
                        Completa il processo per ognuno dei prodotti elencati prima di passare allo step di firma elettronica.
                    </EPMessage>
                    <table class="min-w-full text-sm">
                        <thead>
                            <tr class="bg-gray-100">
                                <th width="20%" class="text-left px-3 py-2">Compagnia</th>
                                <th width="20%" class="text-left px-3 py-2">Nome prodotto</th>
                                <th class="text-left px-3 py-2 text-center">Tipo di processo</th>
                                <th class="text-left px-3 py-2 text-center">Stato</th>
                                <th class="text-left px-3 py-2 text-center">N. Proposta</th>
                                <th class="text-left px-3 py-2 text-center">N. Polizza</th>
                                <th class="text-left px-3 py-2"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="issuance in issuances" :key="issuance.id">
                                <td class="px-3 py-2 align-middle">
                                    <CompanyName :company="issuance.product.company" />

                                    

                                </td>
                                <td class="px-3 py-2 align-middle">
                                    <a class="m-0" :href="`/tasks/issuance/${pipeline.id}/${task.id}/product/${issuance.product.id}`">
                                        {{ issuance.product.name }}
                                    </a>
                                </td>
                                <td class="px-3 py-2 align-middle text-center">
                                    <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="getProcessTypeTooltip(issuance.product.processType)">
                                        {{ processType[issuance.product.processType] }}
                                    </span>
                                </td>
                                <td class="px-3 py-2 align-middle text-center">
                                    <span v-if="issuance.status === 'completed'" class="text-green-600">
                                        <span class="mr-2" v-tooltip.top="'Il backoffice ha caricato il documento richiesto.'">
                                            <i class="pi pi-check"></i>
                                        </span>
                                    </span>
                                    <span v-if="issuance.status === 'awaiting'" class="text-yellow-600">
                                        <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="'Attendi che il backoffice carichi la proposta assicurativa'">
                                            <span>In attesa di caricamento</span>
                                        </span>
                                    </span>
                                    <span v-if="issuance.status === 'pending'" class="text-red-600">
                                        <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="'Completa il processo per questo prodotto'">
                                            <span>Non completato</span>
                                        </span>
                                    </span>
                                </td>
                                <td></td>
                                <td></td>
                                <td class="px-3 py-2 align-middle text-right">
                                    <SplitButton
                                        label="Azioni"
                                        class="p-button-sm"
                                        severity="contrast"
                                        size="small"
                                        :model="getIssuanceActions(issuance)"
                                        @click="    0 && go(issuance.id)"
                                    />
                                    <input
                                        :ref="'fileInput-' + issuance.id"
                                        type="file"
                                        class="hidden"
                                        @change="onUploadFile($event, issuance.id)"
                                    />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <Tenant name="simply">
                <div class="card w-full">
                    <div class="card-body !p-6">
                        <h2 class="mb-2">Importo di brokeraggio</h2>
                        <EPMessage severity="info">
                            Imposta la fee da richiedere al cliente per lo svolgimento delle attività di brokeraggio.
                        </EPMessage>

                        <div>
                            <InputText
                                v-model="form.fee"
                                placeholder="Inserisci l'importo"
                                class="w-1/4 h-12"
                                required
                            />
                                
                            <FormMessage :errors="errors" field="fee">Inserisci l'importo. Può essere pari a zero.</FormMessage>
                        </div>
                        
                    </div>
                </div>

                <div class="flex justify-end">
                    <Button v-if="task.data['taskCompleted']" type="submit" label="Avanti" :loading="loading"></Button>
                    <Button v-else type="button" label="Avanti" :loading="loading" disabled v-tooltip.top="'Completa il processo per ognuno dei prodotti elencati prima di passare allo step successivo.'"></Button>
                </div>
            </Tenant>
            
        </div>
        </form>
    </PipelineLayout>
</template>
