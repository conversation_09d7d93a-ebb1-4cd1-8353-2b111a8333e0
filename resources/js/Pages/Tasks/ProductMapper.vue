<script setup>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import Badge from "@/Components/Badge.vue";
import {CheckIcon, ArrowRightIcon, ArrowUturnLeftIcon} from "@heroicons/vue/24/outline";
import {CheckBadgeIcon, XMarkIcon} from "@heroicons/vue/24/solid";
import PipelineModel from "@/Models/pipeline";

let props = defineProps({
    pipeline: Object,
    task: Object,
    products: Object,
    profile: Object,
    log: Object,
})

let currentIndex = props.pipeline.tasks.findIndex(task => task.id === props.task.id);
let previousTask = props.pipeline.tasks[currentIndex - 1];
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-row" v-if="!products.length">

            <div class="card w-full">
                <div class="card-body ">
                    <HeroEmpty
                        title="Nessun prodotto trovato"
                        message="Non sono stati trovati prodotti compatibili con il profilo del cliente."
                        icon="pi pi-exclamation-triangle">

                        <Button
                            icon="pi pi-arrow-left"
                            label="Torna indietro"
                            class="mt-10"
                            severity="primary"
                            @click="PipelineModel.previous(pipeline)"
                        />

                    </HeroEmpty>
                </div>
            </div>

        </div>

        <div class="flex flex-row space-x-4" ref="scrollTarget" v-if="products.length">

            <div class="basis-8/12">
                <div class="card">
                    <div class="card-body !p-8">
                        <div class="text-xl font-bold">Prodotti coerenti con il profilo del cliente</div>
                        <div class="text-neutral-500 mb-4">Seleziona almeno un prodotto.</div>

                        <pre>
                            <Debug :show="0" v-if="log">{{ log }}</Debug>
                        </pre>

                        <div class="flex flex-row space-x-4 mb-4">
                            <div class="basis-6/12">
                                <div class="flex w-full mb-4" v-for="(p, index) in products">
                                    <div class="rounded-lg bg-white shadow p-6 hover:shadow-xl transition-all w-full relative" @click="toggleProduct(index, p)"
                                         :class="{'shadow-xl border-2 border-indigo-500 cursor-pointer' : p.selected, 'border-2 border-transparent cursor-pointer' : !p.selected && !p.confirmed, 'shadow-xl border-2 border-green-600 cursor-not-allowed' : p.confirmed}">
                                        <div>
                                            <div class="font-bold">{{ p.product.name }}</div>
                                            <p class="text-neutral-500 mb-3">{{p.product.company.name}}</p>
                                            <div class="flex gap-1">
                                                <Badge
                                                    :color="setBadgeColor(p.recommendations.invalidCoverages, coverage)"
                                                    :class="{'line-through' : p.recommendations.invalidCoverages.find((item) => item === coverage.label)}"
                                                    :text="coverage.shortname"
                                                    v-tippy="coverage.name"
                                                    v-for="coverage in filterComplementary(p.product.coverages, profile.coverages)" />
                                            </div>
                                        </div>
                                        <img :src="'/assets/companies/' + p.product.company.logo" alt="" class="absolute top-6 right-6 h-14 w-14 rounded-full">
                                        <CheckBadgeIcon v-if="p.confirmed" class="absolute top-0.5 right-0.5 h-7 w-7 text-green-600" />
                                    </div>
                                </div>
                            </div>
                            <div class="basis-6/12">
                                <div class="rounded-lg bg-white shadow-xl cursor-pointer p-6 transition-all w-full" v-if="showDetail">
                                    <div class="font-bold text-lg">{{ productDetail.product.name }}</div>
                                    <p class="text-neutral-500 mb-3">{{productDetail.product.company.name}}</p>

                                    <div class="rounded bg-amber-100 p-4" v-if="productDetail.recommendations.warnings.length">
                                        <div class="font-bold">Raccomandazioni:</div>
                                        <ul>
                                            <li v-for="warning in productDetail.recommendations.warnings"> - {{ warning }}</li>
                                        </ul>
                                    </div>

                                    <button type="button" class="btn btn-primary btn-icon mt-6" @click="confirmProduct()">
                                        Conferma
                                        <CheckIcon class="h-5 w-5 ml-1" />
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="basis-4/12">
                <div class="card">
                    <div class="card-body">

                        <div class="text-xl font-bold mb-3">Prodotti selezionati</div>

                        <p class="text-neutral-500" v-if="!confirmedProducts.length">Nessun prodotto selezionato.</p>

                        <div v-if="confirmedProducts.length" v-for="(p, index) in confirmedProducts" class="flex items-center justify-between p-2 mb-2 rounded-lg bg-white shadow border-2 hover:border-red-600 cursor-pointer transition-all" @click="removeConfirmedProduct(index, p.id)">
                            <div>{{ p.product.name }}</div>
                            <div><XMarkIcon class="h-6 w-6 text-red-600" /></div>
                        </div>
                        <div v-if="confirmedProducts.length">
                            <button type="button" class="btn btn-icon btn-icon-center btn-primary w-full mt-6" @click="goToSummary()">
                                Prosegui<ArrowRightIcon class="h-5 w-5 ml-1" />
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </PipelineLayout>
</template>

<script>
import {directive} from "vue-tippy";
import {router} from "@inertiajs/core";
import Debug from '../../Components/Debug.vue';
import HeroEmpty from '../../Components/HeroEmpty.vue';
import Button from 'primevue/button';
import Pipeline from '../../Models/pipeline';

export default {
    directives: {
        tippy: directive,
    },
    data() {
        return {
            ref: null,
            showDetail: false,
            productDetail: null,
            confirmedProducts: []
        }
    },
    props: ['products', 'pipeline', 'task'],
    methods: {
        resetSelections() {
            this.products.forEach(function (product) {
                product.selected = false;
            })
        },
        toggleProduct(selectedIndex, product) {

            this.$refs.scrollTarget.scrollIntoView({ behavior: 'smooth' });
            this.resetSelections();

            if (product.confirmed) {
                this.products[selectedIndex].selected = ! this.products[selectedIndex].selected;
                return;
            }

            this.products[selectedIndex].selected = ! this.products[selectedIndex].selected;
            if (this.products[selectedIndex].selected) {
                this.showProductDetail(product)
            }
            else {
                this.showDetail = false;
            }
        },
        filterComplementary(coverages, requestedCoverages) {
            return coverages.filter(x => x.type == 'main' && requestedCoverages.some(item => item.id === x.id));
        },
        setBadgeColor(invalidCoverages, currentCoverage) {
            if (!invalidCoverages) {
                return 'success';
            }
            let isPresent = invalidCoverages.find((item) => item === currentCoverage.label);
            if ( isPresent ) {
                return 'danger';
            }
            return 'success';
        },
        showProductDetail(product) {
            this.productDetail = product;
            this.showDetail = true;
        },
        confirmProduct() {
            let index = this.products.findIndex((p) => p.id === this.productDetail.id)
            this.products[index].confirmed = true;
            this.products[index].selected = false;
            this.confirmedProducts.push(this.productDetail);
            this.productDetail = null;
            this.showDetail = false;
        },
        goToSummary() {
            router.post(
                `/tasks/mapper/${this.pipeline.id}/${this.task.id}`,
                {products: this.confirmedProducts}
            )
        },
        removeConfirmedProduct(index, id) {
            this.confirmedProducts.splice(index, 1);
            let prodIndex = this.products.findIndex((p) => p.id === id)
            this.products[prodIndex].confirmed = false;
            this.products[prodIndex].selected = false;
        }
    }
}
</script>
