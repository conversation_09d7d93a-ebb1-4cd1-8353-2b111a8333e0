<script>
import PipelineLayout from '@/Layouts/PipelineLayout.vue';
import HeroEmpty from '@/Components/HeroEmpty.vue';
import Button from 'primevue/button';
import { useForm } from '@inertiajs/vue3';

export default {
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },

    components: {
        PipelineLayout,
        HeroEmpty,
        Button,
    },

    data() {
        return {
            form: useForm({}),

            loading: false,
        };
    },

    methods: {
        submit() {
            this.loading = true

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
};
</script>
<template>
    
    <PipelineLayout :current="task">

        <HeroEmpty title="TBD" message="Scheda adesione in corso di realizzazione">
            <form @submit.prevent="submit">
                <Button type="submit" label="Invia scheda" :loading="loading" class="mt-10" />
            </form>
        </HeroEmpty>

    </PipelineLayout>

</template>