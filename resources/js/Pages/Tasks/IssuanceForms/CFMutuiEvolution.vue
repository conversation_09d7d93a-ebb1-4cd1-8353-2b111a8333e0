<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker"
import Address from '../../../Components/Form/Address.vue'
import RealEstateRegistry from '../../../Components/Form/RealEstateRegistry.vue'
import PersonalData from '../../../Components/Form/PersonalData.vue'
import Intermediazione from "@/Components/Form/Brokerage.vue";

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker,
        Address,
        RealEstateRegistry,
        PersonalData,
        Intermediazione,
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        const form = useForm({
            // Base
            combinazione: null,
            intermediazione: null,
            importoIntermediazione: null,
            emissione: null,

            // Mutuo
            importoFinanziato: null,
            banca: '',
            durata: null,
            dataErogazione: null,
            notaio: '',

            // Dati immobile
            tipoAbitazione: null,
            indirizzoImmobile: {
                type: 'residence',
                street: '',
                number: '',
                zip: '',
                city: '',
                province: '',
                region: '',
                country: 'IT',
            },
            piano: null,
            interno: null,
            importoDaAssicurare: null,

            // Dati catastali
            datiCatastaliImmobile: {
                foglio: '',
                part: '',
                sub: '',
                cat: '',
                classe: '',
                consist: '',
                rendita: null
            },

            // Dati catastali
            datiCatastaliPertinenza: {
                foglio: '',
                part: '',
                sub: '',
                cat: '',
                classe: '',
                consist: '',
                rendita: null
            },

            // Dati assicurato
            datiAssicurato: {
                nome: '',
                cognome: '',
                via: '',
                numVia: '',
                cap: '',
                localita: '',
                provincia: '',
                telefono: '',
                email: ''
            },

            dichiarazione1: null,
            dichiarazione2: null,
            dichiarazione3: null
        });

        // DEBUG: valorizza con dati di esempio
        if (import.meta.env.DEV) {
            Object.assign(form, {
                combinazione: '3',
                intermediazione: '1',
                importoIntermediazione: 1200,
                emissione: 150,
                importoFinanziato: 100000,
                banca: 'Banca di Debug',
                durata: 20,
                dataErogazione: '2024-07-01',
                notaio: 'Notaio Rossi',
                tipoAbitazione: 'villa',
                indirizzoImmobile: {
                    type: 'residence',
                    street: 'Via Debug',
                    number: '42',
                    zip: '20100',
                    city: 'Milano',
                    province: 'MI',
                    region: 'Lombardia',
                    country: 'IT',
                },
                piano: 2,
                interno: 'B',
                importoDaAssicurare: 200000,
                datiCatastaliImmobile: {
                    foglio: '12',
                    part: '345',
                    sub: '6',
                    cat: 'A/2',
                    classe: '3',
                    consist: '5',
                    rendita: 800,
                },
                datiCatastaliPertinenza: {
                    foglio: '12',
                    part: '346',
                    sub: '7',
                    cat: 'C/6',
                    classe: '1',
                    consist: '1',
                    rendita: 100,
                },
                datiAssicurato: {
                    nome: 'Mario',
                    cognome: 'Debug',
                    via: 'Via Test',
                    numVia: '1',
                    cap: '20100',
                    localita: 'Milano',
                    provincia: 'MI',
                    telefono: '3331234567',
                    email: '<EMAIL>'
                },
                dichiarazione1: '1',
                dichiarazione2: '1',
                dichiarazione3: '0'
            });
        }

        return {
            form,
            loading: false,
        }
    },
    methods: {
        flattenObject(obj, prefix = '', res = {}) {
            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const value = obj[key];
                    const newKey = prefix ? `${prefix}.${key}` : key;
                    if (value && typeof value === 'object' && !Array.isArray(value)) {
                        this.flattenObject(value, newKey, res);
                    } else {
                        res[newKey] = value;
                    }
                }
            }
            return res;
        },
        submit() {
            this.loading = true;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        }
    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p v-if="issuance.product.processType === 'direct'" class="text-gray-500 text-sm font-normal">Modulo di adesione</p>
                                <p v-else="issuance.product.processType === 'deferred'" class="text-gray-500 text-sm font-normal">Modulo di raccolta dati</p>
                            </span>
                        </h2>

                        <hr class="my-4">

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Seleziona la combinazione</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-1" name="combinazione" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-1">Combinazione 1</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-3" name="combinazione" value="3" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-3">Combinazione 3</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-5" name="combinazione" value="5" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-5">Combinazione 5</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-6" name="combinazione" value="6" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-6">Combinazione 6</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.combinazione" inputId="comb-8" name="combinazione" value="8" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="comb-8">Combinazione 8</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="combinazione">Seleziona una combinazione</FormMessage>
                        </div>

                        <Intermediazione
                            :intermediazioneData="form"
                            :errors="{messages: form.errors, key: ''}"
                        />

                        <h2 class="mb-2 mt-14">Dati mutuo</h2>

                        <div class="grid grid-cols-2 gap-x-5 gap-y-8">
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.importoFinanziato"
                                    class="w-full"
                                    mode="currency"
                                    currency="EUR"
                                    locale="it-IT"
                                    placeholder="Importo finanziato"
                                />
                                <FormMessage :errors="form.errors" field="importoFinanziato">
                                    Inserisci l'importo finanziato
                                </FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.banca"
                                    class="w-full"
                                    placeholder="Banca erogante"
                                />
                                <FormMessage :errors="form.errors" field="banca">
                                    Inserisci la banca erogante
                                </FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.durata"
                                    class="w-full"
                                    placeholder="Durata"
                                />
                                <FormMessage :errors="form.errors" field="durata">
                                    Inserisci la durata
                                </FormMessage>
                            </div>
                            <div class="col-span-1">
                                <input type="date" 
                                       v-model="form.dataErogazione"
                                       class="w-full p-inputtext p-component"
                                       placeholder="Data erogazione"
                                       dateFormat="dd/mm/yy" />
                                <FormMessage :errors="form.errors" field="dataErogazione">
                                    Inserisci la data di erogazione
                                </FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.notaio"
                                    class="w-full"
                                    placeholder="Notaio"
                                />
                                <FormMessage :errors="form.errors" field="notaio">
                                    Inserisci il nome del notaio
                                </FormMessage>
                            </div>
                        </div>

                        <h2 class="mb-2 mt-14">Dati immobile da assicurare</h2>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Tipo abitazione</div>
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.tipoAbitazione" inputId="tipoAbit-1" name="tipoAbitazione" value="appartamento" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="tipoAbit-1">Appartamento in condominio</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.tipoAbitazione" inputId="tipoAbit-2" name="tipoAbitazione" value="villa" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="tipoAbit-2">Villa, villetta anche plurifamiliare con ingresso singolo</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="tipoAbitazione">
                                Seleziona il tipo di abitazione
                            </FormMessage>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900 mb-4">Indirizzo immobile</div>
                            <Address
                                :address="form.indirizzoImmobile"
                                :errors="{messages: form.errors, key: 'indirizzoImmobile'}"
                                type="residence"
                            />
                        </div>

                        <div class="grid grid-cols-2 gap-x-5 mb-8">
                            <div class="col-span-1">
                                <InputNumber
                                    v-model="form.piano"
                                    placeholder="Piano"
                                    class="w-full"
                                />
                                <FormMessage :errors="form.errors" field="piano">
                                    Inserisci il piano
                                </FormMessage>
                            </div>
                            <div class="col-span-1">
                                <InputText
                                    v-model="form.interno"
                                    placeholder="Interno"
                                    class="w-full"
                                />
                                <FormMessage :errors="form.errors" field="interno">
                                    Inserisci l'interno
                                </FormMessage>
                            </div>
                        </div>

                        <div class="mb-8">
                            <InputNumber
                                v-model="form.importoDaAssicurare"
                                class="w-full"
                                mode="currency"
                                currency="EUR"
                                locale="it-IT"
                                placeholder="Importo da assicurare (valore di ricostruzione a nuovo)"
                            />
                            <FormMessage :errors="form.errors" field="importoDaAssicurare">
                                Inserisci l'importo da assicurare
                            </FormMessage>
                        </div>


                        <h2 class="mb-2 mt-14">Dati catastatali</h2>

                        <h3 class="text-xl mb-2">Immobile</h3>
                        <RealEstateRegistry
                            :cadastralData="form.datiCatastaliImmobile"
                            :errors="{messages: form.errors, key: 'datiCatastaliImmobile'}"
                        />
                        <h3 class="text-xl mb-2 mt-5">Pertinenza</h3>
                        <RealEstateRegistry
                            :cadastralData="form.datiCatastaliPertinenza"
                            :errors="{messages: form.errors, key: 'datiCatastaliPertinenza'}"
                        />

                        <h2 class="mb-2 mt-14">Assicurato</h2>

                        <PersonalData
                            :personalData="form.datiAssicurato"
                            :errors="{messages: form.errors, key: 'datiAssicurato'}"
                        />
                        
                        <h2 class="mb-2 mt-14">Dichiara inoltre</h2>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Non sono presenti nel fabbricato contenente anche la porzione di fabbricato assicurato con la presente polizza: industrie, cinema, teatri,
                                discoteche o grandi empori, depositi agricoli?</div>
                            <div class="mt-1 space-x-6">
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione1" inputId="dichiar1-1" name="dichiarazione1" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar1-1">Si</label>
                                </div>
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione1" inputId="dichiar1-0" name="dichiarazione1" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar1-0">No</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="dichiarazione1">Seleziona una opzione</FormMessage>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Le strutture portanti verticali, le pareti esterne e il manto esterno del fabbricato e del tetto sono realizzate in C.A o muratura e materiali
                                incombustibili??</div>
                            <div class="mt-1 space-x-6">
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione2" inputId="dichiar2-1" name="dichiarazione2" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar2-1">Si</label>
                                </div>
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione2" inputId="dichiar2-0" name="dichiarazione2" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar2-0">No</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="dichiarazione2">Seleziona una opzione</FormMessage>
                        </div>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900">Nel triennio precedente la data della presente polizza il fabbricato non ha subito danni della medesima natura di quelli indennizzabili in base alla
                                presente assicurazione e non furono annullate al Contraente precedenti polizze incendio sul fabbricato a seguito di sinistro?</div>
                            <div class="mt-1 space-x-6">
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione3" inputId="dichiar3-1" name="dichiarazione3" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar3-1">Si</label>
                                </div>
                                <div class="inline-flex items-center gap-2">
                                    <RadioButton v-model="form.dichiarazione3" inputId="dichiar3-0" name="dichiarazione3" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="dichiar3-0">No</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="dichiarazione3">Seleziona una opzione</FormMessage>
                        </div>

                        <div class="mt-6 flex justify-center">
                            <Button type="submit" icon="pi pi-save" label="Salva" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
