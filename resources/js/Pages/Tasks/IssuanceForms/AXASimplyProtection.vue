<script>
import PipelineLayout from '../../../Layouts/PipelineLayout.vue'
import Button from 'primevue/button'
import Message from 'primevue/message'
import InputText from 'primevue/inputtext'
import { useForm } from '@inertiajs/vue3'
import FormMessage from '../../../Components/Form/FormMessage.vue'
import InputNumber from 'primevue/inputnumber'
import RadioButton from "primevue/radiobutton"
import DatePicker from "primevue/datepicker"
import Address from '../../../Components/Form/Address.vue'
import RealEstateRegistry from '../../../Components/Form/RealEstateRegistry.vue'
import PersonalData from '../../../Components/Form/PersonalData.vue'
import Intermediazione from "@/Components/Form/Brokerage.vue";

export default {
    components: {
        PipelineLayout,
        Button,
        Message,
        InputText,
        FormMessage,
        InputNumber,
        RadioButton,
        DatePicker,
        Address,
        RealEstateRegistry,
        PersonalData,
        Intermediazione,
    },
    props: {
        pipeline: Object,
        task: Object,
        issuance: Object,
    },
    data() {
        // Recupera i dati di default dal task se presenti
        const initial = this.task?.data?.issuance?.[this.issuance.product.id] || {}

        const form = useForm({

            // Pacchetto selezionato
            pacchetto: null,

            // Dati assicurato
            /*datiAssicurato: {
                nome: '',
                cognome: '',
                via: '',
                numVia: '',
                cap: '',
                localita: '',
                provincia: '',
                telefono: '',
                email: ''
            },*/

            durata: null,

            premioUnico: null,

            modalitaPagamento: null

        });

        // DEBUG: valorizza con dati di esempio
        if (import.meta.env.DEV) {
            Object.assign(form, {
                pacchetto: 1,
                /*datiAssicurato: {
                    nome: 'Mario',
                    cognome: 'Debug',
                    via: 'Via Test',
                    numVia: '1',
                    cap: '20100',
                    localita: 'Milano',
                    provincia: 'MI',
                    telefono: '3331234567',
                    email: '<EMAIL>'
                },*/
                durata: 5,
                premioUnico: 1000,
                modalitaPagamento: 1
            });
        }

        return {
            form,
            loading: false,
        }
    },
    methods: {
        submit() {
            this.loading = true;

            this.form.post(`/issue/${this.pipeline.id}/${this.task.id}/${this.issuance.id}`, {
                onFinish: () => { this.loading = false }
            })
        },


    }
}
</script>

<template>
    <PipelineLayout :current="task">
        <div class="flex flex-col gap-5">
            <div class="card w-full">
                <div class="card-body !p-8">
                    <form @submit.prevent="submit">
                        <h2 class="flex items-center gap-4">
                            <img
                                :src="'/assets/companies/' + issuance.product.company.logo"
                                alt="Logo"
                                class="h-10 w-10 rounded-full"
                            >
                            <span>
                                {{ issuance.product.name }}
                                <p v-if="issuance.product.processType === 'direct'" class="text-gray-500 text-sm font-normal">Modulo di adesione</p>
                                <p v-else class="text-gray-500 text-sm font-normal">Modulo di raccolta dati</p>
                            </span>
                        </h2>

                        <hr class="my-4">

<!--                        <h2 class="mb-2 mt-14">Assicurato</h2>

                        <PersonalData
                            :personalData="form.datiAssicurato"
                            :errors="{messages: form.errors, key: 'datiAssicurato'}"
                        />-->

                        <h2 class="mb-4 mt-14">Coperture assicurative ed indennizzi</h2>

                        <!-- Tabella dei pacchetti assicurativi -->
                        <div class="border rounded-lg overflow-hidden">
                            <!-- Header della tabella -->
                            <div class="bg-blue-100 grid grid-cols-3 border-b">
                                <div class="p-3 font-semibold text-center border-r">Pacchetti Assicurativi</div>
                                <div class="p-3 font-semibold text-center border-r">Garanzie prestate</div>
                                <div class="p-3 font-semibold text-center">Prestazione</div>
                            </div>

                            <!-- Pacchetto 1 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-1"
                                        :value="1"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-1">
                                        <strong>Pacchetto 1</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    Decesso
                                </div>
                                <div class="p-3">
                                    Capitale Assicurato
                                </div>
                            </div>

                            <!-- Pacchetto 2 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-2"
                                        :value="2"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-2">
                                        <strong>Pacchetto 2</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Tutti gli Aderenti/Assicurati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div>Invalidità Totale Permanente<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div>Capitale Assicurato</div>
                                </div>
                            </div>

                            <!-- Pacchetto 3 -->
                            <div class="grid grid-cols-3 border-b hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-3"
                                        :value="3"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-3">
                                        <strong>Pacchetto 3</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Lavoratori Dipendenti Privati</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div>Perdita d'Impiego Involontaria</div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div>Indennità mensile assicurata</div>
                                </div>
                            </div>

                            <!-- Pacchetto 4 -->
                            <div class="grid grid-cols-3 hover:bg-gray-50">
                                <div class="p-3 border-r flex items-start gap-2">
                                    <input
                                        type="radio"
                                        v-model="form.pacchetto"
                                        id="pacchetto-4"
                                        :value="4"
                                        name="pacchetto"
                                        class="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                                    />
                                    <label for="pacchetto-4">
                                        <strong>Pacchetto 4</strong><br>
                                        <span class="text-sm italic text-gray-600">Attivabile solo da:<br>Lavoratori Autonomi<br>Lavoratori Dipendenti Pubblici<br>Non Lavoratori</span>
                                    </label>
                                </div>
                                <div class="p-3 border-r">
                                    <div class="mb-2">Decesso</div>
                                    <div class="mb-2">Inabilità Totale Temporanea<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                    <div>Ricovero Ospedaliero<br>
                                        <span class="text-xs text-gray-500">(da Infortunio o Malattia)</span></div>
                                </div>
                                <div class="p-3">
                                    <div class="mb-2">Capitale Assicurato</div>
                                    <div class="mb-2">Indennità mensile assicurata</div>
                                    <div>Indennità mensile assicurata</div>
                                </div>
                            </div>
                        </div>

                        <h2 class="mb-4 mt-14">Durata del contratto</h2>

                        <div v-if="form.pacchetto">
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.durata" inputId="durata-5" name="durata" value="5" :disabled="!form.pacchetto" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="durata-5">5 anni</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.durata" inputId="durata-10" name="durata" value="10" :disabled="!form.pacchetto || (form.pacchetto === 3 || form.pacchetto === 4)" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="durata-10" :class="{'cursor-not-allowed opacity-50': (form.pacchetto === 3 || form.pacchetto === 4)}">10 anni (solo in caso di scelta del Pacchetto 1 o del Pacchetto 2)</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="durata">Seleziona la durata</FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Premio unico anticipato per tutte le garanzie del Pacchetto e per tutta la durata prescelta</h2>
                        <div>
                            <InputNumber
                                v-model="form.premioUnico"
                                placeholder="Premio unico"
                                mode="currency"
                                currency="EUR"
                                locale="it-IT"
                                class="w-3/6"
                            />
                            <FormMessage :errors="form.errors" field="premioUnico">
                                Importo di premio unico anticipato
                                (Incluse imposte pari al 2.5% - Garanzia Decesso
                                esente
                            </FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Mezzi di pagamento del premio</h2>

                        <div class="mb-8">
                            <div class="mt-1 space-y-3">
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-1" name="modalitaPagamento" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for=modalita-1>Assegni bancari, postali o circolari, muniti della clausola di non trasferibilità, intestati a SIMPLY BROKER S.R.L.</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-2" name="modalitaPagamento" value="2" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for=modalita-2>Ordini di bonifico che abbiano quale beneficiario SIMPLY BROKER S.R.L.</label>
                                </div>
                                <div class="flex items-center gap-2">
                                    <RadioButton v-model="form.modalitaPagamento" inputId="modalita-3" name="modalitaPagamento" value="3" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for=modalita-3>POS (Pagobancomat) messo a disposizione da SIMPLY BROKER S.R.L</label>
                                </div>
                            </div>
                            <FormMessage :errors="form.errors" field="modalitaPagamento">Seleziona una modalità</FormMessage>
                        </div>

                        <h2 class="mb-4 mt-14">Informazioni sullo stato di salute</h2>

                        <div class="mb-8">
                            <div class="font-semibold leading-6 text-gray-900 mb-3">A. Ha ricevuto, negli ultimi 5 anni, una diagnosi o prescrizione di esami, cure,
                                trattamenti, subito interventi chirurgici o assunto farmaci in merito alle
                                seguenti patologie?</div>
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">1. Ipertensione arteriosa (pressione superiore a 145/90 o trattamento con 2 o più medicinali)</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute1" inputId="sal1-1" name="salute1" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal1-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute1" inputId="sal1-0" name="salute1" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal1-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute1"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">2. Ictus, attacco ischemico transitorio (mini-ictus), emorragia cerebrale</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute2" inputId="sal2-1" name="salute2" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal2-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute2" inputId="sal2-0" name="salute2" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal2-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute2"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">3. Infarto, cardiopatia ischemica/coronarica, arteriosclerosi</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute3" inputId="sal3-1" name="salute3" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal3-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute3" inputId="sal3-0" name="salute3" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal3-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute3"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">4. Aritmia cardiaca tale da richiedere un trattamento farmacologico e/o controlli periodici</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute4" inputId="sal4-1" name="salute4" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal4-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute4" inputId="sal4-0" name="salute4" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal4-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute4"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">5. Qualsiasi forma di neoplasia maligna, inclusi cancro alla pelle (melanoma), leucemie, linfomi, mieloma, tumore del midollo osseo ed inoltre meningioma, nonché cisti o crescita benigna delle meningi all’interno del cervello o della spina dorsale</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute5" inputId="sal5-1" name="salute5" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal5-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute5" inputId="sal5-0" name="salute5" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal5-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute5"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">6. Immunodeficienza acquisita (AIDS) e qualsiasi altra immunopatologia che comporti deficit del sistema immunitario</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute6" inputId="sal6-1" name="salute6" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal6-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute6" inputId="sal6-0" name="salute6" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal6-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute6"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">7. Sclerosi laterale amiotrofica, sclerosi multipla, morbo di Alzheimer, morbo di Parkinson e tutte le malattie neurogenerative</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute7" inputId="sal7-1" name="salute7" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal7-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute7" inputId="sal7-0" name="salute7" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal7-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute7"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">8. Discopatia, osteoartrite o artrite infiammatoria estesa ad una o più articolazioni</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute8" inputId="sal8-1" name="salute8" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal8-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute8" inputId="sal8-0" name="salute8" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal8-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute8"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">9. Broncopneumopatia cronica ostruttiva, l’asma moderato/severo, la sarcoidosi e l’enfisema</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute9" inputId="sal9-1" name="salute9" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal9-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute9" inputId="sal9-0" name="salute9" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal9-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute9"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">10. Insufficienza renale acuta o cronica, malattie infiammatorie croniche intestinali, pancreatite cronica</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute10" inputId="sal10-1" name="salute10" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal10-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute10" inputId="sal10-0" name="salute10" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal10-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute10"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">11. Ogni forma di diabete</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute11" inputId="sal11-1" name="salute11" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal11-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute11" inputId="sal11-0" name="salute11" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal11-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute11"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">12. Cirrosi da qualsiasi causa, epatite B e C e steatosi epatica o qualsiasi epatopatia</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute12" inputId="sal12-1" name="salute12" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal12-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute12" inputId="sal12-0" name="salute12" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal12-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute12"></FormMessage>-->
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex-grow">13. Le seguenti Malattie o malformazioni congenite/ereditarie: idrocefalo, pneumopatia fibrocistica, cardiopatie congenite, spina bifida, atresie di organi addominali, trisomie, agenesie renali, malformazioni vascolari endocraniche, malformazioni dell’apparato urinario non corrette chirurgicamente, malformazioni dei grossi vasi</div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute13" inputId="sal13-1" name="salute13" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal13-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute13" inputId="sal13-0" name="salute13" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal13-0">No</label>
                                </div>
                            </div>
<!--                            <FormMessage :errors="form.errors" field="salute13"></FormMessage>-->
                            <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">B. È a conoscenza di essere affetto da una delle malattie di cui al punto A, o di
                                una loro possibile insorgenza, di essere in attesa di risultati di consulti
                                medici, esami medici o ricoveri ospedalieri (o in istituti di cura) sempre in
                                riferimento alle patologie elencate al punto A?
                            </div>
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute14" inputId="sal14-1" name="salute14" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal14-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute14" inputId="sal14-0" name="salute14" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal14-0">No</label>
                                </div>
                            </div>
                            <div class="font-semibold leading-6 text-gray-900 mt-5 mb-3">C. È titolare di una pensione di invalidità o inabilità (parziale o totale) o ha in
                                corso pratiche per il relativo riconoscimento?</div>
                            <div class="flex items-center gap-5 mb-2">
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute15" inputId="sal15-1" name="salute15" value="1" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal15-1">Si</label>
                                </div>
                                <div class="flex items-center gap-1">
                                    <RadioButton v-model="form.salute15" inputId="sal15-0" name="salute15" value="0" />
                                    <label class="block text-sm font-medium leading-6 text-gray-900" for="sal15-0">No</label>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 flex justify-center">
                            <Button type="submit" icon="pi pi-save" label="Salva" :loading="loading" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </PipelineLayout>
</template>
