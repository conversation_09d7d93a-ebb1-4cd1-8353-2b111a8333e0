<script>
import PipelineLayout from '../../Layouts/PipelineLayout.vue';
import InputText from 'primevue/inputtext';
import Message from 'primevue/message';
import {router} from "@inertiajs/core";
import ToggleSwitch from 'primevue/toggleswitch';
import TaxCode from '../../cf';
import InputMask from 'primevue/inputmask';
import AutoComplete from 'primevue/autocomplete';
import axios from 'axios';
import { ref } from 'vue';
import Select from 'primevue/select';
import Stepper from 'primevue/stepper';
import StepItem from 'primevue/stepitem';
import StepPanels from 'primevue/steppanels';
import StepPanel from 'primevue/steppanel';
import StepList from 'primevue/steplist';
import Step from 'primevue/step';
import Button from 'primevue/button';
import Address from '../../Components/Form/Address.vue';
import { useForm, usePage } from '@inertiajs/vue3';
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
import FormMessage from '../../Components/Form/FormMessage.vue';
import City from '../../Components/Form/City.vue';
import { useToast } from 'primevue/usetoast';
import PersonSearch from '../../Components/PersonSearch.vue';
import Person from '../../Components/Person.vue';
import IdentityDocument from '../../Components/IdentityDocument.vue';
import Debug from '../../Components/Debug.vue';
import PersonModel from '../../Models/person.js';
import EnterpriseModel from '../../Models/enterprise.js';
import FormUtils from '../../form.utils.js';
import { dateFormatter } from '../../dateFormatter.js';
import EPMessage from '../../Components/EPMessage.vue';
import ClientSelection from '../../Components/Content/ClientSelection.vue';

const loading = ref(false);

export default {
    props: {
        pipeline: Object,
        task: Object,
        client: Object,
    },

    components: {
        PipelineLayout,
        InputText,
        Message,
        ToggleSwitch,
        InputMask,
        AutoComplete,
        Select,
        Stepper,
        StepList,
        Step,
        StepPanels,
        StepPanel,
        StepItem,
        Button,
        Address,
        ExclamationTriangleIcon,
        FormMessage,
        City,
        PersonSearch,
        Person,
        IdentityDocument,
        Debug,
        EPMessage,
        ClientSelection,
    },

    data() {
        return {
            mode: ref('search'),

            cf: new TaxCode(),

            // Used for autocomplete
            selectedClient: {},
            
            showModal: false,
        }
    },

    setup() {
        const props = usePage().props;

        const toast = useToast();

        const insertRep = ref(false);

        const form = useForm({
            isCorporate: props.pipeline.type == 'legal' ? true : false,
            person: PersonModel.make(props.client?.person),
            enterprise: EnterpriseModel.make(props.client?.enterprise),
        });

        const submit = (options = {}) => {
            loading.value = true;
            return form.post(`/tasks/client/${props.pipeline.id}/${props.task.id}`, {
                forceFormData: true,
                onFinish: () => {
                    loading.value = false;
                },
                ...options
            });
        };

        return {
            form, submit, loading, insertRep, dateFormatter,
        }
    },  

    methods: {
        debug() {
            this.form.person = PersonModel.factory();

            this.form.enterprise = EnterpriseModel.factory();
        },

        // MOVE TO BACKEND?
        validateCF(form) {
            let valid = true;

            if (! this.cf.checkName(form.person.name, form.person.taxCode)) {
                form.errors['person.name'] = 'Il codice fiscale non corrisponde al nome.';
                valid = false;
            }

            if (! this.cf.checkLastname(form.person.lastname, form.person.taxCode)) {
                form.errors['person.lastname'] = 'Il codice fiscale non corrisponde al cognome.';
                valid = false;
            }

            return valid;
        },

        onSelect(event) {
            this.selectedClient = event.value;

            this.showModal = true;
        },

        onSelectRep(event) {
            this.selectedClient = event.value;
            this.form.person = event.value.model;
        },

        goSearch(confirmAction) {
            if (confirmAction && ! confirm("Sicuro? I dati non salvati saranno persi.")) {
                return;
            }

            this.form.person = PersonModel.make();
            this.form.enterprise = EnterpriseModel.make();
            this.selectedClient = {};
            this.mode = 'search';
        },

        next() {
            this.submit({
                /*onSuccess: () => {
                    router.get(`/pipeline/${this.pipeline.id}/next`);
                }*/
            });
        },

        goInsert() {
            this.mode = 'insert';
        },

        errorCss(form, field, otherClasses = null) {
            return new FormUtils().errorCss(form.errors, field, otherClasses);
        },
    }
}

</script>

<template>
    <PipelineLayout :current="task">
        <form @submit.prevent="submit(form)" enctype="multipart/form-data">
        <div class="flex flex-col gap-5">
            <div class="card w-full" v-if="form.person.id == null && form.enterprise.id == null && mode == 'search'">
                <div class="card-body !p-8">
                    <h1>Ricerca cliente</h1>

                    <div class="flex flex-col gap-5 mb-8">
                        <div>
                            <PersonSearch @person-search:not-found="goInsert" @person-search:selected="onSelect"></PersonSearch> 
                        </div>
                    </div>

                    <h1>Nuovo cliente</h1>

                    <div class="">
                        <Button type="button" label="Crea un nuovo cliente" @click="goInsert()"></Button>
                    </div>
                </div>

                <ClientSelection :showModal="showModal" :selectedClient="selectedClient" :pipeline="pipeline" :task="task"></ClientSelection>
            </div>

            <div class="card w-full" v-if="form.person.id != null || form.enterprise.id != null || mode == 'insert'">
                <div class="card-body !p-8">
                    <template v-if="! client?.id">
                        <Button icon="pi pi-sync" iconPos="left" type="button" label="Cambia cliente" severity="info" @click="goSearch(true)"></Button>
                        <hr class="my-5">
                    </template>
 
                    <h1>
                        Anagrafica cliente
                    </h1>
                    <Debug :float="true">
                        <a class="p0 cursor-pointer" @click=debug()><small class="text-xs">Compila scheda</small></a>
                    </Debug>

                    <div v-if="Object.keys(form.errors).length" class="border-solid border-2 rounded-md border-red-300 p-3 bg-red-100">
                        <ExclamationTriangleIcon class="h-5 w-5 inline-block"></ExclamationTriangleIcon>
                        Correggere gli errori segnalati prima di proseguire.
                    </div>
                    <hr v-if="Object.keys(form.errors).length" class="my-3">

                    

                    <div class="flex flex-col gap-5">
                        <div v-if="! client?.id" class="flex gap-5">
                            <span :class="{'font-bold': ! form.isCorporate}">Persona fisica</span>
                            <ToggleSwitch v-model="form.isCorporate"></ToggleSwitch> 
                            <span :class="{'font-bold': form.isCorporate}">Persona giuridica</span>
                        </div>

                        <hr>

                        <template v-if="form.isCorporate">
                            <h2>Dati Contraente Giuridico</h2>

                            <div>
                                <InputText required :class="errorCss(form, 'enterprise.name')" class="md:w-1/2 capitalize" v-model="form.enterprise.name" placeholder="Ragione sociale" />
                                <FormMessage :errors="form.errors" field="enterprise.name">Inserisci la ragione sociale o denominazione.</FormMessage>
                            </div>

                            <div>
                                <InputText required :class="errorCss(form, 'enterprise.vat')" class="md:w-1/2 capitalize" v-model="form.enterprise.vat" placeholder="Partita IVA" />
                                <FormMessage :errors="form.errors" field="enterprise.vat">Inserisci la partita IVA</FormMessage>
                            </div>

                            <h3>Sede Legale</h3>

                            <Address :errors="{messages: form.errors, key: 'enterprise.addresses.headquarters'}" :address="form.enterprise.addresses.headquarters" type="headquarters" v-if="form.isCorporate"></Address>

                            <template v-if="form.enterprise.rep.id">
                                <!-- This is the rule for now: cannot change the REP for an existing enterprise -->
                                <h2>Legale rappresentante</h2>
                                <Person :person="form.enterprise.rep" :disabled="true"></Person>
                            </template>
                                
                            <template v-else>
                                <div :class="errorCss(form, 'person.name', ['rounded-lg', 'p-5'])">
                                    <h2>Legale rappresentante</h2>

                                    <template v-if="! insertRep">
                                        <div>
                                            <PersonSearch @person-search:not-found="insertRep = true" @person-search:selected="onSelectRep" placeholder="Cerca legale rappresentante"></PersonSearch> 
                                        </div>
                                        <Person v-if="selectedClient.id" :person="selectedClient.model" :disabled="true"></Person>
                                    </template>

                                    <template v-if="insertRep">
                                        <div class="flex items-center gap-2">
                                            <Button @click="insertRep = false" icon="pi pi-search" class="my-3" size="small" label="Cerca nel database" />
                                        </div>
                                    
                                    
                                        <Person :person="form.person" :errors="form.errors"></Person>
                                    </template>
                                </div>
                            </template>
                            

                        
                        </template>

                        <template v-if="! form.isCorporate">
                            <Person :person="form.person" :errors="form.errors"></Person>
                        </template>

                        

                    </div>
                    

                    

                </div>
                
            </div>

            <div class="flex justify-between">
                <Button type="button" label="Indietro" @click="goSearch(true)"></Button>
                <div class="flex gap-2 ml-auto">
                    <!-- <Button type="submit" label="Salva" :loading="loading"></Button> -->
                    <Button type="submit" label="Salva" :loading="loading"></Button>
                </div>
            </div>
        </div>

        
    </form>
    </PipelineLayout>
</template>
