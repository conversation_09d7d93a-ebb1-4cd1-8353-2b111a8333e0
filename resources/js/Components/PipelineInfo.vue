<script setup>
defineProps({
    creationDate: Date
})
</script>

<template>
    <div class="font-light text-2xl">Cliente <span class="font-bold">#12</span></div>
    <div class="font-light text-2xl mx-3">/</div>
    <div class="font-light text-2xl">Data apertura: <span class="font-bold">{{formatDateTime(creationDate)}}</span></div>
</template>

<script>
export default {
    methods: {
        formatDateTime: function (date) {
            return new Date(date).toLocaleDateString('it-IT');
        }
    }
}
</script>
