<script>
import { MenuButton} from "@headlessui/vue";

export default {
    components: {MenuButton},
    props: {
        user: Object
    },
    methods: {
        abbreviate(name, lastname) {
            // Suddivide le "parole" nel nome
            let wordsInName = name.split(" ");
            let wordsInLastname = lastname.split(" ");

            // Individua la prima lettera nella prima e ultima "parola"
            let nameFirstLetter = wordsInName[0].substring(0, 1).toUpperCase();
            let lastnameFirstLetter = wordsInLastname[0].substring(0, 1).toUpperCase();

            return nameFirstLetter + lastnameFirstLetter;
        }
    }
}

</script>
<template>
    <MenuButton class="relative flex rounded-full bg-gray-800 text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800">
        <span class="absolute -inset-1.5" />
        <span class="sr-only">Open user menu</span>
        <div class="h-8 w-8 rounded-full bg-gray-600 flex justify-center items-center">
            <div class="font-bold text-white">{{abbreviate(this.user.name, this.user.lastname)}}</div>
        </div>
    </MenuButton>
</template>