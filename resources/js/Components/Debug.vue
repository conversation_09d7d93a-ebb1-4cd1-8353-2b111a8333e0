<script>
import Tooltip from 'primevue/tooltip';

const env = import.meta.env;

// Mappa livelli per pathname (o hash)
const debugLevels = {};

export default {
    props: {
        show: {
            type: Boolean,
            default: true,
        },
        float: Boolean,
        pre: Boolean,
    },
    directives: {
        tooltip: Tooltip,
    },
    setup() {
        // Usa pathname come chiave (puoi usare anche window.location.href)
        const key = typeof window !== 'undefined' ? window.location.pathname : 'default';
        if (!debugLevels[key]) debugLevels[key] = 0;
        const level = (100 + (debugLevels[key]++ * 60)) + "px";

        return {
            env,
            level,
        }
    }
};
</script>

<template>
    <template v-if="env.DEV && show">
        <div
            v-tooltip.top="'Questo controllo è visualizzato solo in ambiente di sviluppo.'"
            :class="[
                'items-center gap-2 border border-yellow-600 rounded-md p-2 text-gray-800 z-50',
                float ? 'fixed right-9 bg-white shadow-lg' : '',
                pre ? 'w-96 h-64 overflow-y-auto' : ''
            ]"
            :style="float ? `bottom: ${level};` : ''"
        >

            <span class="flex items-center">
                <template v-if="pre">
                    <pre class="text-xs"><slot /></pre>
                </template>
                <template v-else>
                    <slot />
                </template>
            </span>
        </div>
    </template>
</template>
