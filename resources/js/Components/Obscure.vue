<!-- Obscure.vue -->
<template>
    <span>
        <span v-if="!show">
            <span class="cursor-pointer decoration-dotted" v-tooltip.bottom="'Clicca per mostrare il dato'" @click="show = true" >
                ************
            </span>
        </span>
        <span v-else>
            <slot />
        </span>
    </span>
</template>

<script>

export default {
    data() {
        return {
            show: false,
        };
    },
};
</script>