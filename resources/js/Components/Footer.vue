<script>
export default {
     setup() {
        return {
            env: import.meta.env,
        }
    },
}
</script>

<template>
<footer class="bg-gray-200 w-full">
    <div class="container mx-auto py-8">
        <div class="flex justify-center py-8">
            
            <div class="text-gray-500 font-light text-xs text-center">
                EasyProfile v.{{ env.DEV || env.VITE_GIT_COMMIT_TAG == "null" ? env.VITE_GIT_COMMIT_HASH : env.VITE_GIT_COMMIT_TAG }}<br>
                © {{new Date().getFullYear()}} Copyright. Tutti i diritti sono riservati.
            </div>
        </div>
    </div>
</footer>
</template>
