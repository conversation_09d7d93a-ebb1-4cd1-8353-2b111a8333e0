<script setup>
import { ref } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon, CogIcon } from '@heroicons/vue/24/outline'

const open = ref(false)

defineProps({
    data: Object
})

const env = import.meta.env;

</script>

<template>
    <TransitionRoot as="template" :show="open">
        <Dialog as="div" class="relative z-10" @close="open = false">
        <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-hidden">
            <div class="absolute inset-0 overflow-hidden">
            <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                <TransitionChild as="template" enter="transform transition ease-in-out duration-500 sm:duration-700" enter-from="translate-x-full" enter-to="translate-x-0" leave="transform transition ease-in-out duration-500 sm:duration-700" leave-from="translate-x-0" leave-to="translate-x-full">
                <DialogPanel class="pointer-events-auto relative w-screen max-w-md">
                    <TransitionChild as="template" enter="ease-in-out duration-500" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in-out duration-500" leave-from="opacity-100" leave-to="opacity-0">
                    <div class="absolute left-0 top-0 -ml-8 flex pr-2 pt-4 sm:-ml-10 sm:pr-4">
                        <button type="button" class="relative rounded-md text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-white" @click="open = false">
                        <span class="absolute -inset-2.5" />
                        <span class="sr-only">Close panel</span>
                        <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                        </button>
                    </div>
                    </TransitionChild>
                    <div class="flex h-full flex-col overflow-y-scroll bg-white py-6 shadow-xl">
                        <div class="px-4 sm:px-6">
                            <DialogTitle class="text-base font-semibold leading-6 text-gray-900">
                                Link
                            </DialogTitle>
                        </div>
                        <hr class="m-4">
                        <div class="px-4 sm:px-6">
                            
                            <button type="button" @click="newPipeline">Nuova posizione</button>
                            <hr class="my-3"> 
                            
                            <template v-if="data?.tech?.tenant?.name == 'dorotea'">
                                <a :href="`/dorotea/auth?user_id=${data.tech.EGG_TESTING_USERID}&session_token=${data.tech.EGG_TESTING_TOKEN}`">Login Dorotea</a>
                                <hr class="my-3">
                            </template>
                            
                            <div v-if="env.DEV">
                                <a href="/debug">Login utente</a>
                                <hr class="my-3">
                                <a href="/debug/admin">Login Admin</a>
                            </div>

                            <div v-if="env.MODE == 'staging'">
                                <a :href="`/staging/${env.VITE_STAGING_KEY}`">Login utente</a>
                                <hr class="my-3">
                                <a :href="`/staging/admin/${env.VITE_STAGING_KEY}`">Login Admin</a>
                            </div>
                            
                        </div>

                        <div v-if="env.DEV">
                            <hr class="m-4">
                            <div class="relative mt-6 flex-1 px-4 sm:px-6">
                                <span v-for="d in data">
                                    <pre class="text-xs">{{ d }}</pre>
                                    <hr class="my-2">
                                </span>
                            </div>
                        </div>

                        <div v-if="env.DEV" class="relative mt-6 flex-1 px-4 sm:px-6">
                            <pre class="text-xs">{{ env }}</pre>
                        </div>
                    </div>
                </DialogPanel>
                </TransitionChild>
            </div>
            </div>
        </div>
        </Dialog>
    </TransitionRoot>

    <div @click="open = ! open" class="cursor-pointer rounded-full bg-red-700 w-9 p-1 shadow-2xl" style="position: fixed; bottom: 5%; right: 2%;">
        <CogIcon class="text-white" aria-hidden="true"></CogIcon>
    </div>
</template>

<script>
import { router } from '@inertiajs/vue3';
export default {
    methods: {
        newPipeline() {
            if (this.data.tech && this.data.tech.tenant) {
                console.log(this.data.tech.tenant.name);
                switch (this.data.tech.tenant.name) {
                    case 'dorotea':
                        let pipelineId = Math.floor(Math.random() * 1000);

                        this.pipelineLink = `/dorotea/auth?user_id=${this.data.tech.EGG_TESTING_USERID}&session_token=${this.data.tech.EGG_TESTING_TOKEN}&praticaID=${pipelineId}&anagraficaID=1`;
                        
                        console.log(this.pipelineLink);
                        
                        router.get(this.pipelineLink);

                        break;
                    default:
                        router.post('/pipeline');

                        break;
                }
                
            }
        }
    }
}
</script>
