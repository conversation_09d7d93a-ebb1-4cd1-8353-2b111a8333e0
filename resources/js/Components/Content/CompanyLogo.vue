<script>
/**
 * API
 * @var {Object} company - The company object containing logo and name.
 * @var {String|Number} size - The size of the logo, default is 8
 */
export default {
    props: {
        company: Object,
        size: {
            type: [String, Number],
            default: 8,
        },
    },
    computed: {
        sizeClass() {
            return `h-${this.size} w-${this.size}`;
        }
    }
}
</script>

<template>
    <img v-tooltip.top="company.name" :src="'/assets/companies/' + company.logo" alt="" :class="sizeClass + ' rounded-full'">
</template>