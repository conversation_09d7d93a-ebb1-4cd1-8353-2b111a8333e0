<script>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import State from '../State.vue';
import { dateFormatter } from '@/dateFormatter';

export default {
    props: {
        pipelines: Array
    },

    components: {
        DataTable,
        Column,
        State
    },

    setup() {
        return {
            dateFormatter
        }
    },
}

</script>

<template>
    <DataTable :value="pipelines" class="p-datatable-sm text-sm" responsiveLayout="scroll">
        <Column field="state" header="Stato">
            <template #body="slotProps">
                <State v-if="slotProps.data.state == 'closed'" :entity="slotProps.data" />
                <State v-else :text="slotProps.data.currentTask?.displayName" severity="warn" />
            </template>
        </Column>

        <Column field="p" header="Prodotti">
            <template #body="slotProps">
                <template v-if="slotProps.data.issuedProducts?.length">
                    <a v-tooltip.top="slotProps.data.issuedProducts.map(p => p.name).join(', ')" class="cursor-pointer">
                        {{ slotProps.data.issuedProducts?.length }} prodotti
                    </a>
                </template>
                <template v-else>
                    Posizione in corso
                </template>
            </template>
        </Column>

        <Column field="created_at" header="Creato il">
            <template #body="slotProps">
                {{ dateFormatter(slotProps.data.created_at) }}
            </template>
        </Column>

        <Column field="link" header="Link">
            <template #body="slotProps">
                <a :href="`/pipeline/${slotProps.data.id}`" target="_blank">
                    <i class="pi pi-external-link"></i>
                </a>
            </template>
        </Column>
    </DataTable>
</template>