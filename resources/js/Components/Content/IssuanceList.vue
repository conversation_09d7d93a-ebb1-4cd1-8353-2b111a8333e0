<script>
/**
 * API
 * @var {Object} file - The file object containing details like type, document, uuid, etc.
 * It should contain: product, task, task.pipeline
 */
import { dateFormatter } from '../../dateFormatter';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import CompanyName from './CompanyName.vue';
import State from '../State.vue';
import CompanyLogo from './CompanyLogo.vue';

export default {
    props: {
        issuances: Array,
        allowUpload: {
            type: Boolean,
            default: false,
        },
    },

    components: {
        DataTable,
        Column,
        CompanyName,
        State,
        CompanyLogo,
    },

    setup() {
        return {
            dateFormatter,

            processType: {
                direct: 'Diretto',
                deferred: 'Differ<PERSON>',
                download: 'Download',
            },
        };
    },

    methods: {
        getProcessTypeTooltip(type) {
            switch (type) {
                case 'direct':
                    return 'Per polizze ad adesione. Compila il modulo e procedi con la firma al cliente.';
                case 'deferred':
                    return 'Processo differito. Compila il modulo web e attendi il caricamento della proposta assicurativa da parte del backoffice.';
                case 'download':
                    return 'Scarica, compila e ricarica il pdf compilabile, poi attendi il caricamento della proposta assicurativa da parte del backoffice.';
            }
        }
    },
    
}
</script>

<template>
    <DataTable :value="issuances" responsiveLayout="scroll" class="text-sm">
        <Column field="id" header="#" style="width: 5%;" />

        <Column field="state" header="Stato" style="width: 12%;">
            <template #body="slotProps">
                <State :entity="slotProps.data.task?.pipeline" />
            </template>
        </Column>
        
        <Column field="prep" header="Preparazione" style="width: 5%;">
            <template #body="slotProps">
                <State :entity="slotProps.data" />
            </template>
        </Column>

       <Column field="process" header="Processo">
            <template #body="slotProps">
                <span class="mr-2 underline decoration-dashed underline-offset-4" v-tooltip.top="getProcessTypeTooltip(slotProps.data.product?.processType)">
                    {{ processType[slotProps.data.product?.processType] }}
                </span>
            </template>
        </Column>

        <Column field="product" header="Prodotto">
            <template #body="slotProps">
                <div class="flex items-center gap-3">
                    <CompanyLogo :company="slotProps.data.product?.company" :size="size" />
                    <a :href="`/products/${slotProps.data.product?.id}`">
                        {{ slotProps.data.product?.name ?? '-' }}
                    </a>
                </div>
            </template>
        </Column>

        <Column v-if="allowUpload" field="upload" header="Upload">
            <template #body="slotProps">
                upload
            </template>
        </Column>

    </DataTable>
</template>