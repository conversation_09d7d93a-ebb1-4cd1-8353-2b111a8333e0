<script>
/**
 * API
 * @var {Object} company - The company object containing logo and name.
 */
import Chip from 'primevue/chip';
import CompanyLogo from './CompanyLogo.vue';
import CompanyName from './CompanyName.vue';

export default {
    props: {
        product: Object,
        asBadge: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        CompanyName,
        CompanyLogo,
        Chip,
    },
}
</script>

<template>
    <div v-if="asBadge">
       <div class="inline-flex items-center rounded-full bg-gray-100 px-3 py-1 text-xs font-light gap-2">
            <CompanyLogo :company="product.company" size="6" />
            <span>{{ product.name }}</span>
        </div>
    </div>

    <div v-else class="flex items-center gap-3">
        <CompanyLogo :company="product.company" />
        <span>{{ product.name }}</span>
    </div>
</template>