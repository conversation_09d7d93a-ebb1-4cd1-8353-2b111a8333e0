<script>
import Obscure from '../Obscure.vue';

export default {
    props: {
        salesman: Object
    },

    components: {
        Obscure
    },
}

</script>

<template>
    <h4><PERSON><PERSON> consulente</h4>
    <a :href="`/salesmen/${salesman.id}`">{{ salesman.name }} {{ salesman.lastname }}</a><br>
    {{ salesman.node?.name }}<br>
    RUI {{ salesman.rui?.section }} {{ salesman.rui?.code }}<br>

    <h4><PERSON><PERSON><PERSON></h4>
    <Obscure>
        {{ salesman.email }}<br>
        {{ salesman.phone }}
    </Obscure>
</template>