<script>
/**
 * API
 * @var {Object} pipeline - Should contain: currentTask
 */
import { dateFormatter } from '../../dateFormatter';
import User from '../../user';

export default {
    props: {
        pipeline: Object,
        user: Object,
        showLinks: {
            type: Boolean,
            default: true,
        },
    },
    setup() {
        return {
            dateFormatter,
            User,
        };
    },
}
</script>

<template>
    <div v-if="pipeline.state != 'closed'">
        <h5>ID</h5>
        <p># {{ pipeline.id }} del {{ dateFormatter(pipeline.created_at) }}</p>

        <h5 class="mt-2">Fase</h5>
        <p>{{ pipeline.currentTask?.displayName ?? '-' }}</p>

        <h5 class="mt-2">Dettagli</h5>

        <template v-if="pipeline.currentTask?.type == 'client'">
            Raccolta dati in corso.
        </template>

        <template v-else-if="pipeline.currentTask?.type == 'survey'">
            Compilazione questionario in corso.
        </template>

        <template v-else-if="pipeline.currentTask?.type == 'signature'">
            <span v-if="pipeline.currentTask?.data?.signatureStatus == 'pending'">
                <i class="pi pi-hourglass"></i> In attesa della firma cliente <br>
                <i class="pi pi-clock"></i> Scadenza fascicolo: {{ dateFormatter(pipeline.currentTask.data?.folder?.expires_at) }}
            </span>
            <span v-else-if="pipeline.currentTask?.data?.signatureStatus == 'done'">
                Firma completata
            </span>
            <span v-else>
                In attesa che la documentazione sia inviata al cliente
            </span>
        </template>

        <template v-else-if="pipeline.currentTask?.type == 'issuance'">
            <span v-if="pipeline.currentTask?.data?.issuanceStatus == 'pending' || ! pipeline.currentTask?.data?.issuanceStatus">
                Il consulente sta elaborando le schede prodotto.
            </span>
            <span v-else>
                <i class="pi pi-check"></i>
                Il consulente ha completato la compilazione delle schede prodotto.
            </span>
        </template>

        <template v-else>
            -
        </template>

        <template v-if="showLinks">
            <h5 class="mt-2">Collegamenti</h5>
            <a v-if="User.is(user, 'manager')" :href="`/pipeline/${pipeline.id}`">Accedi alla scheda posizione</a>
            <a v-if="User.is(user, 'salesman')" :href="`/pipeline/${pipeline.id}/resume`">Riprendi posizione</a>
        </template>
    </div>

    <div v-else>
        <h5>ID</h5>
        <p># {{ pipeline.id }} del {{ dateFormatter(pipeline.created_at) }}</p>

        <h5 class="mt-2">Fase</h5>
        <p>
            <i class="text-green-600 pi pi-check"></i>
            Posizione conclusa il {{ dateFormatter(pipeline.closed_at) }}
        </p>    

        <template v-if="showLinks">
            <h5 class="mt-2">Collegamenti</h5>
            <a v-if="User.is(user, 'manager')" :href="`/pipeline/${pipeline.id}`">Accedi alla scheda posizione</a>
            <a v-if="User.is(user, 'salesman')" :href="`/pipeline/${pipeline.id}/resume`">Riprendi posizione</a>
        </template>
    </div>
</template>