<script>
/**
 * API
 * @var {Object} file - The file object containing details like type, document, uuid, etc.
 * It should contain: product, task, task.pipeline
 */
import { dateFormatter } from '../../dateFormatter';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import CompanyName from './CompanyName.vue';
import State from '../State.vue';
import CompanyLogo from './CompanyLogo.vue';
import FileUpload from 'primevue/fileupload';
import { useForm } from '@inertiajs/vue3';
import { useToast } from 'primevue/usetoast';
import Upload from '@/Services/upload';

const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');

export default {
    props: {
        pipeline: Object,
        issuances: Array,
    },

    components: {
        DataTable,
        Column,
        CompanyName,
        State,
        CompanyLogo,
        FileUpload,
    },

    setup() {
        return {
            dateFormatter,

            form: useForm({
                file: null,
                issuance_id: null,
            }),
        };
    },

    methods: {
        // @TODO refactor to utils/upload.js
        
        onUpload(event, issuanceId) {
            const file = event.files[0];

            if (! file) {
                return;
            }

            // Controllo che sia PDF
            if (file.type !== 'application/pdf' && !file.name.toLowerCase().endsWith('.pdf')) {
                this.$toast.add({
                    severity: 'error',
                    summary: 'Formato non valido',
                    detail: 'Puoi caricare solo file PDF.',
                    life: 5000
                });
                return;
            }

            Upload.single(
                file,
                `/tasks/issuance/${this.pipeline.id}/${this.pipeline.currentTask.id}/upload`,
                'post',
                {
                    data: { issuance_id: issuanceId },
                    onSuccess: () => {
                        this.$toast.add({ severity: 'success', summary: 'Caricato', detail: 'Upload completato', life: 10000 });
                        this.$inertia.reload({ preserveState: true, preserveScroll: true });
                    },
                    onError: () => {
                        this.$toast.add({ severity: 'error', summary: 'Errore', detail: 'Si è verificato un errore durante il caricamento.', life: 0 });
                    },
                    onFinish: () => {
                        // eventuale cleanup
                    }
                }
            );
        },
    },
    
}
</script>

<template>
    <DataTable :value="issuances" responsiveLayout="scroll" class="text-sm">
        <Column field="id" header="#" style="width: 5%;" />

        <Column field="product" header="Prodotto">
            <template #body="slotProps">
                <div class="flex items-center gap-3">
                    <CompanyLogo :company="slotProps.data.product?.company" :size="size" />
                    <a :href="`/products/${slotProps.data.product?.id}`">
                        {{ slotProps.data.product?.name ?? '-' }}
                    </a>
                </div>
            </template>
        </Column>

        <Column field="upload" header="" style="text-align: center;">
            <template #header>
                <div class="text-center w-full">Azioni</div>
            </template>
            <template #body="slotProps">

                <!-- Direct process -->
                <template v-if="slotProps.data.product?.processType == 'direct'">
                    <template v-if="slotProps.data.status == 'completed'">
                        <i class="text-green-600 pi pi-check" v-tooltip.top="'Emissione completata.'"></i>
                    </template>
                    <template v-else>
                        <i class="text-yellow-600 pi pi-hourglass" v-tooltip.top="'Il consulente sta elaborando la sosttoscrizione. Questo prodotto non richiede nessun intervento da parte tua.'"></i>
                    </template>
                </template>

                <!-- Deferred, Download process -->
                <template v-else>
                    <template v-if="slotProps.data.status == 'awaiting'">
                        <FileUpload
                            v-tooltip.top="'Il consulente ha caricato la scheda prodotto. Carica il documento di polizza o proposta emessa.'"
                            mode="basic"
                            name="file"
                            :auto="true"
                            :customUpload="true"
                            chooseLabel="Carica file"
                            class="text-xs py-1 px-2"
                            accept=".pdf,application/pdf"
                            @uploader="(event) => onUpload(event, slotProps.data.id)"
                        />
                    </template>

                    <template v-else-if="slotProps.data.status == 'pending'">
                        <i class="text-yellow-600 pi pi-hourglass" v-tooltip.top="'Attendi che il consulente elabori la scheda prodotto.'"></i>
                    </template>

                    <template v-else-if="slotProps.data.status == 'completed'">
                        <i class="text-green-600 pi pi-check" v-tooltip.top="'Processo completato. Il consulente deve procedere con la firma della documentazione.'"></i>
                    </template>
                </template>
            </template>
        </Column>

    </DataTable>
</template>