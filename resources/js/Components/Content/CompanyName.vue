<script>
/**
 * API
 * @var {Object} company - The company object containing logo and name.
 */
import CompanyLogo from './CompanyLogo.vue';

export default {
    props: {
        company: Object,
        size: {
            type: [String, Number],
            default: 8,
        },
    },
    components: {
        CompanyLogo,
    },
    computed: {
        sizeClass() {
            return `h-${this.size} w-${this.size}`;
        }
    }
}
</script>

<template>
    <div class="flex items-center gap-3">
        <CompanyLogo :company="company" :size="size" />
        <span>{{ company.name }}</span>
    </div>
</template>