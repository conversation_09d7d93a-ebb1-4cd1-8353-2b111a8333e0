<script>
import Tag from 'primevue/tag';
import Debug from '../Debug.vue';

export default {
    props: {
        file: Object,
        pipeline: Object,
    },

    components: {
        Tag,
        Debug
    },

    setup() {
        
    },
}
</script>

<template>
    <div class="mb-2">
        <Debug :show="0">#{{ file.id }}</Debug>
        <div class="flex items-start gap-3">
            <!-- Tag di stato -->
            <div class="flex-shrink-0 pt-1">
                <Tag
                    v-if="file.type=='folder'"
                    value="Firmato"
                    icon="pi pi-check"
                    severity="success"
                    class="text-xs px-2 py-1"
                />
                <Tag
                    v-else-if="file.type=='compiled'"
                    value="Temporaneo"
                    icon="pi pi-spinner"
                    severity="secondary"
                    class="text-xs px-2 py-1"
                />
                <Tag
                    v-else-if="file.type=='signable'"
                    value="Da firmare"
                    icon="pi pi-hourglass"
                    severity="warn"
                    class="text-xs px-2 py-1"
                />
            </div>
            <!-- Nome e descrizione -->
            <div>
                <div>
                    <a
                        v-if="file.document"
                        :href="`/documents/${file.uuid}`"
                        target="_blank"
                        class="font-semibold text-sm text-cyan-700 hover:underline"
                    >
                        {{ file.document.title }}
                    </a>
                    <a
                        v-else-if="file.type == 'folder'"
                        :href="`/documents/${file.uuid}`"
                        target="_blank"
                        class="font-semibold text-sm text-cyan-700 hover:underline"
                    >
                        {{ file.displayName }}
                    </a>
                </div>
                <div>
                    <span class="text-xs text-gray-500" v-if="file.document">
                        {{ file.document.description }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>