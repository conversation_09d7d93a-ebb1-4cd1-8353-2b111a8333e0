<script>
import Dialog from 'primevue/dialog';
import EPMessage from '../EPMessage.vue';
import But<PERSON> from 'primevue/button';
import { dateFormatter } from '../../dateFormatter';
import ProfileShort from './ProfileShort.vue';
import { router, usePage } from '@inertiajs/vue3';
import { ref } from 'vue';

export default {
    props: {
        showModal: {
            type: Boolean,
            required: true,
            default: false,
        },
        selectedClient: Object,
        pipeline: Object,
        task: Object,
    },

    components: {
        Dialog,
        EPMessage,
        Button,
        ProfileShort,
    },

    setup(props) {
        const loading = ref(false);

        const next = function(client) {
            loading.value = true;

            router.put(
                `/tasks/client/${props.pipeline.id}/${props.task.id}`,
                { entity: client },
                {
                    onFinish: () => {
                        loading.value = false;
                    }
                }
            );
        };

        const skip = async function(client) {
            // @todo FINALIZE
            return;



            loading.value = true;

            try {
                router.post(`/pipeline/${props.pipeline.id}/skip/${props.task.config?.canSkipTo}`, {
                    entity: client,
                }, {
                    onFinish: () => {
                        loading.value = false;
                    }
                });

            } catch (error) {
                // TODO: only log in dev mode
                console.error('Errore nella richiesta PUT:', error);

                toast.add({severity: 'error', summary: 'Errore', detail: 'Si è verificato un errore durante la richiesta.', life: 3000});
                
            } finally {
                loading.value = false;
            }
        };
            
        return {
            dateFormatter,
            next,
            skip,
            loading, 
        };
    },

    
}
</script>

<template>
    <Dialog v-model:visible="showModal" header="Cliente selezionato" :style="{ width: '75%' }" :position="top" :modal="true" :draggable="false">
        <div v-if="selectedClient.id">
            <EPMessage severity="neutral" title="Dettagli cliente" icon="pi pi-user" class="mb-4">
                <div>{{ selectedClient.type == 'individual' ? 'Persona fisica' : 'Persona giuridica' }}</div>
                <div>{{ selectedClient.name }}</div>
                <div>Codice fiscale: {{ selectedClient.code }}</div>
            </EPMessage>
            
        </div>

        <hr class="my-5">

        <div v-if="selectedClient.profile">
            <ProfileShort :profile="selectedClient.profile" />

            <div class="flex flex-col items-center my-6">
                <div class="mb-4 text-center text-md text-gray-700">
                    Scegli come procedere con il profilo di questo cliente
                </div>
                <div class="flex justify-center gap-8">
                    <div class="w-56 h-48 flex flex-col items-center justify-center rounded-xl px-4 py-6 bg-gray-50 shadow border">
                        <span class="text-sm text-gray-300 text-center mb-3">
                            Se le esigenze del cliente non sono cambiate usa questa opzione.
                        </span>
                        <Button
                            @click="skip(selectedClient)"
                            :loading="loading"
                            label="Procedi"
                            severity="primary"
                            size="small"
                            class="mt-3"
                            :disabled="true"
                        />
                    </div>
                    <div class="w-56 h-48 flex flex-col items-center justify-center rounded-xl px-4 py-6 bg-gray-50 shadow border">
                        <span class="text-sm text-gray-500 text-center mb-3">
                            Se le esigenze del cliente sono cambiate compila un nuovo demands and needs.
                        </span>
                        <Button
                            @click="next(selectedClient)"
                            :loading="loading"
                            label="Procedi"
                            severity="primary"
                            size="small"
                            class="mt-3"
                        />
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="flex justify-center gap-2 mt-5">
            <Button type="button" size="small" label="Usa questo cliente" :loading="loading" @click="next(selectedClient)"></Button>
        </div>

    </Dialog>

</template>