<script>
import IssuanceList from './IssuanceList.vue';
import Product from './Product.vue';
import { dateFormatter } from '../../dateFormatter';

export default {
    props: {
        pipeline: Object,
        task: Object,
        mapperResult: Array,
        issuances: Array,
    },

    components: {
        Product,
        IssuanceList,
    },

    setup() {
        return {
            dateFormatter,
        }
    }
}
</script>
<template>
    
    <template v-if="task.type == 'client'">
        <span v-if="task.state == 'closed'" class="text-sm text-gray-600">
            Cliente: {{ pipeline.client?.person?.name }} {{ pipeline.client?.person?.lastname }}
        </span>
        
        <span v-else>
            Raccolta dati in corso.
        </span>
    </template>

    <template v-else-if="task.type == 'survey'">
        <span v-if="task.state == 'closed'" class="text-sm text-gray-600">
            Questionario completato.
        </span>
        <span v-else>
            Compilazione questionario in corso.
        </span>
    </template>

    <template v-else-if="task.type == 'mapper'">
        <span v-if="task.state == 'closed'" class="text-sm text-gray-600">
            <span class="text-sm text-gray-400">Prodotti coerenti</span>
            <Product class="mt-1" v-for="result in mapperResult" :product="result.product" asBadge="1" />
        </span>
        <span v-else>
            Scelta prodotti in corso.
        </span>
    </template>

    <template v-else-if="task.type == 'signature'">
        <span v-if="task.data?.signatureStatus == 'pending'">
            In attesa della firma cliente. <br>
            <span class="text-xs text-gray-600">Scadenza fascicolo: {{ dateFormatter(task.data?.folder?.expires_at) }}</span>
        </span>
        <span v-else-if="task.data?.signatureStatus == 'done'" class="text-sm text-gray-600">
            Firma completata
        </span>
        <span v-else>
            In attesa che la documentazione sia inviata al cliente
        </span>
    </template>

    <template v-else-if="task.type == 'issuance'">
        <span v-if="task.data?.issuanceStatus == 'pending'">
            Le schede prodotto sono in fase di elaborazione.
        </span>
        <span v-else>
            <span class="text-sm text-gray-400">Preparazione completata.</span>
            <Product class="mt-1" v-for="issuance in issuances" :product="issuance.product" asBadge="1" />
        </span>
    </template>

    <template v-else>
        -
    </template>
</template>