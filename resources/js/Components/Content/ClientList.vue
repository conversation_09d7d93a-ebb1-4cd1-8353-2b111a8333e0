<script>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { dateFormatter } from '@/dateFormatter';

export default {
    props: {
        clients: Array
    },

    components: {
        DataTable,
        Column,
    },

    setup() {
        return {
            dateFormatter
        }
    },
}

</script>

<template>
    <DataTable :value="clients" class="p-datatable-sm text-sm" responsiveLayout="scroll">
        <Column field="id" header="#">
            <template #body="slotProps">
                {{ slotProps.data.id }}
            </template>
        </Column>

        <Column field="id" header="Tipo">
            <template #body="slotProps">
                {{ slotProps.data.type }}
                <span v-if="slotProps.data.enterprise_id">
                    Azienda
                </span>
                <span v-if="slotProps.data.person_id">
                    Persona Fisica
                </span>
            </template>
        </Column>

        <Column field="name" header="Nominativo">
            <template #body="slotProps">
                <a :href="`/clients/${slotProps.data.id}`">
                    {{ slotProps.data.name }} 
                </a>
            </template>
        </Column>

        <Column field="created_at" header="">
            <template #header>
                <div class="w-full text-right font-semibold">
                    Creato
                </div>
            </template>
            <template #body="slotProps">
                <div class="text-right">
                    {{ dateFormatter(slotProps.data.created_at) }}
                </div>
            </template>
        </Column>

    </DataTable>
</template>