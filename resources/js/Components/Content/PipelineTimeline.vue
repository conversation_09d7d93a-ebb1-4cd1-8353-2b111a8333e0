<script setup>
import Timeline from 'primevue/timeline';
import Button from 'primevue/button';
import { computed } from 'vue';
import Task from './Task.vue';

const props = defineProps({
    pipeline: {
    type: Object,
    required: true
    },
    mapperResult: Array,
    issuances: Array,
});

// Prendi solo i task fino a quello con state = 'progress' (incluso)
const visibleTasks = computed(() => {
  const tasks = props.pipeline.tasks || [];
  const idx = tasks.findIndex(t => t.state === 'progress');
  return idx === -1 ? tasks : tasks.slice(0, idx + 1);
});

function markerClass(state) {
  if (state === 'closed') return 'bg-green-600';
  if (state === 'progress') return 'bg-yellow-500 animate-pulse';
  return 'bg-gray-400';
}
function iconClass(state) {
  if (state === 'closed') return 'pi pi-check';
  if (state === 'progress') return 'pi pi-spin pi-spinner';
  return 'pi pi-circle';
}
function formatDate(date) {
  if (!date) return '';
  return new Date(date).toLocaleString('it-IT', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit' });
}
</script>

<template>
  <Timeline :value="visibleTasks" align="alternate" class="custom-timeline">
    <template #marker="slotProps">
      <span
        class="flex items-center justify-center w-8 h-8 rounded-full text-white"
        :class="markerClass(slotProps.item.state)"
      >
        <i :class="iconClass(slotProps.item.state)" class="Stext-xl"></i>
      </span>
    </template>
    <template #content="slotProps">
      <div class="bg-white rounded-xl shadow p-6 mb-4 max-w-md">
        <div class="font-semibold text-lg mb-1">{{ slotProps.item.displayName }}</div>
        <div class="text-xs text-gray-500 mb-2">
          {{ formatDate(slotProps.item.updated_at) }}
        </div>
        <div class="mb-2">
            <Task :pipeline="pipeline" :task="slotProps.item" :mapperResult="mapperResult" :issuances="issuances" />
        </div>
      </div>
    </template>
  </Timeline>
</template>

<style scoped>
.custom-timeline .p-timeline-event-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
</style>