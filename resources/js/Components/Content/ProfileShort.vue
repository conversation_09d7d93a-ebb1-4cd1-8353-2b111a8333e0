<script>
/**
 * API
 * @var {Object} profile - Should contain: mapper_result, mapper_result.product, mapper_result.product.company
 */
import { dateFormatter } from '../../dateFormatter';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import ProgressBar from 'primevue/progressbar';
import CompanyName from './CompanyName.vue';

export default {
    props: {
        profile: Object,
    },

    components: {
        DataTable,
        Column,
        ProgressBar,
        CompanyName,
    },

    setup(props) {
        const profile = props.profile;

        const ranks = profile?.mapper_result?.map(item => item.rank) ?? [];
        const maxRank = ranks.length ? Math.max(...ranks) : null;

        return {
            dateFormatter,
            maxRank,
        };
    },
}
</script>

<template>
    <template v-if="! profile">
        <p>Nessun profilo per questo cliente.</p>
    </template>

    <template v-else>
        <p>L'ultimo demands and needs valido per questo cliente è stato registrato il {{ dateFormatter(profile.created_at) }}.</p>
        <a :href="`/profiles/${profile.id}`">Accedi al dettaglio della posizione</a>

        <hr class="my-4" />

        <h4>Prodotti</h4>
        <DataTable :value="profile.mapper_result" class="text-sm" responsiveLayout="scroll">
            <Column field="company" header="Compagnia">
                <template #body="slotProps">
                    <CompanyName :company="slotProps.data.product?.company" />
                </template>
            </Column>
            <Column field="name" header="Nome prodotto" style="width: 60%;">
                <template #body="slotProps">
                    <div>
                        <a :href="`/products/${slotProps.data.product?.id}`">{{ slotProps.data.product?.name }}</a>
                    </div>
                </template>
            </Column>
            <Column field="" header="Coerenza relativa" style="width: 15%">
                <template #body="slotProps">
                    <div class="flex items-center gap-2">
                        <ProgressBar
                            :value="maxRank && slotProps.data.rank ? (slotProps.data.rank / maxRank * 100) : 0"
                            :showValue="false"
                            style="height: 0.5rem; width: 100px"
                            class="flex-1"
                        />
                    </div>
                </template>
            </Column>
        </DataTable>
    </template>
</template>