<script>
import InputText from "primevue/inputtext";
import FormMessage from "./Form/FormMessage.vue";
import Select from "primevue/select";
import FileUpload from "primevue/fileupload";
import { ref } from "vue";
import { router } from '@inertiajs/vue3';
import <PERSON>ton from "primevue/button";
import FormUtils from "../form.utils";
import AutoComplete from "primevue/autocomplete";
import countries from 'world-countries'; // <--- IMPORTA QUI
import Identity from "../Models/identity";

export default {
    components: {
        InputText,
        FormMessage,
        Select,
        FileUpload,
        Button,
        AutoComplete,
    },
    props: {
        errors: Object,
        document: Object,
        type: {
            type: String,
            default: null
        },
        exclude: {
            type: Array,
            default: () => []
        },
    },
    data() {
        let docTypes = Identity.docTypes;

        if (this.type) {
            docTypes = docTypes.filter((docType) => docType.code == this.type);
        }

        if (this.exclude.length > 0) {
            docTypes = docTypes.filter((docType) => ! this.exclude.includes(docType.code));
        }

        return {
            docType: docTypes,
            utils: new FormUtils(),
            suggestions: [],
        }
    },
    methods: {
        searchCountry(event) {
            const query = event.query ? event.query.toLowerCase() : '';
            this.suggestions = countries
                .filter(c =>
                    c.name.common.toLowerCase().includes(query) ||
                    (c.translations?.ita?.common && c.translations.ita.common.toLowerCase().includes(query))
                )
                .map(c => ({
                    name: c.translations?.ita?.common || c.name.common,
                    code: c.cca2
                }));
        },

        handleUploaded(event) {
            console.log('Documento caricato:', event);
        },

        submit() {
            router.post('/documents', { document: this.document });
        }
    }
}
</script>

<template>
    <div class="flex flex-col md:w-1/2 gap-5">
        <div class="md:w-1/2">
        <Select
            :class="utils.errorCss(errors, 'person.documents.id.type')"
            v-model="document.type"
            :options="docType"
            optionLabel="name"
            optionValue="code"
            placeholder="Tipo di documento"
            style="width: 100%;"/>
            <FormMessage :errors="errors" field="person.documents.id.type">Seleziona il tipo di documento.</FormMessage>
        </div>

        <div>
            <InputText :class="utils.errorCss(errors, 'person.documents.id.number')" v-model="document.number" placeholder="Numero documento"></InputText>
            <FormMessage :errors="errors" field="person.documents.id.number">Inserisci numero del documento.</FormMessage>
        </div>

        <div>
            <InputText :class="utils.errorCss(errors, 'person.documents.id.issuer')" v-model="document.issuer" placeholder="Ente emittente"></InputText>
            <FormMessage :errors="errors" field="person.documents.id.issuer">Inserisci il nome dell'ente emittente.</FormMessage>
        </div>

        <div>
            <AutoComplete
                :class="utils.errorCss(errors, 'person.documents.id.issuerCountry')"
                optionLabel="name"
                v-model="document.issuerCountry"
                :suggestions="suggestions"
                @complete="searchCountry"
                placeholder="Paese emittente"
            >
                <template #option="slotProps">
                    <div>
                        <div>{{ slotProps.option.name }}</div>
                        <small>{{ slotProps.option.code }}</small>
                    </div>
                </template>
            </AutoComplete>
            <FormMessage :errors="errors" field="person.documents.id.issuerCountry">
                Ricerca e seleziona il paese emittente
            </FormMessage>
        </div>

        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.issuerDate')" type="date" class="p-inputtext p-component" v-model="document.issuerDate" placeholder="Data di emissione" />
            <FormMessage :errors="errors" field="person.documents.id.issuerDate">Inserisci la data di emissione del documento</FormMessage>
        </div>
        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.expiry')" type="date" class="p-inputtext p-component" v-model="document.expiry" placeholder="Data di emissione" />
            <FormMessage :errors="errors" field="person.documents.id.expiry">Inserisci la data di scadenza del documento</FormMessage>
        </div>

        <div>
            <input :class="utils.errorCss(errors, 'person.documents.id.files')" type="file" @change="e => document.files[0] = e.target.files[0]" class="p-inputtext p-component" placeholder="File" />
            <FormMessage :errors="errors" field="person.documents.id.files">Carica una fotografia o scansione fronte/retro del documento</FormMessage>
        </div>
    </div>
</template>
