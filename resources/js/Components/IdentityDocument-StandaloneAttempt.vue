<script>
import InputText from "primevue/inputtext";
import FormMessage from "./Form/FormMessage.vue";
import Select from "primevue/select";
import FileUpload from "primevue/fileupload";
import { ref } from "vue";
import { router } from '@inertiajs/vue3';
import Button from "primevue/button";

export default {
    components: {
        InputText,
        FormMessage,
        Select,
        FileUpload,
        Button,
    },
    props: {
        errors: Object,
        type: {
            type: String,
            default: null
        },
        exclude: {
            type: Array,
            default: () => []
        },
    },
    data: function () {
        let docTypes = [
            { code: 'id', name: 'Carta d\'identità' },
            { code: 'passport', name: 'Passaporto' },
            { code: 'driving_license', name: 'Patente di guida' },

            // Health insurance card secondo gpt :/
            { code: 'hic', name: 'Tessera sanitaria / Codice fiscale' }
        ];

        if (this.type) {
            docTypes = docTypes.filter((docType) => docType.code == this.type);
        }

        if (this.exclude.length > 0) {
            docTypes = docTypes.filter((docType) => ! this.exclude.includes(docType.code));
        }

        return {
            docType: docTypes,

           // selectedType: ref(this.type || null),
            document: ref({
                type: this.type || null,
                number: '2234234',
                issuer: 'Comune di Milano',
                issuerCounrty: 'IT',
                issuerDate: '2020-01-01',
                expiry: '2020-01-01',
            }),
            number: ref('2234234'),
            issuer: ref('Comune di Milano'),
            issuerCounrty: ref('IT'),
            issuerDate: ref('2020-01-01'),
            expiry: ref('2020-01-01'),

            loading: false,
            uploaded: false,
            uploadError: null,
        }
    },
    methods: {
        async myUploader(files/*event*/) {
            this.loading = true;
            this.uploadError = null;
            
            const formData = new FormData();

            formData.append('type', this.document.type);
            formData.append('number', this.document.number);
            formData.append('issuer', this.document.issuer);
            formData.append('issuerCounrty', this.document.issuerCounrty);
            formData.append('issuerDate', this.document.issuerDate);
            formData.append('expiry', this.document.expiry);
            formData.append('doc[]', files /*event.files[0]*/);

            router.post('/clients/id/upload', formData, {
                forceFormData: true, // ⬅️ necessario per far funzionare FormData con Inertia
                preserveScroll: true,
                onSuccess: () => {
                    this.uploaded = true;

                    this.$emit('identityDocument:uploaded', this.document);
                },
                onError: (errors) => {
                    this.uploadError = 'Errore durante l\'upload';
                    console.error(errors);
                },
                onFinish: () => {
                    this.loading = false;
this.$emit('identityDocument:uploaded', this.document);                    
                },
            });

        },

    }
}
</script>

<template>
    <!-- 2 column flex grid with tailwind -->
    <div class="flex">
        <div class="w-1/4 mr-5">
            <div class="flex flex-col gap-3">
                <Select
                    v-model="document.type"
                    :options="docType"
                    optionLabel="name"
                    optionValue="code"
                    placeholder="Tipo di documento"
                    style="width: 100%;"/>

                <InputText required v-model="document.number" placeholder="Numero documento" />
                <input type="text" v-model="document.number" />
                <input type="text" v-model="document.issuerCounrty" />
                <input type="text" v-model="document.expiry" />
                <input type="text" v-model="document.issuerDate" />
            </div>
            

        </div>

        <div class="w-3/4 gap-2">
            <FileUpload
                name="doc"
                :multiple="false"
                url="/clients/id/upload"
                accept="image/*"
                :customUpload="true"
                @uploader="myUploader"
                
                :maxFileSize="1000000">

                <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
                    <div class="flex flex-wrap justify-between items-center flex-1 gap-4">
                        <div class="flex gap-2">
                            <Button @click="chooseCallback()" label="Scegli file" icon="pi pi-file" severity="secondary"></Button>
                            <Button @click="myUploader(files)" label="Carica" icon="pi pi-upload" severity="secondary" v-if="files && files.length > 0"></Button>
                        </div>
                    </div>
                </template>

                <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback, messages }">
                    {{ this.uploaded }}
                    <div v-if="files.length > 0">
                        <div class="flex flex-wrap gap-4">
                            <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="p-8 rounded-border flex flex-col border border-surface items-center gap-4">
                                <div>
                                    <img role="presentation" :alt="file.name" :src="file.objectURL" width="100" height="50" />
                                </div>
                                <span class="font-semibold text-ellipsis max-w-60 whitespace-nowrap overflow-hidden">{{ file.name }}</span>
                                <Badge value="Pending" severity="warn" />
                                <Button icon="pi pi-times" @click="onRemoveTemplatingFile(file, removeFileCallback, index)" outlined rounded severity="danger" />
                            </div>
                        </div>
                    </div>
                </template>

                <template #empty>
                    <div class="flex items-center justify-center flex-col">
                        <p class="mt-6 mb-0">Trascina i file o clicca sul pulsante per iniziare il caricamento.</p>
                    </div>
                </template>
            </FileUpload>
        </div>
    </div>

     


    <div style="display: flex; gap: 1rem;">
        <div style="flex: 1;">
            <Select
                v-model="document.type"
                :options="docType"
                optionLabel="name"
                optionValue="code"
                placeholder="Tipo di documento"
                style="width: 100%;"/>
        </div>

        <input type="text" v-model="document.number" />
        <input type="text" v-model="document.issuerCounrty" />
        <input type="text" v-model="document.expiry" />
        <input type="text" v-model="document.issuerDate" />

        <div style="flex: 0.5; display: flex; align-items: center;">
            <FileUpload
                name="doc"
                url="/clients/id/upload"
                accept="image/*"
                :customUpload="true"
                @uploader="myUploader"
                mode="basic"
                auto="true"
                :maxFileSize="1000000"
                :chooseLabel="this.loading ? 'Caricamento...' : 'Carica documento'"
                :disabled="loading">
            </FileUpload>
        </div>
        <div style="flex: 0.5; display: flex; align-items: center;">
            <span v-if="uploaded" style="color: green;">Documento caricato!</span>
            <span v-else-if="uploadError" style="color: red;">{{ uploadError }}</span>
        </div>
    </div>
</template>
