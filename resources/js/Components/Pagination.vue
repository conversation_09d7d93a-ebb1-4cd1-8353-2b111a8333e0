<template>
    <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6" v-if="links.length > 3">
        <div class="flex flex-1 justify-between sm:hidden">
            <a href="#" class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
            <a href="#" class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    <span class="font-medium">{{ from }}</span>
                    to
                    <span class="font-medium">{{ to }}</span>
                    of
                    <span class="font-medium">{{ total }}</span>
                    results
                </p>
            </div>
            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                <template v-for="(link, key) in links" :key="key">
                    <div v-if="link.url === null" :class="{'rounded-l-md' : key === 0, 'rounded-r-md' : key === (links.length - 1)}" class="relative inline-flex items-center px-4 py-2 text-sm text-gray-300 font-semibold ring-1 ring-inset ring-gray-300 hover:cursor-not-allowed focus:z-20 focus:outline-offset-0" v-html="link.label" />
                    <Link v-else
                          class="relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0"
                          :class="[{'rounded-l-md' : key === 0, 'rounded-r-md' : key === (links.length - 1)}, link.active ? 'bg-indigo-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 hover:cursor-default' : 'text-gray-900 hover:bg-gray-50' ]" :href="link.url" v-html="link.label" />
                </template>
            </nav>
        </div>
    </div>
</template>

<script>
import {Link} from "@inertiajs/vue3";

export default {
    props: {
        links: Array,
        from: Number,
        to: Number,
        total: Number,
    },
    components : {
        Link
    }
}
</script>
