<script>
export default {
    props: {
        icon: {
            type: String,
            default: 'pi pi-info-circle',
        },
        text: {
            type: String,
            default: 'text-sm',
        },
        title: {
            type: String,
            default: '',
        },
        severity: {
            type: String,
            default: 'info',
        },
    },
    computed: {
        severityClasses() {
            switch (this.severity) {
                case 'success':
                    return 'border-green-200 bg-green-50 text-green-900';
                case 'warn':
                    return 'border-yellow-200 bg-yellow-50 text-yellow-900';
                case 'neutral':
                    return 'border-gray-200 bg-gray-50 text-gray-700';
                case 'info':
                default:
                    return 'border-blue-200 bg-blue-50 text-cyan-600';
            }
        },
        iconColor() {
            switch (this.severity) {
                case 'success':
                    return 'text-green-600';
                case 'warn':
                    return 'text-yellow-600';
                case 'neutral':
                    return 'text-gray-500';
                case 'info':
                default:
                    return 'text-cyan-600';
            }
        }
    }
}
</script>

<template>
    <div class="my-5 p-3 rounded border font-light text-lg" :class="severityClasses">
        <div v-if="title" class="flex items-center mb-2">
            <i :class="`${icon} mr-2 ${iconColor}`"></i>
            <span class="font-semibold">{{ title }}</span>
        </div>
        <slot/>
    </div>
</template>