
<template>

    <template v-if="group && !field.isHidden">
        <div class="flex h-6 items-center">
            <input
                :id="field.name"
                :name="field.name"
                :value="field.name"
                v-model="modelValue"
                @input="handleClickOnTrigger(field.trigger, $event)"
                type="checkbox"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600" />
        </div>
        <div class="text-sm leading-6">
            <label :for="field.name" class="font-medium text-gray-900">{{ field.title }}</label>
            <Helper :text="field.helper" v-if="field.helper"></Helper>
            <p class="text-sm text-gray-500">{{ field.description }}</p>
        </div>
        <div v-if="errors && errors[field.name]">
            <span v-for="e in errors[field.name]" class="text-red-500 text-sm">{{ e }}</span>
        </div>

    </template>

    <template v-if="!group">
        <div class="relative flex gap-x-1.5 mb-3" v-show="!field.isHidden">
            <div class="h-6 items-center">
                <input
                    :id="field.name"
                    :name="field.name"
                    :value="field.name"
                    v-model="modelValue"
                    @input="handleClickOnTrigger(field.trigger, $event)"
                    type="checkbox"
                    class="h-4 w-4 rounded border-gray-300 text-indigo-600" />
            </div>
            <div class="text-sm leading-6 inline-flex flex-col mr-3">
                <label :for="field.name" class="font-medium text-gray-900">{{ field.title }}</label>
                <Helper :text="field.helper" v-if="field.helper"></Helper>
                <p class="text-sm text-gray-500">{{ field.description }}</p>
            </div>
            <div v-if="errors && errors[field.name]">
                <span v-for="e in errors[field.name]" class="text-red-500 text-sm">{{ e }}</span>
            </div>

        </div>
    </template>
    
</template>

<script>
import Helper from "@/Components/Form/Helper.vue";

export default {
    components: {Helper},
    props: {
        field: Object,
        modelValue: Object,
        group: Boolean,
        errors: Object
    },
    methods: {
        updateArray (event) {
            const arr = this.modelValue;

            if(arr.includes(event.target.value)) { // Remove if present
                arr.splice(arr.indexOf(event.target.value), 1)
            }
            else { // Add if not present
                arr.push(event.target.value)
            }

            this.$emit('update:modelValue', arr)
        },
        handleClickOnTrigger(trigger = null, event) {

            if (this.group) {
                return this.updateArray(event);
            }

            this.$emit('update:modelValue', event.target.checked)

            if (trigger) {
                this.$emit("customChange", trigger, null, null, event.target.checked)
            }
        }
    }
}
</script>
