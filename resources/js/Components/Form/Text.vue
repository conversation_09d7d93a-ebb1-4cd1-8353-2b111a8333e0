<script setup>
defineProps({
    field: Object,
    errors: Object,
    modelValue: Object
})
</script>

<template>
    <div class="mb-6">
        <label class="font-semibold leading-6 text-gray-900">
            {{ field.title }}
        </label>
        <input
            :class="{'border-red-700 border-2 border-solid': errors && errors[field.name]}"
            class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
            :value="modelValue"
            @input="$emit('update:modelValue', $event.target.value)"
        />
        <div v-if="errors && errors[field.name]">
            <span v-for="e in errors[field.name]" class="text-red-500 text-sm">{{ e }}</span>
        </div>
    </div>
</template>

<script>
export default {
  emits: ['update:modelValue']
}
</script>
