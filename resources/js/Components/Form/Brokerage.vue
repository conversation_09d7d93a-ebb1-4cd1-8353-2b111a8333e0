<script>
import RadioButton from 'primevue/radiobutton';
import InputNumber from 'primevue/inputnumber';
import FormMessage from './FormMessage.vue';

export default {
    props: {
        intermediazioneData: Object,
        errors: Object,
    },

    components: {
        RadioButton,
        InputNumber,
        FormMessage,
    },

    computed: {
        // Filter errors following the same pattern as Address.vue and PersonalData.vue
        filteredErrors() {
            if (! this.errors || ! this.errors.messages || ! this.errors.key) {
                return {};
            }

            return Object.fromEntries(
                Object.entries(this.errors.messages).filter(([key]) =>
                    key.startsWith(this.errors.key)
                )
            );
        }
    },

    methods: {
        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        },

        // Client-side validation methods
        validateImportoIntermediazione(importo) {
            // Must be a positive number if intermediazione is selected
            return importo && importo > 0;
        },

        validateEmissione(emissione) {
            // Must be a positive number if intermediazione is selected
            return emissione && emissione > 0;
        },

        // Input validation handlers
        onImportoIntermediazioneInput() {
            if (this.intermediazioneData.intermediazione === '1' && 
                this.intermediazioneData.importoIntermediazione && 
                !this.validateImportoIntermediazione(this.intermediazioneData.importoIntermediazione)) {
                // Could emit validation error or handle it as needed
            }
        },

        onEmissioneInput() {
            if (this.intermediazioneData.intermediazione === '1' && 
                this.intermediazioneData.emissione && 
                !this.validateEmissione(this.intermediazioneData.emissione)) {
                // Could emit validation error or handle it as needed
            }
        }
    }
}
</script>

<template>
    <div :class="{'error-border rounded-lg p-5': hasErrors()}">
        <!-- Intermediazione -->
        <div class="mb-8">
            <div class="font-semibold leading-6 text-gray-900">Intermediazione:</div>
            <div class="mt-1 space-x-6">
                <div class="inline-flex items-center gap-2">
                    <RadioButton 
                        v-model="intermediazioneData.intermediazione" 
                        inputId="interm-1" 
                        name="intermediazione" 
                        value="1" 
                    />
                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-1">Si</label>
                </div>
                <div class="inline-flex items-center gap-2">
                    <RadioButton 
                        v-model="intermediazioneData.intermediazione" 
                        inputId="interm-0" 
                        name="intermediazione" 
                        value="0" 
                    />
                    <label class="block text-sm font-medium leading-6 text-gray-900" for="interm-0">No</label>
                </div>
            </div>
            <FormMessage :errors="filteredErrors" :field="`${errors?.key}.intermediazione`">
                Seleziona una opzione
            </FormMessage>
        </div>

        <!-- Importi intermediazione (mostrati solo se intermediazione = '1') -->
        <div v-if="intermediazioneData.intermediazione === '1'">
            <div class="grid grid-cols-2 gap-x-5 gap-y-8">
                <div class="col-span-1">
                    <InputNumber
                        v-model="intermediazioneData.importoIntermediazione"
                        class="w-full"
                        mode="currency"
                        currency="EUR"
                        locale="it-IT"
                        placeholder="Intermediazione"
                        @input="onImportoIntermediazioneInput"
                    />
                    <FormMessage :errors="filteredErrors" :field="`${errors?.key}.importoIntermediazione`">
                        Inserisci l'importo di intermediazione
                    </FormMessage>
                </div>
                <div class="col-span-1">
                    <InputNumber
                        v-model="intermediazioneData.emissione"
                        class="w-full"
                        mode="currency"
                        currency="EUR"
                        locale="it-IT"
                        placeholder="Diritti di emissione"
                        @input="onEmissioneInput"
                    />
                    <FormMessage :errors="filteredErrors" :field="`${errors?.key}.emissione`">
                        Inserisci i diritti di emissione
                    </FormMessage>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-border {
    border: 2px solid #ef4444;
}
</style>
