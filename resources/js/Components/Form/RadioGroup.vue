<template>
    <fieldset class="mb-6">
        <legend class="font-semibold leading-6 text-gray-900">
            <span :class="{'text-red-500' : errors && errors[field.name]}">{{ field.title }}</span>
        </legend>
        <!--<p class="mt-1 text-sm leading-6 text-gray-600">These are delivered via SMS to your mobile phone.</p>-->
        <div class="mt-1" :class="{'space-y-3' : field.orientation === 'v', 'space-x-6' : field.orientation === 'h'}">
            <div v-for="o in field.options" class="relative" :class="{'flex gap-x-1.5' : field.orientation === 'v', 'inline-flex gap-x-1.5' : field.orientation === 'h'}">
                <div class="flex h-6 items-center">
                    <input
                        :id="o.name"
                        :value="o.value"
                        v-model="modelValue"
                        @change="customRule(o.triggerCallback, modelValue)"
                        @input="handleClickOnTrigger(o.trigger, o.triggerNegative); $emit('update:modelValue', $event.target.value)"
                        :name="o.name" type="radio" class="h-4 w-4 border-gray-300 text-indigo-600" />
                </div>
                <div class="text-sm leading-6">
                    <label :for="o.name" class="block text-sm font-medium leading-6 text-gray-900">
                        {{ o.title }}
                    </label>
                    <Helper :text="field.helper" v-if="field.helper"></Helper>
                    <p class="text-sm text-gray-500" v-if="o.description">{{ o.description }}</p>
                </div>

            </div>
        </div>
        <div v-if="errors && errors[field.name]">
            <span v-for="e in errors[field.name]" class="text-red-500 text-sm">{{ e }}</span>
        </div>
    </fieldset>
</template>

<script>
import Helper from "@/Components/Form/Helper.vue";
export default {
    components: {Helper},
    props: {
        field: Object,
        errors: Object,
        modelValue: Object
    },
    emits: ['update:modelValue'],
    methods: {
        handleClickOnTrigger(trigger = null, triggerNegative = null, callback = null) {
            if (trigger || triggerNegative || callback) {
                this.$emit("customChange", trigger, triggerNegative, callback)
            }
        },
        customRule(callback = null, modelValue) {
            if (callback) {
                this.$emit("customRule", callback, modelValue)
            }
        }
    }
}
</script>
