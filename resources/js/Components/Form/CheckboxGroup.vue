

<template>
    <fieldset class="mb-6">
        <legend class="font-semibold leading-6 text-gray-900">
            <ExclamationTriangleIcon v-if="errors && errors[field.name]" class="h-5 w-5 inline-block text-red-400"></ExclamationTriangleIcon>
            {{ field.title }}
        </legend>
        <div class="font-light text-gray-500" v-if="field.description">
            {{field.description}}
        </div>
        <div class="mt-1" :class="{'space-y-3' : field.orientation === 'v', 'space-x-6' : field.orientation === 'h'}">
            <div v-for="o in field.options" class="relative" :class="{'flex gap-x-1.5' : field.orientation === 'v', 'inline-flex gap-x-1.5' : field.orientation === 'h'}">
                <Checkbox :field="o" v-model="modelValue" :group="true" />
            </div>
        </div>
    </fieldset>
</template>

<script>
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';
import Checkbox from "@/Components/Form/Checkbox.vue";

export default {
    components: {
        Checkbox,
        ExclamationTriangleIcon
    },
    props: {
        field: Object,
        modelValue: Object
    },
}
</script>
