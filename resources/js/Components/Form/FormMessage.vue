<template>
    <template v-if="errors && errors[field]">
        <div class="text-red-500 text-sm">
            {{ errors[field] }} 
            <template v-if="customMessage">
                <span>{{ customMessage }}</span>        
            </template>
        </div>
    </template>
    <template v-else>
        <div class="text-xs p-1">
            {{ $slots.default()[0].children }}
        </div>
    </template>
</template>

<script setup>
    defineProps({
        errors: Object,
        field: String,
        customMessage: {
            type: String,
            default: null
        }
    });
</script>
  