<script>
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import FormMessage from './FormMessage.vue';

export default {
    props: {
        personalData: Object,
        errors: Object,
    },

    components: {
        InputText,
        Select,
        FormMessage,
    },

    data() {
        return {
            // Italian provinces for dropdown
            provinces: [
                { code: 'AG', name: '<PERSON><PERSON><PERSON>ento' },
                { code: 'AL', name: 'Alessand<PERSON>' },
                { code: 'AN', name: 'Ancona' },
                { code: 'A<PERSON>', name: '<PERSON><PERSON><PERSON>' },
                { code: 'AR', name: '<PERSON><PERSON>' },
                { code: 'AP', name: '<PERSON><PERSON><PERSON>' },
                { code: 'AT', name: '<PERSON><PERSON>' },
                { code: 'AV', name: 'Avellino' },
                { code: 'BA', name: '<PERSON>' },
                { code: 'BT', name: 'Barletta-Andria-Trani' },
                { code: 'B<PERSON>', name: '<PERSON><PERSON>' },
                { code: 'B<PERSON>', name: '<PERSON><PERSON><PERSON>' },
                { code: 'B<PERSON>', name: '<PERSON><PERSON>' },
                { code: '<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
                { code: '<PERSON><PERSON>', name: '<PERSON>' },
                { code: '<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
                { code: '<PERSON>', name: '<PERSON><PERSON><PERSON>' },
                { code: 'BR', name: 'Brindisi' },
                { code: 'CA', name: 'Cagliari' },
                { code: 'CL', name: 'Caltanissetta' },
                { code: 'CB', name: 'Campobasso' },
                { code: 'CI', name: 'Carbonia-Iglesias' },
                { code: 'CE', name: 'Caserta' },
                { code: 'CT', name: 'Catania' },
                { code: 'CZ', name: 'Catanzaro' },
                { code: 'CH', name: 'Chieti' },
                { code: 'CO', name: 'Como' },
                { code: 'CS', name: 'Cosenza' },
                { code: 'CR', name: 'Cremona' },
                { code: 'KR', name: 'Crotone' },
                { code: 'CN', name: 'Cuneo' },
                { code: 'EN', name: 'Enna' },
                { code: 'FM', name: 'Fermo' },
                { code: 'FE', name: 'Ferrara' },
                { code: 'FI', name: 'Firenze' },
                { code: 'FG', name: 'Foggia' },
                { code: 'FC', name: 'Forlì-Cesena' },
                { code: 'FR', name: 'Frosinone' },
                { code: 'GE', name: 'Genova' },
                { code: 'GO', name: 'Gorizia' },
                { code: 'GR', name: 'Grosseto' },
                { code: 'IM', name: 'Imperia' },
                { code: 'IS', name: 'Isernia' },
                { code: 'SP', name: 'La Spezia' },
                { code: 'AQ', name: "L'Aquila" },
                { code: 'LT', name: 'Latina' },
                { code: 'LE', name: 'Lecce' },
                { code: 'LC', name: 'Lecco' },
                { code: 'LI', name: 'Livorno' },
                { code: 'LO', name: 'Lodi' },
                { code: 'LU', name: 'Lucca' },
                { code: 'MC', name: 'Macerata' },
                { code: 'MN', name: 'Mantova' },
                { code: 'MS', name: 'Massa-Carrara' },
                { code: 'MT', name: 'Matera' },
                { code: 'VS', name: 'Medio Campidano' },
                { code: 'ME', name: 'Messina' },
                { code: 'MI', name: 'Milano' },
                { code: 'MO', name: 'Modena' },
                { code: 'MB', name: 'Monza e Brianza' },
                { code: 'NA', name: 'Napoli' },
                { code: 'NO', name: 'Novara' },
                { code: 'NU', name: 'Nuoro' },
                { code: 'OG', name: 'Ogliastra' },
                { code: 'OT', name: 'Olbia-Tempio' },
                { code: 'OR', name: 'Oristano' },
                { code: 'PD', name: 'Padova' },
                { code: 'PA', name: 'Palermo' },
                { code: 'PR', name: 'Parma' },
                { code: 'PV', name: 'Pavia' },
                { code: 'PG', name: 'Perugia' },
                { code: 'PU', name: 'Pesaro e Urbino' },
                { code: 'PE', name: 'Pescara' },
                { code: 'PC', name: 'Piacenza' },
                { code: 'PI', name: 'Pisa' },
                { code: 'PT', name: 'Pistoia' },
                { code: 'PN', name: 'Pordenone' },
                { code: 'PZ', name: 'Potenza' },
                { code: 'PO', name: 'Prato' },
                { code: 'RG', name: 'Ragusa' },
                { code: 'RA', name: 'Ravenna' },
                { code: 'RC', name: 'Reggio Calabria' },
                { code: 'RE', name: 'Reggio Emilia' },
                { code: 'RI', name: 'Rieti' },
                { code: 'RN', name: 'Rimini' },
                { code: 'RM', name: 'Roma' },
                { code: 'RO', name: 'Rovigo' },
                { code: 'SA', name: 'Salerno' },
                { code: 'SS', name: 'Sassari' },
                { code: 'SV', name: 'Savona' },
                { code: 'SI', name: 'Siena' },
                { code: 'SR', name: 'Siracusa' },
                { code: 'SO', name: 'Sondrio' },
                { code: 'TA', name: 'Taranto' },
                { code: 'TE', name: 'Teramo' },
                { code: 'TR', name: 'Terni' },
                { code: 'TO', name: 'Torino' },
                { code: 'TP', name: 'Trapani' },
                { code: 'TN', name: 'Trento' },
                { code: 'TV', name: 'Treviso' },
                { code: 'TS', name: 'Trieste' },
                { code: 'UD', name: 'Udine' },
                { code: 'VA', name: 'Varese' },
                { code: 'VE', name: 'Venezia' },
                { code: 'VB', name: 'Verbano-Cusio-Ossola' },
                { code: 'VC', name: 'Vercelli' },
                { code: 'VR', name: 'Verona' },
                { code: 'VV', name: 'Vibo Valentia' },
                { code: 'VI', name: 'Vicenza' },
                { code: 'VT', name: 'Viterbo' }
            ]
        }
    },

    computed: {
        // Filter errors following the same pattern as Address.vue and RealEstateRegistry.vue
        filteredErrors() {
            if (! this.errors || ! this.errors.messages || ! this.errors.key) {
                return {};
            }

            return Object.fromEntries(
                Object.entries(this.errors.messages).filter(([key]) =>
                    key.startsWith(this.errors.key)
                )
            );
        }
    },

    methods: {
        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        },

        // Client-side validation methods
        validateItalianPostalCode(cap) {
            // Italian postal codes are 5 digits
            const regex = /^[0-9]{5}$/;
            return regex.test(cap);
        },

        validatePhoneNumber(phone) {
            // Italian phone numbers: mobile (3xx) or landline (0xx) + 6-10 digits
            const regex = /^(3[0-9]{2}|0[0-9]{1,3})[0-9]{6,10}$/;
            return regex.test(phone.replace(/\s+/g, ''));
        },

        validateEmail(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email);
        },

        // Input validation handlers
        onCapInput() {
            if (this.personalData.cap && !this.validateItalianPostalCode(this.personalData.cap)) {
                // Could emit validation error or handle it as needed
            }
        },

        onPhoneInput() {
            if (this.personalData.telefono && !this.validatePhoneNumber(this.personalData.telefono)) {
                // Could emit validation error or handle it as needed
            }
        },

        onEmailInput() {
            if (this.personalData.email && !this.validateEmail(this.personalData.email)) {
                // Could emit validation error or handle it as needed
            }
        }
    }
}
</script>

<template>
    <div :class="{'error-border rounded-lg p-5': hasErrors()}">
        <!-- Dati personali -->
        <div class="grid grid-cols-4 gap-x-5 gap-y-6">
            <!-- Nome -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.nome"
                    class="w-full"
                    placeholder="Nome"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.nome`">
                    Inserisci il nome.
                </FormMessage>
            </div>

            <!-- Cognome -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.cognome"
                    class="w-full"
                    placeholder="Cognome"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.cognome`">
                    Inserisci il cognome.
                </FormMessage>
            </div>

            <!-- Via -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.via"
                    class="w-full"
                    placeholder="Via/Largo/Piazza"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.via`">
                    Inserisci l'indirizzo.
                </FormMessage>
            </div>

            <!-- Numero civico -->
            <div class="col-span-1">
                <InputText
                    v-model="personalData.numVia"
                    class="w-full"
                    placeholder="Numero civico"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.numVia`">
                    Inserisci il numero civico.
                </FormMessage>
            </div>

            <!-- CAP -->
            <div class="col-span-1">
                <InputText
                    v-model="personalData.cap"
                    class="w-full"
                    placeholder="CAP"
                    maxlength="5"
                    @input="onCapInput"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.cap`">
                    Inserisci un CAP valido (5 cifre).
                </FormMessage>
            </div>

            <!-- Località -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.localita"
                    class="w-full"
                    placeholder="Città/Località"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.localita`">
                    Inserisci la città o località.
                </FormMessage>
            </div>

            <!-- Provincia -->
            <div class="col-span-1">
                <Select
                    v-model="personalData.provincia"
                    :options="provinces"
                    optionLabel="name"
                    optionValue="code"
                    placeholder="Seleziona provincia"
                    class="w-full"
                    filter
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.provincia`">
                    Seleziona la provincia.
                </FormMessage>
            </div>

            <!-- Telefono -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.telefono"
                    class="w-full"
                    placeholder="Numero di telefono"
                    @input="onPhoneInput"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.telefono`">
                    Inserisci un numero di telefono valido.
                </FormMessage>
            </div>

            <!-- Email -->
            <div class="col-span-2">
                <InputText
                    v-model="personalData.email"
                    type="email"
                    class="w-full"
                    placeholder="Indirizzo email"
                    @input="onEmailInput"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.email`">
                    Inserisci un indirizzo email valido.
                </FormMessage>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-border {
    border: 2px solid #ef4444;
}
</style>