<script>
/**
 * Previously used in person form to select birthplace. Not used anymore but can be useful for future implementations.
 */
import AutoComplete from 'primevue/autocomplete';
import InputText from 'primevue/inputtext';
import Select from 'primevue/select';
import axios from 'axios';
import { ref } from 'vue';
import FormMessage from './FormMessage.vue';

export default {
    props: {
        city: String,
        cssClass: Object,
        errors: Object,
    },

    components: {
        InputText,
        AutoComplete,
        Select,
        FormMessage,
    },

    data() {
        return {
            geo: ref({}),
            suggestions: ref([]),
        }
    },

    watch: {
        geo: {
            deep: true,
            handler(newGeo) {
                // Always emit string
                this.$emit('update:city', typeof newGeo === 'object' && newGeo !== null
                    ? newGeo.denominazione_ita
                    : newGeo);
            }
        }
    },

    methods: {
        async searchMunicipality(event) {
            if (! event.query.length || event.query.length < 3) {
                this.suggestions = [];
                return;
            }

            await axios.get(`/geo/${event.query}?_`).then(response => {
                this.suggestions = response.data;
            });
        }
    },
}
</script>

<template>
     <div>
        <AutoComplete optionLabel="denominazione_ita" v-model="geo" :suggestions="suggestions" @complete="searchMunicipality" placeholder="Cerca">
            <template #option="slotProps">
                <div>
                    <div>{{ slotProps.option.denominazione_ita }}</div>
                    <small>{{ slotProps.option.cap }}</small>
                </div>
            </template>
        </AutoComplete>
        <FormMessage :errors="errors" field="person.birthplace" customMessage="Devi selezionare uno dei valori suggeriti nella dropdown.">Seleziona il comune.</FormMessage>
    </div>
</template>


