<script>
import InputText from 'primevue/inputtext';
import InputNumber from 'primevue/inputnumber';
import FormMessage from './FormMessage.vue';

export default {
    props: {
        cadastralData: Object,
        errors: Object,
    },

    components: {
        InputText,
        InputNumber,
        FormMessage,
    },

    computed: {
        // Filter errors following the same pattern as Address.vue
        // The controller expects fields to follow the form of the JSON sent,
        // and uses the same nomenclature for errors.
        filteredErrors() {
            if (! this.errors || ! this.errors.messages || ! this.errors.key) {
                return {};
            }

            return Object.fromEntries(
                Object.entries(this.errors.messages).filter(([key]) =>
                    key.startsWith(this.errors.key)
                )
            );
        }
    },

    methods: {
        hasErrors() {
            return this.filteredErrors && Object.keys(this.filteredErrors).length;
        }
    }
}
</script>

<template>
    <div :class="{'error-border rounded-lg p-5': hasErrors()}">
        <!-- <PERSON>ti catastali -->
        <div class="grid grid-cols-2 gap-x-5 gap-y-6">
            <!-- Foglio -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.foglio"
                    class="w-full"
                    placeholder="Numero foglio"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.foglio`">
                    Inserisci il numero del foglio catastale.
                </FormMessage>
            </div>

            <!-- Particella -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.part"
                    class="w-full"
                    placeholder="Numero particella"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.part`">
                    Inserisci il numero della particella.
                </FormMessage>
            </div>

            <!-- Subalterno -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.sub"
                    class="w-full"
                    placeholder="Numero subalterno"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.sub`">
                    Inserisci il numero del subalterno.
                </FormMessage>
            </div>

            <!-- Categoria -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.cat"
                    class="w-full"
                    placeholder="Categoria catastale"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.cat`">
                    Inserisci la categoria catastale.
                </FormMessage>
            </div>

            <!-- Classe -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.classe"
                    class="w-full"
                    placeholder="Classe catastale"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.classe`">
                    Inserisci la classe catastale.
                </FormMessage>
            </div>

            <!-- Consistenza -->
            <div class="col-span-1">
                <InputText
                    v-model="cadastralData.consist"
                    class="w-full"
                    placeholder="Consistenza"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.consist`">
                    Inserisci la consistenza dell'immobile.
                </FormMessage>
            </div>

            <!-- Rendita -->
            <div class="col-span-2">
                <InputNumber
                    v-model="cadastralData.rendita"
                    class="w-full"
                    mode="currency"
                    currency="EUR"
                    locale="it-IT"
                    placeholder="Rendita catastale"
                />
                <FormMessage :errors="filteredErrors" :field="`${errors?.key}.rendita`">
                    Inserisci la rendita catastale.
                </FormMessage>
            </div>
        </div>
    </div>
</template>

<style scoped>
.error-border {
    border: 2px solid #ef4444;
}
</style>