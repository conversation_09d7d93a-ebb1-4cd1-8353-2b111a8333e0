<script>
import axios from "axios";
import AutoComplete from "primevue/autocomplete";

export default {
    components: {
        AutoComplete,
    },
    props: {
        /**
         * Controls whether to search for individuals or legal entities.
         * Also controls text shown in template accordingly.
         */
        type: {
            type: String,
            default: null
        },

        placeholder: {
            type: String,
            default: 'Cerca cliente'
        },
    },
    data: function () {
        return {
            __client: null,
            __clients: [],
            selectedClient: null,
        }
    },
    methods: {
        async searchClient(event) {
            if (! event.query.length || event.query.length < 3) {
                this.__clients = [];
                return;
            }

            await axios.get(`/clients/search/${event.query}`, {params: {type: this.type}}).then(response => {
                this.__clients = response.data;

                if (this.__clients.length > 0) {
                    this.__clients = [...response.data, {code: 'new'}];
                }
                
            });
        },

        onSelect(event) {
            this.$emit('person-search:selected', event);
        },

        handleItemNotFound() {
            this.$emit('person-search:not-found');
        }
    }
}
</script>

<template>
    <AutoComplete 
        class="w-full" style="--width: 50%;" 
        optionLabel="name" 
        v-model="__client" :suggestions="__clients" 
        @complete="searchClient" @item-select="onSelect"
        :placeholder=placeholder>

        <template #option="slotProps">
            <template v-if="slotProps.option.code == 'new'">
                <div class="p-2 cursor-pointer text-blue-500" @click="handleItemNotFound">
                    <div @click="handleItemNotFound" class="p-2 cursor-pointer text-blue-500">
                        <i class="pi pi-plus"></i>
                        Clicca qui per aggiungere una nuova anagrafica.
                    </div>
                </div>
            </template>
            <template v-else>
                <div>
                    <div>{{ slotProps.option.name }}</div>
                    <small>{{ slotProps.option.type == 'individual' ? 'Persona fisica' : 'Persona giuridica' }}</small>
                    <br>
                    <small>{{ slotProps.option.code }}</small>
                </div>
            </template>
        </template>

        <template #empty>
            <div @click="handleItemNotFound" class="p-2 cursor-pointer text-blue-500">
                Nessun risultato trovato. Clicca qui per aggiungere una nuova anagrafica.
            </div>
  </template>
    </AutoComplete>
    <div class="text-xs p-1" v-if="! type">Ricerca per nome, codice fiscale o partita iva.</div>  
    <div class="text-xs p-1" v-if="type == 'individual'">Ricerca per nome, cognome o codice fiscale.</div>  
    <div class="text-xs p-1" v-if="type == 'legal'">Ricerca per denominazione o partita iva.</div>  
    
</template>
