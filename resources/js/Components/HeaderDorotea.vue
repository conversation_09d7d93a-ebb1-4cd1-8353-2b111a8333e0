<script setup>
import {
    Bars3Icon,
    XMarkIcon
} from '@heroicons/vue/20/solid'
import { Disclosure, DisclosureButton, DisclosurePanel, Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import UserMenuButton from './UserMenuButton.vue';
import { Link } from '@inertiajs/vue3';

function checkUrl(string) {
    return window.location.pathname.toString() === string;
}

defineProps({
    user: Object,
    navigation: Array,
})

</script>

<template>

    <Disclosure as="nav" class="bg-gray-800" v-slot="{ open }">
        <div class="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8 sm:py-1">
            <div class="relative flex h-16 items-center justify-between">
                <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
                    <!-- Mobile menu button-->
                    <DisclosureButton class="relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                        <span class="absolute -inset-0.5" />
                        <span class="sr-only">Open main menu</span>
                        <Bars3Icon v-if="!open" class="block h-6 w-6" aria-hidden="true" />
                        <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
                    </DisclosureButton>
                </div>
                <div class="flex flex-1 items-center justify-center sm:justify-start">
                    <!-- Logo -->
                    <div class="flex flex-col flex-shrink-0 gap-y-0.5 items-center">
                        <img class="h-7 w-auto" src="../../assets/logo-easyprofile-4.svg" alt="EasyProfile" />
<!--                        <div class="h-px bg-gray-500 w-10/12"></div>-->
                        <img class="h-6 w-auto" src="../../assets/logo-dorotea.png" alt="Dorotea" />
                    </div>
                    <!-- Voci di menu -->
                    <div class="hidden sm:ml-6 sm:block">
                        <div class="flex space-x-4">
                            <Link v-for="item in navigation" :key="item.name" :href="item.href" :class="[checkUrl(item.href) ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white', 'rounded-md px-3 py-2 text-sm font-medium']" :aria-current="checkUrl(item.href) ? 'page' : undefined">{{ item.name }}</Link>
                            <!--
                            <a target="_blank" href="href" class="text-gray-300 hover:bg-gray-700 hover:text-white rounded-md px-3 py-2 text-sm font-medium">
                                Vai su EGG
                            </a>
                        -->
                        </div>
                    </div>
                </div>
                <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">

                    <!-- Profile dropdown -->
                    <Menu as="div" class="relative ml-3">
                        <div>
                            <UserMenuButton :user="user"></UserMenuButton>
                        </div>
                        <transition enter-active-class="transition ease-out duration-100" enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
                            <MenuItems class="absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <MenuItem>
                                    <a href="#" :class="['block px-4 py-2 text-sm text-gray-400']">{{ user.name }} {{ user.lastname }}</a>
                                </MenuItem>
                                <MenuItem v-slot="{ active }">
                                    <Link class="block px-4 py-2 text-sm text-gray-700" href="/logout" method="post">Logout</Link>
                                </MenuItem>
                            </MenuItems>
                        </transition>
                    </Menu>
                </div>
            </div>
        </div>

        <DisclosurePanel class="sm:hidden">
            <div class="space-y-1 px-2 pb-3 pt-2">
                <DisclosureButton v-for="item in navigation" :key="item.name" as="a" :href="item.href" :class="[item.current ? 'bg-gray-900 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white', 'block rounded-md px-3 py-2 text-base font-medium']" :aria-current="item.current ? 'page' : undefined">{{ item.name }}</DisclosureButton>
            </div>
        </DisclosurePanel>
    </Disclosure>

</template>
