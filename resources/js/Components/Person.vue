<script>
import InputText from "primevue/inputtext";
import City from "./Form/City.vue";
import Address from "./Form/Address.vue";
import FormMessage from "./Form/FormMessage.vue";
import Select from "primevue/select";
import TaxCode from "../cf";
import IdentityDocument from "./IdentityDocument.vue";      
import { ref } from "vue";
import FormUtils from "../form.utils";
import Dropdown from "primevue/dropdown";
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';
import Checkbox from "primevue/checkbox";
import Birthplace from "./Form/Birthplace.vue";

const prefixes = getCountries().map((countryCode) => {
  const callingCode = getCountryCallingCode(countryCode);
  return {
    label: `+${callingCode} ${countryCode}`,
    value: `+${callingCode}`,
  };
}).sort((a, b) => a.label.localeCompare(b.label));

export default {
    components: {
        InputText,
        City,   
        Address,
        FormMessage,
        Select,
        IdentityDocument,
        Dropdown,
        Checkbox,
        Birthplace,
    },
    props: {
        person: Object,
        errors: Object,
        disabled: {
            type: Boolean,
            default: false
        },
    },
    data: function () {
        return {
            cf: new TaxCode(),

            docType: [{code: 'ID', name: 'Carta d\'identità'}, {code: 'DL', name: 'Patente'}, {code: 'PS', name: 'Passaporto'}],

            stage: ref('docs'), 

            utils: new FormUtils(),

            prefixes: prefixes,
        }
    },
    methods: {
        setBirthDate(person) {
            person.birthdate = this.cf.getBirthDate(person.taxCode);
        },

        handleUploaded(event) {
            this.stage = 'data';
        },
    }
}
</script>

<template>
    <template v-if="disabled">
        <div class="p-4 rounded-2xl bg-white">
            <h2 class="text-lg font-semibold mb-2">{{ person.name }} {{ person.lastname }}</h2>
            <div class="text-sm space-y-1 text-gray-600">
                <div><strong>Codice Fiscale:</strong> <span class="uppercase">{{ person.taxCode }}</span></div>
                <div><strong>Data di nascita:</strong> {{ person.birthdate }}</div>
                <div><strong>Indirizzo:</strong> Via Roma 1, Milano</div>
            </div>
        </div>
    </template>

    <template v-else>
        <h2>Documenti</h2>

        <h3>Documento di identità</h3>
        <div>
            <IdentityDocument :errors="errors" :document="person.documents?.id" :exclude="['hic']" @identityDocument:uploaded="handleUploaded"></IdentityDocument>
        </div>

        <!--
        <h3>Tessera sanitaria</h3>
        <IdentityDocument type="hic" @identityDocument:uploaded="handleUploaded"></IdentityDocument>    
        -->

        <template v-if="1 || this.stage == 'data'">
            <h2>Dati anagrafici</h2>

            <div>
                <Select v-model="person.sex" :options="[{label: 'Maschio', value: 'M'}, {label: 'Femmina', value: 'F'}, {label: 'Altro', value: 'A'}]" optionValue="value" optionLabel="label" placeholder="Genere" />
            </div>

            <div>
                <InputText required :class="utils.errorCss(errors, 'person.name')" class="md:w-1/2 capitalize" v-model="person.name" aria-describedby="username-help" placeholder="Nome di battesimo" />
                <FormMessage :form="form" field="person.name">Inserisci il nome di battesimo.</FormMessage>
            </div>

            <div>
                <InputText required :class="utils.errorCss(errors, 'person.lastname')" class="md:w-1/2 capitalize" v-model="person.lastname" aria-describedby="username-help" placeholder="Cognome" />
                <FormMessage :errors="errors" field="person.lastname">Inserisci il cognome.</FormMessage>
            </div>

            <div>
                <InputText @blur="setBirthDate(person)" required :class="utils.errorCss(errors, 'person.taxCode')" class="md:w-1/2 uppercase" pattern="^[a-zA-Z]{6}[0-9]{2}[ABCDEHLMPRSTabcdehlmprst][0-9]{2}[a-zA-Z][0-9]{3}[a-zA-Z]$" v-model="person.taxCode" aria-describedby="username-help" placeholder="Codice fiscale" />
                <FormMessage :errors="errors" field="person.taxCode">Inserisci il codice fiscale.</FormMessage>
            </div>

            <!-- Data di nascita -->
            <div>
                <input type="date" :class="utils.errorCss(errors, 'person.birthdate')" class="md:w-1/2 p-inputtext p-component" v-model="person.birthdate" placeholder="Data di nascita" />
                <FormMessage :errors="errors" field="person.birthdate">Inserisci la data di nascita.</FormMessage>
            </div>

            <h2>Luogo di nascita</h2>

            <div>
                <Birthplace :errors="{messages: errors, key: 'person.birthplace'}" v-model:birthplace="person.birthplace" />
            </div>

            <!-- Residenza -->
            <h2>Residenza</h2>
            <div>
                <Address :errors="{messages: errors, key: 'person.addresses.residence'}" :address="person.addresses.residence" type="residence" />
            </div>

            <h2>Contatti</h2>
            
            <!-- Telefono cellulare -->
            <div>
                <Dropdown
                v-model="person.phonePrefix"
                :class="utils.errorCss(errors, 'person.phonePrefix')"
                :options="prefixes"
                optionLabel="label"
                optionValue="value"
                placeholder=""
                filter
                />
                
                <FormMessage :errors="errors" field="person.phonePrefix">Seleziona il prefisso internazionale.</FormMessage>
            </div>
            <div>
                <InputText :class="utils.errorCss(errors, 'person.phone')" required class="md:w-1/2" v-model="person.phone" aria-describedby="username-help" placeholder="Telefono cellulare" />
                <FormMessage :errors="errors" field="person.phone">Inserisci il numero di telefono.</FormMessage>
            </div>

            <!-- Email -->
            <div>
                <InputText :class="utils.errorCss(errors, 'person.email')" required type="email" class="md:w-1/2" v-model="person.email" aria-describedby="username-help" placeholder="Email" />
                <FormMessage :errors="errors" field="person.email">Inserisci l'indirizzo email.</FormMessage>
            </div>

            <h2>Preferenze del cliente</h2>

            <div class="flex items-center">
                <Checkbox
                    v-model="person.preferences.marketing"
                    :binary="true"
                    inputId="marketing-checkbox"
                    class="cursor-pointer"
                />
                <label
                    for="marketing-checkbox"
                    class="ml-2 text-gray-600 cursor-pointer select-none"
                >
                    Consenso comunicazioni di marketing
                </label>
            </div>
            <FormMessage :errors="errors" field="person.preferences.marketing">
                Chiedi al cliente se desidera ricevere le comunicazioni marketing.
            </FormMessage>
        </template>
    </template>
</template>
