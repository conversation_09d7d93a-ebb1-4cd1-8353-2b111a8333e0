<script setup>

import {MapPinIcon, UserIcon} from "@heroicons/vue/20/solid";

defineProps({
    title: String
})

</script>

<template>
    <header class="bg-white shadow">
        <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            <div class="lg:flex lg:items-center lg:justify-between">
                <div class="min-w-0 flex-1">

                    <h2 class="text-lg font-bold leading-4 text-gray-900 sm:truncate sm:text-2xl sm:tracking-tight">
                        <span class="font-normal">{{ title }}</span>
                    </h2>
<!--
                    <div class="mt-1 flex flex-col sm:mt-0 sm:flex-row sm:flex-wrap sm:space-x-6">
                        <div v-for="item in items" class="mt-2 flex items-center text-sm text-gray-500">
                            <UserIcon class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" aria-hidden="true" />
                            {{ item || null }}
                        </div>
                    </div>
                -->
                </div>
            </div>
        </div>
    </header>
</template>

<style scoped>

</style>
