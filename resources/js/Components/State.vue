<script setup>
import { computed } from 'vue';
import Badge from './Badge.vue';

const props = defineProps({
    entity: Object,
    text: String,
    severity: String,
});

const stateMap = {
    open:      { text: 'In corso', color: 'warning' },
    closed:    { text: 'Chiusa', color: 'success' },
    canceled:  { text: 'Cancellata', color: 'danger' },
    completed: { text: 'Completata', color: 'success' },
    pending:   { text: 'In lavorazione', color: 'warning' },
    awaiting:  { text: 'Attesa caricamento backoffice', color: 'warning' },
};

const badgeData = computed(() => {
    if (props.text) {
        return { text: props.text, color: props.severity || 'info' };
    }
    const state = props.entity?.state || props.entity?.status;
    return stateMap[state] || { text: state || '-', color: 'info' };
});
</script>

<template>
    <Badge :text="badgeData.text" :color="badgeData.color" />
</template>