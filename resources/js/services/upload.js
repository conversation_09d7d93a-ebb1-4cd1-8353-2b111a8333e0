import { router } from '@inertiajs/vue3'

/**
 * Upload utility for single file uploads via Inertia.
 */
const Upload = {
    /**
     * Upload single file via Inertia with custom options.
     * 
     * @param {File} file - The file to upload.
     * @param {string} url - The endpoint URL.
     * @param {string} [method='post'] - HTTP method (post, put, etc).
     * @param {Object} [options={}] - Extra options:
     *   - fileField: nome campo file (default 'file')
     *   - data: oggetto con altri dati da inviare
     *   - csrfToken: token CSRF (default: auto da meta)
     *   - inertiaInstance: istanza inertia (default: importata qui)
     *   - onSuccess, onError, onFinish: callback inertia
     *   - forceFormData: forza FormData (default true)
     */
    single(file, url, method = 'post', options = {}) {
        if (!file || !url) throw new Error('File and URL are required for Upload.single.');

        // Mostra overlay
        window.dispatchEvent(new CustomEvent('upload:loading', { detail: true }));

        const fileField = options.fileField || 'file';
        const data = options.data || {};
        const inertiaInstance = options.inertiaInstance || router;
        let csrfToken = options.csrfToken;

        const formData = new FormData();
        formData.append(fileField, file);
        Object.entries(data).forEach(([key, value]) => formData.append(key, value));
        if (!(csrfToken = csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'))) {
            throw new Error('CSRF token is required for Upload.single.');
        }
        formData.append('_token', csrfToken);

        inertiaInstance[method](url, formData, {
            forceFormData: options.forceFormData !== false,
            onSuccess: options.onSuccess,
            onError: options.onError,
            onFinish: (...args) => {
                // Nascondi overlay
                window.dispatchEvent(new CustomEvent('upload:loading', { detail: false }));
                options.onFinish && options.onFinish(...args);
            },
            ...options.inertia
        });
    }
};

export default Upload;