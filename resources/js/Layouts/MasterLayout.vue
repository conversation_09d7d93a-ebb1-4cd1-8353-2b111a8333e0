<script>

import Header from "@/Components/Header.vue";
import HeaderDorotea from "@/Components/HeaderDorotea.vue";
import Navigation from "@/Components/Navigation.vue";
import Footer from "@/Components/Footer.vue";
import SlideOver from "../Components/SlideOver.vue";
import {usePage} from "@inertiajs/vue3";
import 'primeicons/primeicons.css';
import Toast from "primevue/toast";
import { useToast } from 'primevue/usetoast';
import Overlay from "@/Components/Overlay.vue";

export default {
    components: {
        Header,
        HeaderDorotea,
        Navigation,
        Footer,
        SlideOver,
        Toast,
        Overlay,
    },

    props: {
        user: Object,
        navItems: Object,
        tech: Object,
        roles: Object,
        title: String,
        ui: Object,
    },

    setup() {
        // Required to access "in-page", shared global props
        // coming from HandleInertiaRequests.
        const page = usePage()

        let env = import.meta.env;

        return {
            env,
            "data": page.props,
        }
    },

    created() {
        window.addEventListener('upload:loading', this.handleUploadLoading);
    },
    
    beforeUnmount() {
        window.removeEventListener('upload:loading', this.handleUploadLoading);
    },

    data() {
        return {
            uploading: false,
        }
    },

    mounted() {
        const toast = useToast();
        const page = usePage();

        // @todo finalize
        if (page.props.flash && page.props.flash.eptoast?.message) {
            toast.add({
                severity: page.props.flash.eptoast?.severity,
                summary: page.props.flash.eptoast?.summary,
                detail: page.props.flash.eptoast?.message + (page.props.flash.eptoast?.code ? ` Codice operazione: (${page.props.flash.eptoast?.code})` : ''),
                life: page.props.flash.eptoast?.life || 5000
            });
        }

        // Mostra toast se c'è un errore globale
        if (page.props.errors && page.props.errors.error) {
            toast.add({
                severity: 'error',
                summary: 'Errore',
                detail: page.props.errors.error + (page.props.errors.errorCode ? ` Codice errore: (${page.props.errors.errorCode})` : ''),
                life: 0
            });
        }

        if (page.props['notification'] && page.props['notification'].message) {
            toast.add({
                severity: 'info',
                summary: 'Notifica',
                detail: page.props['notification'].message,
                life: 5000
            });
        }
    },
    methods: {
        handleUploadLoading(e) {
        this.uploading = e.detail;
        }
    }
}
</script>

<template>
    <div class="min-h-full">

        <Toast/>

        <Overlay v-if="uploading" />

        <!-- Debug Panel -->
        <SlideOver v-if="this.env.DEV || ['development', 'staging'].includes(this.env.MODE)" :data="{tech: this.data.tech, user: this.data.user}"></SlideOver>

        <component :is="this.data.ui.header" :user="this.data.user" :navigation="this.data.navItems"></component>

        <component :is="this.data.ui.navigation" :title="title"></component>

        <main>
            <div class="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
                <slot>Loading...</slot>
            </div>
        </main>

        <Footer></Footer>

    </div>
</template>

