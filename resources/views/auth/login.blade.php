<!--<div>tenant: {{ app('currentTenant')->name }}</div>

<hr>

<form method="post" action="/login">
    @csrf
    <input type="text" name="email" value="<EMAIL>">
    <input type="password" name="password" value="password">
    <button type="submit">Login</button>
</form>-->

<!DOCTYPE html>
<html class="h-full bg-gray-100">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    @vite('resources/css/app.css')
</head>
<body class="h-full">

<div class="flex min-h-full flex-1 flex-col justify-center px-6 py-12 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-sm">
        <img class="mx-auto h-10 w-auto" src="/assets/logo.svg" alt="EasyProfile" />
        @env('local')
        <h2 class="mt-10 text-center text-2xl font-light leading-9 tracking-tight text-gray-900">Tenant: <span class="font-bold">{{ app('currentTenant')->name }}</span></h2>
        @endenv
    </div>

    <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
        <form class="space-y-6" action="/login" method="post">
            @csrf
            <div>
                <label for="email" class="block text-sm font-medium leading-6 text-gray-900">Email address</label>
                <div class="mt-2">
                    @env('local')
                    <input id="email" name="email" type="email" autocomplete="email" value="<EMAIL>" required="" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                    @endenv

                    @env(['staging', 'production'])
                    <input id="email" name="email" type="email" autocomplete="email" required="" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                    @endenv
                </div>
            </div>

            <div>
                <div class="flex items-center justify-between">
                    <label for="password" class="block text-sm font-medium leading-6 text-gray-900">Password</label>
                    <div class="text-sm">
                        <a href="/new-password" class="font-semibold text-indigo-600 hover:text-indigo-500">Password dimenticata?</a>
                    </div>
                </div>
                <div class="mt-2">
                    @env('local')
                    <input id="password" name="password" type="password" autocomplete="current-password" value="test" required="" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                    @endenv

                    @env(['staging', 'production'])
                    <input id="password" name="password" type="password" autocomplete="current-password" required="" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                    @endenv
                </div>
            </div>

            <div>
                <button type="submit" class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Accedi</button>
            </div>
        </form>

    </div>
</div>

</body>
</html>

