# First setup
Attualmente abbiamo una database/migrations/dev in cui sono appoggiate le migrations temporanee.


Cleanup things.

```
vendor/bin/sail artisan migrate:reset --database=landlord --path=database/migrations/landlord
vendor/bin/sail artisan tenants:artisan "migrate:reset --database=tenant --path=database/migrations/tenant"
```

or drop *tables* manually.

Migrate.

```
vendor/bin/sail artisan migrate --database=landlord --path=database/migrations/landlord --seed
vendor/bin/sail artisan tenants:artisan "migrate --database=tenant --path=database/migrations/tenant --seed"
```

Migrate for specific tenants.
```
vendor/bin/sail artisan tenants:artisan "migrate --database=tenant --path=database/migrations/dorotea" --tenant=2
```

Also, i've added an artisan command to refresh db for develop.
> vendor/bin/sail artisan dev:init

## For user/pipelines limit
Idea
https://stackoverflow.com/questions/2716232/maximum-number-of-records-in-a-mysql-database-table/2716470#2716470

# Ratio
- clients table is for static client data
- profile and profile_coverage table are for dynamic client data
