<?php namespace Upnovation\Easyprofile;

use Upnovation\Easyprofile\Errors;

class Toast
{
    public static function success(string $messageKey, $options = []) : array
    {
        // @TODO use lang instead of config
        if (! $message = config("easyprofile.toast.{$messageKey}")) {
            $message = "Operazione completata con successo.";
        }

        return self::make(
            $message, 
            'success',
            $options['code'] ?? null, 
            $options['ttl'] ?? 0
        );
    }

    public static function error(string $messageKey, $options = []) : array
    {
        return Errors::make(
            $options['task'] ?? null,
            $messageKey,
            $options['ttl'] ?? 0
        );
    }


    public static function make($message, $severity, ?string $code = null, ?int $ttl = 0) : array
    {
        return [
            'eptoast.severity' => $severity,
            'eptoast.summary' => $severity == 'success' ? 'Completato' : 'Errore',
            'eptoast.message' => $message,
            'eptoast.code' => $code,
            'ttl' => $ttl,
        ];
    }
}