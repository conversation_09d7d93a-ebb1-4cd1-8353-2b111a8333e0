<?php namespace Upnovation\Easyprofile;

use App\Models\Task;

/**
 * To be used with $response->withErrors();
 * i.e. $response->withErrors(Errors::make($task, 'signature'));
 */
class Errors
{
    public static function make(Task $task, string $messageKey, ?int $ttl = 0) : array
    {
        // @TODO use lang instead of config
        if (! $message = config("easyprofile.errors.{$messageKey}")) {
            $message = "Si è verificato un errore imprevisto durante l'elaborazione della richiesta.";
        }

        $user = auth()->user();

        return [
            'error' => $message,
            'errorCode' => "{$task->pipeline->id}-{$task->id}-{$user->id}-" . date('YmdHis'),
            'ttl' => $ttl,
        ];
    }
}