<?php namespace Upnovation\Easyprofile\Dorotea;

use App\Models\EggProduct;
use App\Models\Form;
use App\Models\Pipeline;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use stdClass;
use Upnovation\Easyprofile\Dorotea\UserManager as DoroteaUserManager;
use Upnovation\Easyprofile\UserManager;

class EggManager
{

    //
    //
    //  Auth
    //
    //
    //  =====================================================================

    public function auth($userId, $token)
    {
        try {
            $eggUser = $this->getUser($userId);
        } catch (\Exception $ex) {
            Log::info("SSO failure for {$userId}/{$token}");
            return null;
        }

        if (! $eggUser) {
            //Log::debug("SSO failure: empty response");
            return null;
        }

        if (! $eggUser->session_token) {
            Log::debug("SSO failure: no session token");
            return null;
        }

        if ($eggUser->session_token != $token) {
            Log::debug(env('APP_ENV') == 'production' ? "SSO failure: token mismatch" : "SSO failure: token mismatch {$eggUser->session_token}/{$token}");
            return null;
        }

        return $eggUser;
    }

    public function getUser($userId) 
    {
        try {
            $tokenData = $this->getToken();

            // Assert on token.
            $tokenData->access_token;
        } catch (\Exception $ex) {
            Log::debug($ex->getMessage());
            Log::debug($ex->getTraceAsString());
            Log::info("SSO failure: cannot get token for {$userId}");
            return null;
        }

        $user = $this->call("GET", "data/entity/user/record/{$userId}", $tokenData->access_token);

        if (! $user || ! property_exists($user, 'result') || ! $user->result) {
            Log::debug("EGG failure: empty response");
            return null;
        }

        $properties = ['session_token', 'utenteID', 'nome', 'cognome', 'email1'];

        $failure = false;

        foreach($properties as $property) {
            if (! property_exists($user->result, $property)) {
                Log::debug("EGG failure: {$property} is empty");
                $failure = true;
            }
        }

        if ($failure) {
            return null;
        }

        return $user->result;
    }

    //
    //
    //  Opportunity management
    //
    //
    //  =====================================================================

    public function updateOpportunity(Pipeline $pipeline)
    {
        $data = $this->formatOpportunityData(
            $pipeline->getProfile()->mapperResult()->get(),
            $pipeline->form
        );

        $result = $this->patchOpportunity(
            in_array(env('APP_ENV'), ['testing']) ? 1 : $pipeline->egg_opportunity_id,
            $data
        );
        
        if (! $result || ! property_exists($result, 'ok') || ! $result->ok) {
            Log::debug("Bad patch opportunity result: " . print_r($result, true));

            return false;
        }
        
        return $result->ok;
    }

    /**
     * Entry point to opportunity update API.
     *
     * @param Collection $results 
     * MapperResult collection.
     * 
     * @param Form $form 
     * The pipeline form.
     * 
     * @return array 
     * The array of dataupdated to EGG.
     */
    public function formatOpportunityData(Collection $results, Form $form) : array
    {
        // There are no results.
        if (! $results->count()) {
            throw new Exception("Mapper results are empty.");
        }

        // There's more than 1 confirmed product.
        $filtered = $results->filter(function($item){
            return $item->confirmed;
        });

        if ($filtered->count() != 1) {
            throw new Exception("Bad confirmed results.");
        }
        
        $data = [];

        // Initialize product fields with false condition.
        foreach (EggProduct::all() as $eggProduct) {
            $data[$eggProduct->field] = "NO";
        }

        // Set to true products in the result, also set
        // the confirmed one.
        foreach($results as $result) {
            $data[$result->product->eggProduct->field] = "SI";

            if ($result->confirmed) {
                $data['c__prodrichie'] = $result->product->eggProduct->id;
            }
        }

        // Finally, add the form JSON.
        $data['c__jsonesito'] = json_encode($form->cachedResult);

        return $data;
    }

    /**
     * Calls EGG API to update the opportunity entity.
     *
     * @param [type] $opportunityId
     * @param array $data
     * @return stdClass
     */
    public function patchOpportunity($opportunityId, array $data) : stdClass
    {
        try {
            $tokenData = $this->getToken();
        } catch (\Exception $ex) {
            Log::debug($ex->getTraceAsString());
            Log::error($ex->getMessage());
            Log::error("Token failure: cannot get token");
            throw $ex;
        }

        try {
            return $this->call(
                "PATCH", 
                "data/entity/opportunity/record/{$opportunityId}",
                $tokenData->access_token,
                null, 
                $data
            );
        } catch (\Exception $ex) {
            Log::debug($ex->getTraceAsString());
            Log::error($ex->getMessage());
            Log::error("EGG Server error.");
            throw $ex;
        }
    }

    /**
     * EGG redirect url.
     *
     * @param Pipeline $pipeline
     * @return void
     */
    public function getRedirectUrl(Pipeline $pipeline)
    {
        if (! $pipeline->egg_opportunity_id) {
            throw new Exception("EGG Opportunity is null for pipeline {$pipeline->id}");
        }

        $url = sprintf(env("EGG_REDIRECT"), $pipeline->egg_opportunity_id);

        if (! preg_match("/opzione=([a-zA-Z0-9]+)/", $url)) {
            throw new Exception("EGG Opportunity has invalid characters for pipeline {$pipeline->id}");
        }

        return $url;
    }

    //
    //
    //  Users Sync.
    //
    //
    //  =====================================================================

    public function loadUsers()
    {
        try {
            $tokenData = $this->getToken();
        } catch (\Exception $ex) {
            Log::debug($ex->getTraceAsString());
            Log::error($ex->getMessage());
            Log::error("Token failure: cannot get token");
            throw $ex;
        }

        try {
            $json = stripslashes(env("EGG_USER_QUERY"));

            $json = json_decode($json);

            $users =  $this->call(
                "POST", 
                "data/entity/user/queryV2/objects",
                $tokenData->access_token,
                null, 
                $json
            );

            if (! $users) {
                throw new Exception("EGG returned empty users response.");
            }

            if (! property_exists($users, 'ok') || ! property_exists($users, 'result')) {
                throw new Exception("EGG returned bad users response.");
            }

            if (! $users->result) {
                throw new Exception("EGG returned empty users list.");
            }

            return $users->result;

        } catch (\Exception $ex) {
            Log::debug($ex->getTraceAsString());
            Log::error($ex->getMessage());
            Log::error("EGG Server error.");
            throw $ex;
        }
    }
    

    //
    //
    //  API methods.
    //
    //
    //  =====================================================================

    public function getToken() 
    {
        return $this->call("GET", "oauth/token", null, [
            'grant_type' => 'password',
            'client_id' => env('EGG_CLIENTID'),
            'client_secret' => env('EGG_SECRET'),
            'username' => env('EGG_USERNAME'),
            'password' => env('EGG_PASSWORD'),
        ]);
    }

    public function call($method, $uri, $token, $params = null, $json = null) 
    {
        $url = env('EGG_ENDPOINT') . "/{$uri}?" . $this->params($params);
        
        $curl = curl_init();

        $headers = [
            "User-Agent: easyprofile/0.0.0"
        ];

        if ($token) {
            $headers[] = "Authorization: Bearer {$token}";
        }

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_POSTFIELDS => $json ? json_encode($json) : null,
            CURLOPT_HTTPHEADER => $headers,
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception($err);
        } 
            
        return  json_decode($response);
    }

    public function params($params)
    {
        if (! $params) {
            return null;
        }

        $query = [];

        foreach($params as $key => $value) {
            if (
                ! $this->checkParam($key) || 
                ! $this->checkParam($value) 
            ) {
                throw new Exception("Invalid params {$key} {$value}");
            }
            $query[] = "{$key}={$value}";
        }

        return implode('&', $query);
    }

    public function checkParam($string) 
    {   
        $string = trim($string);
        
        return ! empty($string) && preg_match("/^[a-zA-Z0-9_.\-%!]*$/", $string);
    }
}