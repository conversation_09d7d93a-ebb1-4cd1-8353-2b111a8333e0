<?php namespace Upnovation\Easyprofile\Dorotea;

use App\Models\NetworkNode;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\UserManager as EasyprofileUserManager;

class UserManager
{
    /** @var EggManager */
    protected $egg;

    /**
     * Undocumented variable
     *
     * @var EasyprofileUserManager
     */
    protected $manager;

    public function __construct(
        EggManager $egg,
        EasyprofileUserManager $manager
    ) 
    {
        $this->egg = $egg;
        $this->manager = $manager;
    }

    public function auth($userId, $token)
    {
        if (! $eggUser = $this->egg->auth($userId, $token)) {
            return null;
        }

        if (! $user = $this->getUser($eggUser)) {
            return null;
        }

        return $this->login($user);
    }

    public function login($user)
    {
        if (! $user->active) {
            return null;
        }
        
        Auth::guard('web')->login($user);  

        return $user;
    }

    public function getUser($eggUser) 
    {
        if ($user = User::whereEggId($eggUser->utenteID)->first()) {
            return $user;
        }

        return Config::get('easyprofile.tenants.dorotea.createUserOnLogin') ? $this->createUserFromEgg($eggUser) : null;
    }

    public function createUserFromEgg($eggUser)
    {
        // Fixes "Bcrypt password must not contain null character" error.
        $rand = preg_replace('/\x00/', rand(0,9), random_bytes(64));

        $user = new User();
        $user->password = Hash::make($rand);
        $user->egg_id = $eggUser->utenteID;
        $user->name = $eggUser->nome;
        $user->lastname = $eggUser->cognome;
        $user->email = $eggUser->email1;
        $user->active = 1;

        // Assign the main network root.
        // @TODO agencies management.
        if (! $node = NetworkNode::where('code', 'PMC')->first()) {
            Log::error("PMC node not found.");

            return null;
        }
        
        $user = $this->manager->on('dorotea')->create($user, 'salesman', $node);

        Log::info("EGG User {$eggUser->utenteID} created.");

        return $user;
    }

    //
    //
    //  Users Sync.
    //
    //
    //  =====================================================================

    public function syncUsers()
    {
        DB::connection('tenant')->beginTransaction();

        $this->manager->bulkDisable('salesman');

        $eggUsers = $this->egg->loadUsers();

        $count = $this->activateUsers($eggUsers);

        DB::connection('tenant')->commit();

        if ($count > Config::get('easyprofile.tenants.dorotea.maxUsers')) {
            Log::warning("Max users exceeded: {$count} active users.");
        }

        return $count;
    }

    public function activateUsers(array $eggUsers)
    {
        foreach ($eggUsers as $eggUser) {
            try {
                if ($user = User::where('egg_id', $eggUser->utenteID)->first()) {
                    $user->forceFill(['active' => 1])->update();

                    Log::debug("User {$user->id} (EGG: {$eggUser->utenteID}) activated");
                } else {
                    $user = $this->createUserFromEgg($eggUser);
                }

            } catch (\Exception $ex) {
                Log::error($ex->getMessage());

                if (Config::get('tenants.dorotea.abortSyncUsersOnError')) {
                    Log::error("User sync aborted.");

                    break;
                }

                Log::debug("User sync resumed after error, EGG user {$eggUser->utenteID} skipped.");
            }
        }
        
        return $this->manager->countUsers();
    }
}