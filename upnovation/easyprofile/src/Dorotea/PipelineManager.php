<?php namespace Upnovation\Easyprofile\Dorotea;

use App\Models\Client;
use App\Models\Person;
use Illuminate\Support\Facades\DB;
use Upnovation\Easyprofile\PipelineManager as EasyprofilePipelineManager;

class PipelineManager 
{
    /** @var EasyprofilePipelineManager */
    protected $pipelines;

    public function __construct(EasyprofilePipelineManager $pipelines)
    {
        $this->pipelines = $pipelines;
    }

    public function start($user, $opportunityId, $eggClientId)
    {
        DB::connection('tenant')->beginTransaction();

        $pipeline = $this->pipelines->start($user);
        $pipeline->egg_opportunity_id = $opportunityId;
        $pipeline->save();

        if (! $person = Person::findIfExists('egg_client_id', $eggClientId)) {
            $person = new Person();
        }

        $person->egg_client_id = $eggClientId;
        $person->save();

        $client = new Client();

        $client->person_id = $person->id;
        $client->pipeline_id = $pipeline->id;
        $client->name = $eggClientId;
        $client->role = 'contractor';
        $client->save();

        DB::connection('tenant')->commit();

        return $pipeline;
    }
    
}