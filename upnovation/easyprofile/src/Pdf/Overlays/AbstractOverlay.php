<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use setasign\Fpdi\Fpdi;
use Upnovation\Easyprofile\Pdf\Font;

abstract class AbstractOverlay
{
    public $value; 
    public $page; 
    public $x;
    public $y;

    public $settings = [];

    //public Font $font;
    
    public function __construct($page, $x, $y)
    {
        $this->page = $page;
        $this->x = $x;
        $this->y = $y;
    }

    // Returns the string to be prinded.
    public abstract function getValue() : string;

    // Resolves the entity on which to work.
    public abstract function resolve(Pipeline $pipeline);

    public function ___setupFont(Fpdi $pdf)
    {
        if (! $this->font) {
            return;
        }

        if ($this->font->family){
            $pdf->SetFont($this->font->family);
        }
    }
}