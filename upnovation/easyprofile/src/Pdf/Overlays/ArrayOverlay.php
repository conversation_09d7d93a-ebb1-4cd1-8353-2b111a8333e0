<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use Illuminate\Support\Facades\Log;

class ArrayOverlay extends AbstractOverlay implements InjectableInterface
{
    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y);
     
        if (empty($settings) || empty($settings['key'])) {
            throw new \Exception("Settings cannot be empty for ArrayOverlay");
        }

        $this->settings = $settings;
    }

    public function inject($data)
    {
        $this->value = (array)$data;
    }


	public function getValue() : string
	{
        $key = $this->settings['key'];

        $parts = array_filter(explode('.', $key));

        if (empty($parts)) {
            Log::warning("Key {$key} is empty for ArrayOverlay");

            return "";
        }

        $value = $this->value;

        foreach ($parts as $part) {
            if (! is_array($value) || ! array_key_exists($part, $value)) {
                Log::warning("Key {$key} does not exist in value for ArrayOverlay");

                return "";
            }

            $part = trim($part);

            if (! $part || $part == "") {
                throw new \Exception("Key part is empty for ArrayOverlay");
            }
            
            $value = $value[$part];
        }

if (
    isset($this->settings['fmtCurrency']) &&
    $this->settings['fmtCurrency'] === true &&
    is_numeric($value)
) {
    return number_format((float)$value, 2, ',', '.');
}

if (
    isset($this->settings['fmtDate']) &&
    $this->settings['fmtDate'] === true &&
    is_string($value) &&
    preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)
) {
    $parts = explode('-', $value);
    if (count($parts) === 3) {
        return $parts[2] . '-' . $parts[1] . '-' . $parts[0];
    }
}

        return is_null($value) ? "" : $value;
    }

	public function resolve(Pipeline $pipeline)
    {
        // Do nothing. Work with injected data.
    }

}