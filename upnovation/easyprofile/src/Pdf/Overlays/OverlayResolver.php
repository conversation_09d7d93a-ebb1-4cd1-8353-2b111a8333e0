<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Address;
use App\Models\Pipeline;
use Exception;

class OverlayResolver
{
    /**
     * Data that can be injected into overlays.
     */
    protected $data;

    public function __construct($data = null)
    {
        $this->data = $data;
    }

    public function resolve(array $overlays, Pipeline $pipeline)
    {
        $resolvedOverlays = [];

        foreach ($overlays as $overlay) {
            if ($this->data && $overlay instanceof InjectableInterface) {
                $overlay->inject($this->data);
            }
            
            $overlay->resolve($pipeline);
        }

        return $resolvedOverlays;
    }
}