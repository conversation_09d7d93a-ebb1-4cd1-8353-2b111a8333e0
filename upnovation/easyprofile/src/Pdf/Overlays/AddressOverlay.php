<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use Upnovation\Easyprofile\Pdf\Overlays\SubjectOverlay;

class AddressOverlay extends SubjectOverlay
{
    public function __construct($page, $x, $y, array $settings)
    {
        if (! empty($settings['type']) ) {
            $settings['type'] = trim($settings['type']);
        }

        parent::__construct($page, $x, $y, $settings);

        $this->settings = $settings;
    }

    public function resolve(Pipeline $pipeline)
    {
        $subject = parent::resolve($pipeline);

        if (! empty($this->settings['type'])) {
            return $this->value = $subject->getAddress($this->settings['type']);
        }

        return $this->value = $subject->getSubjectAddress();
    }
}

