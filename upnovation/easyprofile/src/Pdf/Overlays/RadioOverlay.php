<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

class RadioOverlay extends ArrayOverlay
{

    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y, $settings);

        if (empty($settings['options'])) {
            throw new \Exception("Options cannot be empty for RadioOverlay");
        }

        $this->settings['options'] = $settings['options'];
    }

    public function getValue() : string
    {
        // Invoke parent just to assert the conditions are met.
        parent::getValue();

        if (! isset($this->value[ $this->settings['key'] ])) {
            throw new \Exception("Invalid key for RadioOverlay");
        }

        $optionKey = $this->value[ $this->settings['key'] ];

        /** @var AbstractOverlay */
        $option = $this->settings['options'][$optionKey];

        // Override main overlay coordinates with the option's coordinates.
        $this->page = $option->page;
        $this->x = $option->x;
        $this->y = $option->y;

        return $option->getValue();
    }

}