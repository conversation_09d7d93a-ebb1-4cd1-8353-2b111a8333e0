<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;
use setasign\Fpdi\Fpdi;

class TextOverlay extends AbstractOverlay
{
    public function __construct($page, $x, $y, $settings = [], $value)
    {
        parent::__construct($page, $x, $y);
        
        $this->value = $value;
    }

    public function getValue() : string
    {
        if (is_null($this->value)) {
            return '';
        }

        return $this->value;
    }

    public function resolve(Pipeline $pipeline)
    {
        // Do nothing.
    }
}