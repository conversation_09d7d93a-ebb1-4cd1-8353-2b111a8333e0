<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use Exception;
use Illuminate\Support\Facades\Log;
use setasign\Fpdi\Fpdi;

abstract class ObjectOverlay extends AbstractOverlay
{
    public function __construct($page, $x, $y, array $settings)
    {
        if (empty($settings)) {
            throw new Exception('ObjectOverlay: Settings array cannot be empty.');
        }

        if (empty($settings['properties']) && empty($settings['method'])) {
            throw new Exception('ObjectOverlay: properties and method cannot be empty.');
        }

        parent::__construct($page, $x, $y);

        $this->settings = $settings;
    }

    public function getValue() : string
    {
        if (is_null($this->value)) {
            Log::warning('ObjectOverlay: Value is null, returning empty string.');
            
            return '';
        }

        if (isset($this->settings['method']) && method_exists($this->value, $this->settings['method'])) {
            return (string)$this->value->{$this->settings['method']}();
        }

        foreach ($this->settings['properties'] as $property) {
            if (! isset($this->value->$property)) {
                throw new Exception("Property '$property' does not exist on object.");
            }
        }

        $self = $this;

        return join(
            $this->settings['separator'] ?? ' ',
            array_map(function ($property) use ($self) {
                return $self->value->{$property};
            }, $this->settings['properties'])
        );
    }
    
}