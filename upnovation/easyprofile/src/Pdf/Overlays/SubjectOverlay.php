<?php namespace Upnovation\Easyprofile\Pdf\Overlays;

use App\Models\Pipeline;

class SubjectOverlay extends ObjectOverlay
{
    /**
     * Constructor for SubjectOverlay.
     * 
     * @param int $page The page number where the overlay will be placed.
     * @param int $x The x-coordinate for the overlay.
     * @param int $y The y-coordinate for the overlay.
     * @param array $settings The settings for the overlay.
     * 
     * Example settings:
     * 'conditionalRole' => [
     *       'field' => 'type', // Field checked in the pipeline.
     *       'options' => [
     *           'legal' => 'contractor',   // If type is 'legal', resolve with 'contractor'.
     *           'individual' => null,      // If null, return empty string in case of individual.
     *       ],
     *   ],
     * @throws \Exception If the neither role nor conditionalRole is set.
     */
    public function __construct($page, $x, $y, array $settings)
    {
        parent::__construct($page, $x, $y, $settings);

        if (empty($settings['role']) && empty($settings['conditionalRole'])) {
            throw new \Exception('SubjectOverlay requires a role setting');
        }
    }

    public function resolve(Pipeline $pipeline)
    {
        if (isset($this->settings['conditionalRole']) && $this->settings['conditionalRole']) {
            return $this->resolveConditional($pipeline);
        }

        return $this->resolveRole($pipeline);
    }

    public function resolveRole(Pipeline $pipeline)
    {
        if ($this->settings['role'] == 'rep') {
            if (! $contractor = $pipeline->getContractor()) {
                throw new \Exception("No contractor found in pipeline.");
            }

            if ($pipeline->type != 'legal') {
                throw new \Exception("SubjectOverlay with role 'rep' can only be used in legal pipelines.");
            }

            return $this->value = $contractor->enterprise->rep;
        }

        // Conditional role might force a null value, used
        // to print empty strings.
        if (! $this->settings['role']) {
            return $this->value = null;
        }

        if (! $result = $pipeline->getSubjects($this->settings['role'])) {
            throw new \Exception("No subjects found for role '{$this->settings['role']}'");
        }

        return $this->value = $result[0];
    }

    public function resolveConditional(Pipeline $pipeline)
    {
        if (! isset($this->settings['conditionalRole']['field']) || ! isset($this->settings['conditionalRole']['options'])) {
            throw new \Exception('SubjectOverlay: conditionalRole settings must contain field and options.');
        }

        $field = $this->settings['conditionalRole']['field'];

        if (! isset($pipeline->{$field})) {
            throw new \Exception("Field '$field' does not exist on pipeline.");
        }

        foreach ($this->settings['conditionalRole']['options'] as $value => $role) {
            

            if ($pipeline->{$field} == $value) {
                $this->settings['role'] = $role;
                
                return $this->resolveRole($pipeline);
            }
        }

        return null;
    }
}