<?php namespace Upnovation\Easyprofile\Pdf;

use App\Models\Document;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use Illuminate\Support\Facades\Storage;
use setasign\Fpdi\Fpdi;

class PdfProcessorDN implements PdfProcessorInterface
{
    // @todo in parent class
    protected $data;

    public function setCustomData($data)
    {
        $this->data = $data;
    }

    public function compile(Document $document, Pipeline $pipeline, File $file) : File
    {
        if (! $pipeline->form) {
            throw new \Exception("Pipeline {$pipeline->id} does not have a form associated.");
        }

        if (! isset($pipeline->form['cachedResult'])) {
            throw new \Exception("Pipeline {$pipeline->id} does not have cached form results.");
        }

        if (! isset($pipeline->form['cachedResult']['sections'])) {
            throw new \Exception("Pipeline {$pipeline->id} does not have cached form sections.");
        }



        $pdf = new Fpdi();
        $pdf->setSourceFile($document->template->getFullPath());
        $templateId = $pdf->importPage(1);
        $size = $pdf->getTemplateSize($templateId);
        $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
        $pdf->useTemplate($templateId);

        $pdf->SetFont('Helvetica');
        $pdf->SetFontSize(11);
        $pdf->SetTextColor(0, 0, 0);

        $pdf->setXY(20, 30);
        $pdf->Write(0, "QUESTIONARIO DI VALUTAZIONE DELLE ESIGENZE E DEI BISOGNI DEL CLIENTE");

        $pdf->setXY(20, 40);
        $pdf->SetFont('Helvetica', 'B');
        $pdf->Write(0, "Dati Contraente");
        $pdf->SetFont('Helvetica', '');
        

        $contractor = $pipeline->getSubjects('contractor')[0];
        $pdf->SetFont('Helvetica', 'I');
        if ($contractor instanceof Person) {
            $pdf->setXY(20, 44);
            $pdf->Write(0, $contractor->name . ' ' . $contractor->lastname);
            $pdf->setXY(20, 48);
            $pdf->Write(0, "CF: " . strtoupper($contractor->taxCode));
        }

        if ($contractor instanceof Enterprise) {
            $pdf->setXY(20, 44);
            $pdf->Write(0, $contractor->name . ' ' . $contractor->legalForm);
            $pdf->setXY(20, 48);
            $pdf->Write(0, "P.IVA: " . strtoupper($contractor->vat));
        }

        $pdf->SetFont('Helvetica', '');

        $maxY = $size['height'] - 40; // margine inferiore
        $y = 60;
        $yReset = 40;
        
        $maxWidth = 170; // larghezza massima in mm (regola secondo il tuo layout)

        foreach ($pipeline->form['cachedResult']['sections'] as $section) {
            if ($y > $maxY) {
                $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                $pdf->useTemplate($templateId);
                $y = $yReset;
            }
            $pdf->setXY(20, $y);
            $pdf->SetFont('Helvetica', 'B');
            $sectionTitle = mb_convert_encoding($section['title'], 'UTF-8', 'UTF-8');
            if ($pdf->GetStringWidth($sectionTitle) > $maxWidth) {
                $pdf->MultiCell($maxWidth, 8, $sectionTitle);
                $y = $pdf->GetY();
            } else {
                $pdf->Write(0, $sectionTitle);
                $y += 8;
            }
            $pdf->SetFont('Helvetica', '');

            foreach ($section['qa'] as $qa) {
                if ($y > $maxY) {
                    $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                    $pdf->useTemplate($templateId);
                    $y = $yReset;
                }
                $pdf->setXY(20, $y);
                $question = mb_convert_encoding($qa['q'], 'UTF-8', 'UTF-8');
                if ($pdf->GetStringWidth($question) > $maxWidth) {
                    $pdf->MultiCell($maxWidth, 7, $question);
                    $y = $pdf->GetY();
                } else {
                    $pdf->Write(0, $question);
                    $y += 7;
                }

                $answers = is_array($qa['a']) ? $qa['a'] : [$qa['a']];
                foreach ($answers as $answer) {
                    if ($y > $maxY) {
                        $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
                        $pdf->useTemplate($templateId);
                        $y = $yReset;
                    }
                    $pdf->setXY(20, $y);
                    $pdf->SetFont('Helvetica', 'I');
                    if (isset($section['id']) && $section['id'] == 4) {
                        $splitAnswers = array_map('trim', explode(',', $answer));
                        foreach ($splitAnswers as $splitAnswer) {
                            $splitAnswer = mb_convert_encoding($splitAnswer, 'UTF-8', 'UTF-8');
                            if ($pdf->GetStringWidth($splitAnswer) > $maxWidth) {
                                $pdf->MultiCell($maxWidth, 7, $splitAnswer);
                                $y = $pdf->GetY();
                            } else {
                                $pdf->Write(0, $splitAnswer);
                                $y += 7;
                            }
                            $pdf->setXY(20, $y);
                        }
                    } else {
                        $answer = mb_convert_encoding($answer, 'UTF-8', 'UTF-8');
                        if ($pdf->GetStringWidth($answer) > $maxWidth) {
                            $pdf->MultiCell($maxWidth, 7, $answer);
                            $y = $pdf->GetY();
                        } else {
                            $pdf->Write(0, $answer);
                            $y += 7;
                        }
                    }
                    $pdf->SetFont('Helvetica', '');
                }
            }
            $y += 10; // spazio extra dopo ogni sezione
        }

        $pdf->Output('F', $file->getFullPath());

        return $file;
    }
}