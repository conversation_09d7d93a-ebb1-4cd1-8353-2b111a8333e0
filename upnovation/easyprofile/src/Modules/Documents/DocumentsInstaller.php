<?php namespace Upnovation\Easyprofile\Modules\Documents;

use App\Models\Document;
use App\Models\File;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DocumentsInstaller
{
    public function makeDocument($documentConfig)
    {
        $document = new Document();

        $document->overlayArray = $documentConfig['overlayArray'] ?? [];

        $document->forceFill([
            'node_id' => $documentConfig['node_id'],
            'title' => $documentConfig['title'],
            'type' => $documentConfig['type'],
            'version' => $documentConfig['version'],
            'description' => $documentConfig['description'],
            'processor' => $documentConfig['processor'] ?? null,
            'signers' => $documentConfig['signers'] ?? null,
            'signatures' => $documentConfig['signatures'] ?? null,
        ]);

        return $document;
    }

    public function install(string $code, string $tenant)
    {
        $configKey = "tenants.{$tenant}.documents";

        if (! config($configKey)) {
            Log::warning("Documents configuration not found for {$tenant}.");

            return null;
        }

        if (! $config = config("{$configKey}.{$code}")) {
            Log::warning("Document configuration for {$code} not found.");

            return null;
        }

        if (! file_exists($config['file']->getFullPath())) {
            Log::warning("Document file not found in tenant: " . $config['file']->getFullPath());

            return null;
        }

        DB::connection('tenant')->beginTransaction();

        $productForm = $this->installDocument($config);

        DB::connection('tenant')->commit();

        return $productForm;
    }

    public function installDocument($config)
    {
        $document = $this->makeDocument($config['document']);

        $document->save();

        // For some reason, if i try to save $config['file'] directly, the boot:saving event is not called.
        $file = new File([
            'disk' => $config['file']->disk,
            'path' => $config['file']->path,
            'filename' => $config['file']->filename,
            'type' => $config['file']->type,
        ]);

        $file->document_id = $document->id;
        $file->configName = $config['code'];

        $file->save();

        return $file;
    }

    public function installPolicy(string $code, string $tenant)
    {
        $configKey = "tenants.{$tenant}.documents";

        if (! config($configKey)) {
            Log::warning("Policy documents configuration not found for {$tenant}.");

            return null;
        }

        if (! $config = config("{$configKey}.{$code}")) {
            Log::warning("Document configuration for {$code} not found.");

            return null;
        }

        if (! empty($config['policy'])) {
            $policyConfig = [
                'node_id' => $config['document']['node_id'],
                'title' => $config['policy']['title'],
                'type' => 'product-policy',
                'version' => $config['document']['version'],
                'description' => $config['policy']['description'] ?? "Documentazione da firmare",
                'signers' => $config['policy']['signers'],
                'signatures' => $config['policy']['signatures'],
            ];

            $policyDocument = $this->makeDocument($policyConfig);

            $policyDocument->save();

            return $policyDocument;
        }

        return null;
    }
}
