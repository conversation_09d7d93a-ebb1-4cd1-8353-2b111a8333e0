<?php namespace Upnovation\Easyprofile;

use App\Models\Pipeline;
use App\Models\Task;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class PipelineManager
{
    /**
     * Get tasks configuration.
     *
     * @param string $tenant
     * @return array
     */
    protected function getTasks(string $tenant) : array 
    {
        if (! $tasks = config("tenants.{$tenant}.pipeline.tasks")) {
            throw new Exception("Pipeline configuration not found for tenant: {$tenant}");
        }

        return $tasks;
    }

    /**
     * Starts a new pipeline.
     *
     * @param User $user
     * @return Pipeline
     */
    public function start(User $user) : Pipeline
    {
        // Get tenant.
        if (! $tenant = app('currentTenant')->name) {
            throw new Exception('Tenant is null.');
        }

        // Get tasks from configuration.
        $config = $this->getTasks($tenant);

        // Create entries.
        DB::connection('tenant')->beginTransaction();

        $pipeline = Pipeline::create([
            'user_id' => $user->id,
        ]);

        foreach($config as $priority => $task) {
             $pipeline->tasks()->create([
                'priority' => $priority,
                'type' => $task['type'],
                'template' => $task['template'],
                'manager' => $task['manager'],
                'controller' => $task['controller'],
                'accessible' => $task['dependson'] != null,
                'dependson' => $task['dependson'],
                'navigation' => $task['navigation'],
                'displayName' => $task['name'] ?? __('easyprofile.tasks.' . $task['type']),
                'config' => $task['config'] ?? [],
                'data' => [],
            ]);
        }

        $this->next($pipeline);

        DB::connection('tenant')->commit();

        return $pipeline;
    }

    /**
     * Resume pipeline at current Task.
     *
     * @param Pipeline $pipeline
     * @return Task
     */
    public function resume(Pipeline $pipeline) : Task|null
    {
        foreach($pipeline->prioritizedTasks() as $task) {
            if ($this->isCurrent($task)) {
                return $task;
            }
        }

        Log::debug("There is no active task.");
        
        return null;
    }

    /**
     * Check if a task is the current one.
     *
     * @param Task $task
     * @return boolean
     */
    public function isCurrent(Task $task) : bool 
    {
        return $task->state == 'progress';
    }

    /**
     * Completes current task and set the next as the current one.
     * If there's not more tasks, pipeline is saved as closed.
     *
     * @param Pipeline $pipeline
     * @return Task
     */
    public function next(Pipeline $pipeline) : Task|null
    {
        $tasks = $pipeline->prioritizedTasks();

        $currentTask = null;
        
        foreach($tasks as $pos => $task) {
            if ($this->isCurrent($task)) {
                /** @var TaskInterface */
                $manager = app()->make($task->manager);

                $currentTask = $manager->finalize($task);
                $currentTask->accessible = $this->getAccessible($task, $pipeline->state);
                $currentTask->state = 'closed';

                break;
            }
        }

        // No task found in previous cycle, we assume pipeline is starting now.
        if (! $currentTask) {
            $pos = -1;
        }

        // If there's no next state, we assume pipeline is done.
        if (! isset($tasks[$pos+1])) {
            $this->close($pipeline);

            return null;
        }

        /** @var TaskInterface */
        $manager = app()->make($tasks[$pos+1]->manager);

        $tasks[$pos+1] = $manager->initialize($tasks[$pos+1], $pos >= 0 ? $tasks[$pos] : null);
        $tasks[$pos+1]->state = 'progress';
        $tasks[$pos+1]->accessible = true;

        $pipeline->tasks()->saveMany($tasks);

        return $tasks[$pos+1];
    }

    public function previous(Pipeline $pipeline)
    {
        Log::debug("Rewinding pipeline to previous task.");

        $tasks = $pipeline->prioritizedTasks();

        $currentTask = $pipeline->currentTask();

        Log::debug("Current task: {$currentTask->type} with priority: {$currentTask->priority}");

        $targetTask = null;

        // Rewind to the first accessible task.
        // reverse cycle
        for ($i = count($tasks) - 1; $i >= 0; $i--) {
            if ($tasks[$i]->priority >= $currentTask->priority) {
                continue;
            }

            Log::debug("Checking task: {$tasks[$i]->type} with priority: {$tasks[$i]->priority}");

            if ($tasks[$i]->accessible) {
                $targetTask = $tasks[$i];
                break;
            }
        }

        return $targetTask;
    }

    /**
     * Load requested task, if accessible. Only supports rewind mode.
     *
     * @param Pipeline $pipeline
     * @param Task $task
     * @return void
     */
    public function goto(Pipeline $pipeline, Task $targetTask)
    {
        // Bye.
        if (! $targetTask->accessible) {
            return null;
        }

        // We are targeting a task that comes after the current one,
        // fast-forward goto is not allowed.
        if ($targetTask->priority > $pipeline->currentTask()->priority) {
            return null;
        }

        // Task is not accessible due to blacklist rules.
        if (! $accessibilityByBlacklist = $this->getAccessibilityByBlacklist($targetTask, $pipeline->currentTask())) {
            Log::debug("Task is not accessible for blacklist rules.");

            return null;
        }

        foreach($pipeline->prioritizedTasks() as $task) {
            if ($task->priority > $targetTask->priority) {
                $task->state = 'open';

                $task->accessible = $this->getAccessible($task, $pipeline->state) && $accessibilityByBlacklist;

                $task->save();
            }
        }

        /** @var TaskInterface */
        $manager = app()->make($targetTask->manager);

        $targetTask->state = 'progress';
        $targetTask = $manager->initialize($targetTask);
        $targetTask->save();

        return $targetTask;
    }

    public function skip(Pipeline $pipeline, Task $targetTask, ?array $data = null)
    {
        $currentTask = $pipeline->currentTask();

        if (! isset($currentTask->config['canSkipTo'])) {
            Log::debug("Current task doesn't support skipping.");

            return null;
        }

        if (! $skipTo = $currentTask->config['canSkipTo']) {
            Log::debug("Current task donesn't support skipping.");

            return null;
        }

        if ($currentTask->priority >= $targetTask->priority) {
            Log::debug("Target task is not after current task, skipping.");

            return null;
        }

        if ($targetTask->type != $skipTo) {
            Log::debug("Cannot jump from this task to {$targetTask->type}");

            return null;
        }

        $tasks = $pipeline->prioritizedTasks();

        foreach($tasks as $task) {
            // Skip tasks that are before the current one.
            if ($task->priority < $currentTask->priority) {
                continue;
            }

            // Break on tasks that are after the target one.
            if ($task->priority > $targetTask->priority) {
                break;
            }

            /** @var TaskInterface $manager */
            $manager = app()->make($task->manager);

            // Pass skip generated data to next manager.
            $data = $manager->skip($task, $targetTask, $data);

            $task->accessible = $this->getAccessible($task, $pipeline->state);

            if ($task == $targetTask) {
                $task->state = 'progress';
                $manager->initialize($task, $currentTask);
            } else {
                $task->state = 'closed';
            }
        }

        $pipeline->tasks()->saveMany($tasks);

        return $targetTask;
    }

    public function close(Pipeline $pipeline)
    {
        DB::connection('tenant')->beginTransaction();

        $pipeline->state = 'closed';
        $pipeline->closed_at = Carbon::now();
        $pipeline->save();

        foreach ($pipeline->tasks as $task) {
            $task->accessible = $this->getAccessible($task, $pipeline->state);
            $task->state = 'closed';
            $task->save();
        }

        DB::connection('tenant')->commit();

        return $pipeline;
    }

    /**
     * Delete the pipeline.
     *
     * @param Pipeline $pipeline
     * @return void
     */
    public function delete(Pipeline $pipeline)
    {
        return $pipeline->delete();
    }

    /**
     * It determines whether a task should be considered accessible or not,
     * according to task configuration.
     * 
     * pipeline.open => task is always accessible, unless it depends on other tasks (pipeline must be opened).
     * pipeline.closed => as above, but pipeline must be closed.
     * always => always accesible, unless it depends on other tasks
     * never => never
     *
     * @param Task $task
     * @param string $pipelineState
     * @return void
     */
    public function getAccessible(Task $task, string $pipelineState) : bool
    {
        $states = explode("|", $task->navigation);

        if (in_array('never', $states)) {
            return false;
        }

        if (in_array('always', $states)) {
            return ! $task->dependson;
        }

        // Split by the dot => "pipeline.open" becomes just "open".
        $states = array_map(function($item){
            $values =  explode(".", $item);

            if (count($values) != 2) {
                return null;
            }

            return $values[1];
        }, $states);

        // Remove null values.
        $states = array_filter($states);

        return in_array($pipelineState, $states) && ! $task->dependson;
    }

    public function getAccessibilityByBlacklist(Task $targetTask, Task $currentTask) : bool
    {
        $manager = app()->make($currentTask->manager);

        return $manager->isAccessible($targetTask, $currentTask);
    }
}
