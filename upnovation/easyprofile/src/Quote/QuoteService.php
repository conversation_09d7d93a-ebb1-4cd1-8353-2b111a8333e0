<?php namespace Upnovation\Easyprofile\Quote;

use Illuminate\Support\Facades\Log;

class QuoteService
{
    protected string $token;

    protected array $settings = [
        'CFMUTUIEVOL' => [
            'endpoint' => 'mutuo_evolution.php',
            'parameters' => ['combinazione', 'capitale', 'durata'],
        ],
    ];

    public function supports(string $product): bool
    {
        return isset($this->settings[$product]);
    }

    public function quote(string $product, array $parameters): array
    {
        if (! $this->supports($product)) {
            throw new QuoteServiceException("Product {$product} not supported");
        }

        if (! isset($this->settings[$product]['endpoint'])) {
            throw new QuoteServiceException("Endpoint for product {$product} not defined");
        }

        if (! $endpoint = $this->settings[$product]['endpoint']) {
            throw new QuoteServiceException("Endpoint for product {$product} not defined");
        }

        // Check parameters has the keys defined in settings
        foreach ($this->settings[$product]['parameters'] as $key) {
            if (! array_key_exists($key, $parameters)) {
                throw new QuoteServiceException("Missing parameter: {$key} for product {$product}");
            }
        }

        return $this->call(
            $endpoint, 
            $parameters, 
            $this->getToken()
        );
    }

    public function call($endpoint, array $parameters, ?string $token = null)
    {
        // curl to the API
        $url = env('QUOTE_API_URL') . "/{$endpoint}";

        Log::debug("Calling API: {$url} with token: $token");

        $ch = curl_init($url);

        if ($token) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Token: Bearer ' . $token,
            ]);
        }

        // set post 
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new QuoteServiceException("Error calling API: {$httpCode} - {$response}", $httpCode);
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("JSON decode error: " . json_last_error_msg());
        }

        return $data;
    }

    public function getToken()
    {
        $response = $this->call(
            'auth.php',
            [
                'email' => env('QUOTE_API_USER'),
                'password' => env('QUOTE_API_PASSWORD'),
            ]
        );

        if (! $response || ! isset($response['token'])) {
            throw new QuoteServiceException("Failed to retrieve token");
        }

        return $this->token = $response['token'];
    }

    public function getCachedToken()
    {
        return $this->token;
    }
 
}