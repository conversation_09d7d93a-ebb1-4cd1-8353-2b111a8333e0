<?php namespace Upnovation\Easyprofile;

use Illuminate\Support\Facades\Log;

class EasyProfile
{
    public static function isModuleEnabled(string $tenant, string $module) : bool
    {
        if (! $config = config("easyprofile.modules.{$module}")) {
            Log::error("Module {$module} not found in easyprofile configuration.");

            return false;
        }

        $tenantsArray = explode(',', $config['enabledTenants']);
        
        return in_array($tenant, $tenantsArray);
    }
}