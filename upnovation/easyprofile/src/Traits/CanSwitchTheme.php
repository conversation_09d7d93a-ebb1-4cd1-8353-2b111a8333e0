<?php namespace Upnovation\Easyprofile\Traits;

trait CanSwitchTheme
{
    function getView(string $tenant, string $view): string
    {
        if (! $config = config("easyprofile.templateOverrides.{$tenant}")) {
            return $view;
        }
    
        if (! isset($config[$view])) {
            return $view;
        }
    
        if (! $override = $config[$view]) {
            return $view;
        }
    
        return $override;
    }

    function getUIConfig(string $tenant)
    {
        // Load default config.
        $ui = config("easyprofile.tenants.default.ui");

        // Search for tenant config.
        if ($config = config("easyprofile.tenants.{$tenant}")) {
            $tenantUiConfig = $config['ui'] ?? [];
        }

        // If tenant config is found, overwrite its values in default config.
        foreach($tenantUiConfig ?? [] as $key => $value) {
            $ui[$key] = $value;
        }

        return $ui;
    }
}