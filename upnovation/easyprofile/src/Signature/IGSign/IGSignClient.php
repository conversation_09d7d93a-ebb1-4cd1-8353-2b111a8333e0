<?php namespace Upnovation\Easyprofile\Signature\IGSign;

use App\Models\Person;
use CURLFile;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class IGSignClient
{
    public static $SIGN_TYPE_CERTIFICATE = 0;
    public static $SIGN_TYPE_SIMPLE = 1;

    public static $AUTH_TYPE_SMS = 1;
    public static $AUTH_TYPE_EMAIL = 2;

    private string $baseUrl;
    private string $token;

    protected $signType;
    protected $authType;

    public function __construct(string $baseUrl, string $jwtToken)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->token = $jwtToken;
    }

    public function setSignType(int $signType)
    {
        if (! in_array($signType, [self::$SIGN_TYPE_CERTIFICATE, self::$SIGN_TYPE_SIMPLE])) {
            throw new Exception("Invalid sign type: {$signType}");
        }

        $this->signType = $signType;

        return $this;
    }
    
    public function setAuthType(int $authType)
    {
        if (! in_array($authType, [self::$AUTH_TYPE_SMS, self::$AUTH_TYPE_EMAIL])) {
            throw new Exception("Invalid auth type: {$authType}");
        }

        $this->authType = $authType;

        return $this;
    }

    public function request(string $method, string $endpoint, array $data = [], array $headers = [], $isMultipart = false)
    {
        $url = "{$this->baseUrl}{$endpoint}";
        $ch = curl_init($url);

        $defaultHeaders = [
            "Authorization: Bearer {$this->token}"
        ];

        if (!$isMultipart) {
            $headers = array_merge($defaultHeaders, ['Content-Type: application/json'], $headers);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        } else {
            $headers = array_merge($defaultHeaders, $headers);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }

        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, strtoupper($method));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        Log::debug("IGSIGN request: {$method} {$url}");
        Log::debug("IGSIGN request headers: " . json_encode($headers) . " and data: " . json_encode($data));

        $response = curl_exec($ch);
        if (!$response) throw new Exception(curl_error($ch));
        curl_close($ch);

        return json_decode($response, true);
    }

    public function createFolder(string $name, string $description, ?string $expiresAt = null): object
    {
        $data = [
            'name' => $name,
            'description' => $description,
            'state' => 1, // draft
            'urgent' => 0,
            'quick_sign' => 0,
            'reminder' => $expiresAt ? true : false,
        ];

        if ($expiresAt) {
            $data['expires_at'] = $expiresAt;
        }

        try {
            $res = $this->request('POST', '/folders', $data);

            if (! isset($res['id'])) {
                throw new Exception('Folder ID is undefined: ' . json_encode($res));
            }
            
            return (object)$res;
        } catch (Exception $e) {
            // todo
            throw new Exception("Errore nella creazione del fascicolo: " . $e->getMessage());
        }
    }
    

    public function uploadDocument(int $folderId, object $document): object
    {
        if (! file_exists($document->filepath)) {
            throw new Exception("File not found: {$document->filepath}");
        }

        try {
            // Crea documento
            $doc = $this->request('POST', "/folders/{$folderId}/documents", [
                'name' => $document->name,
                //'description' => $document->name . "foobar desc",
                'visible_signature' => true,
                'mandatory_visible_signature' => true,
                'form_filling' => false,
                'timestamp' => false,
                'digital_stamp' => false,
                'signature_type' => '1' // 1 PADES (PDF), 2 FOR P7M
            ]);

            if (! is_array($doc) || ! isset($doc['id'])) {
                Log::error('IGSign: Error creating document meta: ' . json_encode($doc));

                throw new Exception('IGSign: Error creating document meta');
            }

            $docId = $doc['id'];

            // Upload file
            $uploadResponse = $this->request(
                'POST',
                "/folders/{$folderId}/documents/{$docId}/file",
                ['file' => new CURLFile($document->filepath, 'application/pdf', $document->outputFilename)],
                ['Content-Type: multipart/form-data'],
                true
            );

            if (! is_array($uploadResponse) || empty($uploadResponse['file_name'])) {
                throw new Exception("323423233: " . json_encode($uploadResponse));
            }

            return (object)$doc;
        } catch (Exception $e) {
            // @todo refactor this
            
            throw new Exception("Errore in uploadDocument(): " . $e->getMessage(), 0, $e);
        }
    }

    public function uploadAttachment(int $folderId, object $document): int
    {
        if (! file_exists($document->filepath)) {
            throw new Exception("File not found: {$document->filepath}");
        }

        try {
            $res = $this->request('POST', "/folders/{$folderId}/attachments", [
                'name' => $document->name,
                //'description' => $description,

                // @TODO check available options
                'attachment_type' => 0 
            ]);

            if (! isset($res['id'])) {
                Log::error('IGSign: Error creating attachment meta: ' . json_encode($res));

                throw new Exception('IGSign: Error creating attachment meta');
            }

            $uploadRes = $this->request(
                'POST',
                "/folders/{$folderId}/attachments/{$res['id']}/file",
                ['file' => new CURLFile($document->filepath, 'application/pdf', $document->outputFilename)],
                ['Content-Type: multipart/form-data'],
                true
            );

            if (! isset($uploadRes['file_name'])) {
                throw new Exception("Errore nel caricamento dell'allegato: " . json_encode($uploadRes));
            }

            return $res['id'];

        } catch (Exception $e) {
            // @todo refactor this
            throw new Exception("Errore in uploadAttachment(): " . $e->getMessage(), 0, $e);
        }
    }

    public function addVisibleSignature(int $folderId, int $documentId, int $userId, int $page = 1, int $x = 30, int $y = 30, int $cx = 300, int $cy = 65): void
    {
        try {
            $this->request(
                'POST',
                "/folders/{$folderId}/documents/{$documentId}/visible-signatures/{$userId}",
                [
                    'page' => $page,
                    'x' => $x,
                    'y' => $y,
                    'cx' => $cx,
                    'cy' => $cy
                ]
            );
        } catch (Exception $e) {
            throw new Exception("Errore nell'aggiunta della firma visibile: " . $e->getMessage(), 0, $e);
        }
    }


    public function addSimpleSignatureStep(int $folderId, array $docIds, Person $person)
    {

$email = $person->email;
$name = $person->name;
$surname = $person->lastname;
$phone = $person->phone;

// Crea step
// @FIXME: label compare in frontend igisign
$step = $this->request('POST', "/folders/{$folderId}/steps", [
    'label' => 'Firma',
    'order' => 1
]);

        $stepId = $step['id'];

        // Crea action con firma semplice SENZA OTP
        return (object)$this->request('POST', "/folders/{$folderId}/steps/{$stepId}/actions", [
            'role' => 2,
            'sign_type' => $this->signType, // 0=self certificate (i.e. fea), 1=simple

            // @check nel caso di firma con certiticato short term l'autenticazione deve essere per forza sms (me lo hanno detto in call ma non so se è vero)
            'auth_type' => $this->authType, // 1=sms, 2=email

            'isNewUser' => true,
            'new_user' => [
                'email' => $email,
                'phone_number' => $phone,
                'name' => $name,
                'surname' => $surname,
                'locale' => 'it'
            ],

            // @fixme: sto creando uno step per ogni documento
            'documents' => $docIds
        ]);
    }

    public function startFolder(int $folderId)
    {
        return $this->request('PUT', "/folders/{$folderId}", ['state' => 2]);
    }

    public function downloadSignedFolder(int $folderId)
    {
        $url = "{$this->baseUrl}/folders/{$folderId}/archive?type=zip";

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            "Authorization: Bearer {$this->token}"
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FAILONERROR, true);

        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("Errore cURL nella richiesta di download: $error");
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("Download fallito: codice HTTP {$httpCode}. Il fascicolo potrebbe non essere completato.");
        }

        if (!$data || strlen($data) < 100) {
            throw new Exception("Contenuto ZIP vuoto o non valido.");
        }
return $data;




        // Scrive il file usando Laravel Storage (es: 'signed/folder123.zip')
        if (! Storage::disk('private')->put($relativePath, $data)) {
            throw new Exception("Errore nel salvataggio del file ZIP in Storage: {$relativePath}");
        }
    }

    /**
     * 
     *   1 → FOLDER_CREATE - Fired whenever a folder is created.
     *   2 → FOLDER_UPDATE - Fired whenever a folder is updated.
     *   3 → STEP_CREATE - Fired when a workflow step is created
     *   4 → STEP_COMPLETED - Fired when a step is completed
     *   5 → FILE_DOWNLOADED - Fired when a document is downloaded
     *   6 → QUICK_SIGN - Fired on quicksign signature
     *   7 → DOCUMENT_CREATE - Fired when a folder document is created
     *   8 → ACTION_CREATE - Fired whenever a signature or approval task is created
     *   9 → ACTION_DELETE - Fired whenever a signature or approval task is removed
     *   10 → ACTION_SIGN - Fired whenever a signature, approval or refusal occurs
     *   11 → FOLDER_COMPLETE - Fired when all signature / approval actions inside a folder have been successfully completed
     *   12 → FOLDER_START - Fired when a folder starts
     *   13 → FOLDER_ABORT - Fired when a folder is aborted
     *   14 → FOLDER_EXPORT - Fired whenever a folder was exported
     *   15 → REPORT_READY - Fired whenever a folder is completed, and the Report is ready
     *   16 → DOCUMENT_FILLED - Fired whenever a document is filled
     *   17 → ACTION_ERROR - Fired when an action is not completed due to error during process
     */
    public function registerCallback(int $folderId, string $callbackUrl, $event): array
    {
        return $this->request('POST', "/folders/{$folderId}/callbacks", [
            'url' => $callbackUrl,
            "event_type" => $event,
        ]);
    }

    /**
     * 1 → FOLDER DRAFT: quando non ancora avviato il workflow approvativo
     * 2 → FOLDER RUNNING: quando il workflow approvativo è in corso
     * 3 → FOLDER ABORTED: quando l'owner abortisce il flusso approvativo di un folder
     * 4 → FOLDER DONE: quando tutti gli attori hanno completato il workflow approvativo con esito positivo; se uno step viene rifiutato, lo stato del folder viene mantenuto a RUNNING.
     * 5 → FOLDER DELETED: quando l'owner cancella un fascicolo
     * 6 → FOLDER TEMPLATE: quando il fascicolo definisce un template
     */
    public function getFolderStatus(int $folderId)
    {
        return $this->request('GET', "/folders/{$folderId}");
    }

}
