<?php namespace Upnovation\Easyprofile\Signature\IGSign;

use App\Models\Address;
use App\Models\Person;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;

class Time4MindClient
{
    private string $raoUsername;
    private string $certPath;
    private string $keyPath;
    private string $caPath;

    protected $providerName;
    protected $registrationOffice;
    protected $language;

    // Mappa metodi → endpoint



    // @todo configure staging/production endpoints




    private array $endpoints = [
        'default' => 'https://servicesbe.test4mind.com/Time4UserServices/services/backend/ra/t4ujson',
        'listCredentials' => 'https://servicesbe.test4mind.com/Time4UserServices/services/backend/t4ujson',
        'pushOTP' => 'https://servicesbe.test4mind.com/Time4UserServices/services/backend/t4ujson',
    ];

    public function __construct(
        string $raoUsername,
        string $certPath,
        string $keyPath,
        string $caPath = ''
    ) {
        $this->raoUsername = $raoUsername;
        $this->certPath = $certPath;
        $this->keyPath = $keyPath;
        $this->caPath = $caPath;

        $this->providerName = env('TIME4MIND_PROVIDER_NAME');
        $this->registrationOffice = env('TIME4MIND_REGISTRATION_OFFICE');
        $this->language = 'IT';
    }

    public function isCertificateActive(array $credentials): bool
    {
        if (! isset($credentials['certificateStatus']) || $credentials['certificateStatus'] !== 'ATTIVO') {
            Log::debug("TIME4MIND Credentials non active" );
            return false;
        }

        if (! isset($credentials['validFrom']) || ! isset($credentials['validTo'])) {
            Log::debug("TIME4MIND validity not set");
            return false;
        }

        $now = Carbon::now();
        $from = Carbon::createFromTimestampMs($credentials['validFrom']);
        $to = Carbon::createFromTimestampMs($credentials['validTo']);

        return $now->between($from, $to);
    }

    public function issueCertificate(Person $person)
    {
        Log::debug("issueCertificate for Person ID: {$person->id}");
       
        if ($credentials = $this->listCredentials($person->email)) {
            Log::debug("Found certificate credentials for Person ID: {$person->id}");

            // Log::debug("Credentials: " . json_encode($credentials));

            // Return if we find at least one active certificate
            foreach( $credentials as $credential) {
                if ($this->isCertificateActive($credential)) {
                    Log::debug("Found active certificate for Person ID: {$person->id}");

                    return $credential;
                }

                if (! isset($credential['cid'])) {
                    Log::warning("No certificate ID found for Person ID: {$person->id}");

                    continue;
                }

                Log::debug("Certificate {$credential['cid']} expired or inactive for Person ID: {$person->id}");
            }
        }

        try {
            $registrationResp = $this->addRegistration(
                $this->mapPerson($person), 
                $this->providerName, 
                $this->registrationOffice, 
                $this->language
            );

            $registrationId = $registrationResp['registrationId'];
            //echo "🆗 Registrazione avviata. ID: $registrationId\n";

            $completeResp = $this->completeRegistration($registrationId, 'sms', [
                ['key' => 'mobileNumber', 'value' => $person->phone]
            ]);

            Log::info("Certificate registration completed for Person ID: {$person->id}, Registration ID: $registrationId");

            return $this->approveRegistration($registrationId);
        } catch (Exception $e) {
            Log::error("Error issuing certificate for Person ID: {$person->id}: " . $e->getMessage());

            throw $e;
        }
    }

    private function sendRequest(string $method, array $params): array
    {
        $payload = [
            'jsonrpc' => '2.0',
            'id' => 1,
            'method' => $method,
            'params' => $params,
        ];

        $endpoint = $this->endpoints[$method] ?? $this->endpoints['default'];

        $ch = curl_init($endpoint);
        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => ['Content-Type: application/json'],
            CURLOPT_SSLCERT => $this->certPath,
            CURLOPT_SSLKEY => $this->keyPath,
        ]);

        Log::debug("Sending request to $endpoint with payload: " . json_encode($payload));

        if ($this->caPath) {
            curl_setopt($ch, CURLOPT_CAINFO, $this->caPath);
        }

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            throw new Exception("cURL error: " . curl_error($ch));
        }

        $decoded = json_decode($response, true);
        curl_close($ch);

        if (isset($decoded['error'])) {
            throw new Exception("API error: " . json_encode($decoded['error']), $decoded['error']['code'] ?? 0);
        }

        //Log::debug("Received response from $endpoint: " . json_encode($decoded));

        // 🔍 Debug automatico
        //$this->dump($decoded['result'] ?? $decoded);
        return $decoded['result'] ?? [];
    }

    // Funzione dump compatibile
    private function dump($var): void
    {
        echo "\n==== RESPONSE ====\n";
        print_r($var);
        echo "\n==================\n";
    }

    // === Metodi pubblici ===

    public function listCredentials(string $email): array
    {
        try {
            $response = $this->sendRequest('listCredentials', ['account' => $email]);

            if (! isset($response)) {
                return [];
            }

            if (! isset($response['credentialsData'])) {
                throw new Exception("No credentials found for email: $email");
            }

            return $response['credentialsData'];

        } catch (Exception $e) {
            if ($e->getCode() === 512) {
                return [];
            }

            throw $e;
        }
    }

    public function addRegistration(array $registrationData, string $providerName, string $registrationOffice, string $language = 'IT'): array
    {
        return $this->sendRequest('addRegistration', [
            'raoUsername' => $this->raoUsername,
            'providerName' => $providerName,
            'registrationOffice' => $registrationOffice,
            'language' => $language,
            'registrationData' => $registrationData,
        ]);
    }

    public function completeRegistration(string $registrationId, string $otpKey, array $otpDetails): array
    {
        return $this->sendRequest('completeRegistration', [
            'raoUsername' => $this->raoUsername,
            'registrationId' => $registrationId,
            'otpKey' => $otpKey,
            'otpDetails' => $otpDetails,
            'url' => 'https://www.upnovation.it/ig.php', 
        ]);
    }

    public function approveRegistration(string $registrationId): array
    {
        return $this->sendRequest('approveRegistration', [
            'raoUsername' => $this->raoUsername,
            'registrationId' => $registrationId,
        ]);
    }

    public function uploadDocument(string $registrationId, string $base64Document, string $documentType = 'AGREEMENT'): array
    {
        return $this->sendRequest('uploadDocument', [
            'raoUsername' => $this->raoUsername,
            'registrationId' => $registrationId,
            'documentType' => $documentType,
            'document' => $base64Document,
            'signed' => true
        ]);
    }

    public function searchUser(string $fiscalCode, string $providerName, string $registrationOffice): array
    {
        return $this->sendRequest('searchUser', [
            'raoUsername' => $this->raoUsername,
            'taxIDNumber' => $fiscalCode,
            'providerName' => $providerName,
            'registrationOffice' => $registrationOffice,
        ]);
    }

    public function pushOtp(string $alias, string $domain): array
    {
        return $this->sendRequest('pushOTP', [
            'alias' => $alias,
            'domain' => $domain,
        ]);
    }

    public function mapPerson(Person $person): array
    {
        /** @var Address $address */
        if (! $address = $person->getAddress('residence')) {
            throw new Exception("Address not found for person: {$person->id}");
        }

        if (! $document = $person->getDocument('id')) {
            throw new Exception("Document not found for person: {$person->id}");
        }

        // se la residenza non è italiana il codice fiscale non è richiesto
        return [
            "surname" => $person->lastname,
            "name" => $person->name,
            "email" => $person->email,
            "gender" => "M", // FIXME
            "taxCodeCountry" => $address->country,
            "dateOfBirth" => $person->birthdate->format('d/m/Y'),
            "taxCode" => $person->taxCode,
            "documentType" => $this->mapType($document->type),
            "documentNumber" => $document->number,
            "documentIssuerCountry" => $document->issuerCountry,
            "documentExpiryDate" => $document->expiry->format('d/m/Y'),
            "documentIssuer" => $document->issuer,
            "documentIssuerDate" => $document->issuerDate->format('d/m/Y'),
            "cityOfBirth" => "Milano", // FIXME
            "provinceOfBirth" => "MI", // FIXME
            "countryOfBirth" => "IT", // FIXME
            
            // residenze 
            "state" => $address->zipRecord->country,
            
            "telephoneNumber" => $person->phone,
            "address" => $address->street,
            "postalCode" => $address->zip,
            "city" => $address->zipRecord->denominazione_ita,
            "province" => $address->zipRecord->denominazione_provincia,


            // non sanno a che serve - provo a disabilitare
            //"certUseLimit" => "ADVANCED_SIGN",
            "nationality" => "IT", // FIXME

            // dovrebbe essere opzionale anche se la documentazione dice di no
            //"cadastreCode" => "F205" // FIXME
        ];
    }

    public function mapType(string $type): string
    {
        $types = [
            'id' => 'CI',
            'passport' => 'PASS',
            'driving_license' => 'PA',
        ];

        if (! isset($types[$type])) {
            throw new Exception("Unsupported document type: $type");
        }

        return $types[$type];
    }
}
