<?php namespace Upnovation\Easyprofile\Signature\IGSign\Exceptions;

use Exception;

class CertificateAccountNotFoundException extends Exception
{
    protected $message = 'Certificate account not found. Please check the provided credentials or contact support.';
    
    public function __construct(string $message, int $code = 0)
    {
        if ($message) {
            $this->message = $message;
        }
        
        parent::__construct($this->message, $code);
    }
}