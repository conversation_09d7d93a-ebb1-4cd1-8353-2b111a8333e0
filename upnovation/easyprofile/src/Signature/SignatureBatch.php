<?php namespace Upnovation\Easyprofile\Signature;

use App\Models\Pipeline;
use App\Models\Task;

class SignatureBatch
{
    public string $title;
    public string $description;
    public Pipeline $pipeline;
    public Task $task;

    /**
     * How many days before the folder expire.
     */
    public int $deadline;
    public array $signatories = [];
    public array $documents = [];
    public array $attachments = [];
}