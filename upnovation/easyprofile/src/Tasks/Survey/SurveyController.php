<?php namespace Upnovation\Easyprofile\Tasks\Survey;

use App\Http\Controllers\Controller;
use App\Models\Coverage;
use App\Models\Form;
use App\Models\Pipeline;
use App\Models\Profile;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class SurveyController extends Controller
{
    use CanSwitchTheme;

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        if ($formModel = Form::wherePipelineId($pipeline->id)->first()) {
            $fd = $formModel->cachedStructure;
            $fv = $formModel->cachedValues;
        } else {
            $tenant = app('currentTenant');

            if (! $pipeline->type) {
                Log::error("Pipeline type is not set for pipeline ID: {$pipeline->id}");

                abort(500);
            }

            $filepath = storage_path("form.{$tenant->name}.{$pipeline->type}.json");

            if (! file_exists($filepath)) {
                Log::error("Form definition file not found: $filepath");

                abort(500);
            }

            try {
                $fd = json_decode(file_get_contents($filepath));
                $fd->pipeline_id = $pipeline->id;
                $fd->task_id = $task->id;
                $fv = null;
            } catch (\Exception $ex) {
                Log::error("Error decoding form definition file: $filepath");

                abort(500);
            }
        }

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'fd' => $fd,
            'fv' => $fv
        ]);
    }

    public function postIndex(Request $request, $pipelineId, $taskId)
    {
        if (! $mode = $request->get('mode')) {
            abort(400);
        }

        $task = Task::find($taskId);
        $pipeline = Pipeline::find($pipelineId);

        if ($mode == 'meta') {
            return $this->processMeta($request, $pipeline, $task);
        }

        if ($mode == 'data') {
            return $this->processData($request, $pipeline, $task);
        }

        abort(400);
    }

    public function processMeta(Request $request, $pipeline, $task)
    {
        // @todo refactor


        $pipelineId = $request->input('fd.pipeline_id');

        if (! $form = Form::wherePipelineId($pipelineId)->first()) {
            $form = new Form();
            $form->pipeline_id = $pipelineId;
            $form->version = $request->input('fd.version');
        }

        $form->cachedStructure = $request->input('fd');
        $form->cachedValues = $request->input('fv');
        $form->save();

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'fd' => $form->cachedStructure,
            'fv' => $form->cachedValues
        ]);
    }

    public function getValidationRules($tenant)
    {
        if (Config::get("easyprofile.tenants.{$tenant}") && $rules = Config::get("easyprofile.tenants.{$tenant}.tasks.survey.rules")) {
            return $rules;
        }

        return Config::get("easyprofile.tenants.default.tasks.survey.rules");
    }

    public function filterValidationRules(array $rules, array $filter)
    {
        if (! $filter) {
            return $rules;
        }

        // Assume some values might pass in a null value because UI reasons, so filter out null values.
        $filter = array_filter($filter, function($item){
            return ! is_null($item);
        });

        return array_intersect_key($rules, array_flip($filter));
    }

    public function processData(Request $request, $pipeline, $task)
    {
        // @todo massive refactor

        $rules = $this->filterValidationRules(
            $this->getValidationRules(app('currentTenant')->name), 
            $request->get('sectionFields')
        );
        
        try {
            
            // Per qualche motivo la prima invocazione di questa POST
            // (dalla section "data di nascita") triggera una eccezione di 
            // tipo Illuminate\Validation\ValidationException e qualcuno
            // o qualcosa fanno in modo che venga generata una response
            // Inertia-compliant che contiene, tra le altre cose, 
            // anche gli errori di validazione.
            // Nelle section successive viene sollevata la stessa eccezione
            // ma non scatta l'automatismo che restituisce gli errors al
            // frontend, per cui sembrava che la validazione andasse a buon fine
            // ma invece non era così (falliva a causa di questa eccezione).
            // Quindi ho aggiunto la gestione manuale dell'eccezione per gestire
            // sia il primo caso (data di nascita), che in realtà già funzionava
            // per i cazzi suoi, che i casi successivi che invece non funzionavano.

            $request->request->add([
                'date0' => Carbon::parse('100 years ago'),
                'date1' => Carbon::parse('18 years ago'),
            ]);

            $request->validate($rules);
            
        } catch (ValidationException $ex) {
            Log::debug($ex->getMessage());
            
            return Inertia::render($task->template, [
                "errors" => $ex->errors()
            ]);
        }
        
        // Get and format input.

        $fields = [
            'client' => [],
            'profile' => [],
            'coverage' => [],
        ];

        $inputFields = $request->only(array_filter($request->get('sectionFields')));

        // This for will separate inputs in the 3 $fields properties (client, profile, coverage).
        foreach ($inputFields as $key => $value) {
            $matches = null;

            preg_match("/^([\w]+)\/([\w]+)/", $key, $matches);

            if (isset($matches[1]) && isset($matches[2]) && isset($fields[$matches[1]])) {
                $fields[$matches[1]][$matches[2]] = $value;
            }
        }

         $client = $pipeline->getContractor();

        // FIXME :(
        // LEGACY: client data is now handled in client task.
        // Update every client field that was posted.
        if (app('currentTenant')->name == 'dorotea') {
            foreach ($fields['client'] as $field => $value) {
                $client->person->{$field} = $value;
            }

            $client->person->save();
        }
        
        $profile = $pipeline->getProfile() ?: new Profile();
//$profile->client()->associate($client);
        $profile->pipeline_id = $pipeline->id;

        // Update every profile field that was posted.
        foreach ($fields['profile'] as $field => $value) {
            $profile->{$field} = $value ?? null;
        }

        $profile->save();

        // When at least 1 coverage is posted, do the thing
        // This will not work properly in a use-case where
        // some user wants to delete all coverages that were
        // previously saved. That's not a use-case we care about tho.
        if ($fields['coverage']) {
            $coverageLabels = [];

            foreach ($fields['coverage'] as $coverageCategory) {
                // Each coverage can be a string with multiple labels separated by '|'
                foreach ($coverageCategory as $category) {
                    $coverageLabels = array_merge($coverageLabels, explode('|', $category));
                }
            }
            
            $coverages = Coverage::whereIn('label', $coverageLabels)->pluck('id');
    
            $profile->coverages()->sync($coverages);
        }

        return Inertia::render($task->template);
    }
}
