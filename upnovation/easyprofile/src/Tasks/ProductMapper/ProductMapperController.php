<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper;

use App\Http\Controllers\Controller;
use App\Models\MapperResult;
use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ProductMapperController extends Controller
{
    public function getIndex(Pipeline $pipeline, Task $task)
    {
        $profile = $pipeline->getProfile();

        // @TODO refactor rankMax.
        $items = $profile->mapperResult()->orderBy('rank', 'desc')->get();

        $rankMax = 0;

        foreach($items as $item) {
           $rankMax = $item->rank > $rankMax ? $item->rank : $rankMax;
        }

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'products' => $items,
            'rankMax' => $rankMax,
            'profile' => $profile,
            'log' => env('APP_DEBUG') ? $profile->log : null,
        ]);
    }

    public function postProducts(Pipeline $pipeline, Task $task, Request $request)
    {
        $results = [];

        foreach($request->get('products') as $product) {
            $results[] = MapperResult::find($product['id']);
        }

        $results = app()->make(ProductMapperManager::class)->confirm($results);

        return redirect()->route("pipeline.next", ['pipeline' => $pipeline->id]);
    }
}
