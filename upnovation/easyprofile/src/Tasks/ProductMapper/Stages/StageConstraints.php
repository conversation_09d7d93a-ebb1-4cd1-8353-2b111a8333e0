<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Profile;
use Illuminate\Support\Facades\Config;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperStageInterface;

class StageConstraints implements ProductMapperStageInterface
{
    public function run(Profile $profile, ProductMapperResult $result) : ProductMapperResult
    {
        foreach ($result->items as $key => $item) {
            $product = $item->getProduct();

            // 1 SCARTARE PRODOTTI QUANDO VIENE CHIESTA SOLO UNA GARANZIA FACOLTATIVA

            // 2 DA DEFINIRE - ATTESA FEEDBACK GUIDO - GESTIONE GARANZIE OBBLIGATORIE A PACCHETTO (ES. POSSONO ESSERE PRESE SOLO IN BLOCCO, 
            // PER CUI SE IL CLIENTE NE CHIEDE UNA SOLA IL PRODOTTO DEVE ESSERE MENO COERENTE O ADDIRITTURA
            // NON COERENTE)

            //
            // @TODO Enterprise - maybe separate Stages + a ProfileInterface
            //

            //
            // Check age
            //

            $age = $this->checkAgeConstraints(
                //$profile->client->birthdate->age,     
                $profile->pipeline->getContractor()->person->birthdate->age,
                $product->minAge,
                $product->maxAge,
            );

            if (! $age) {
                $result->log("{$product->name} removed for age contraint");

                $result->items->forget($key);
            }

            //
            // Check required job
            //

            $allowedCoverages = $this->checkJobConstraints(
                $profile,
                $item 
            );

            if (count($allowedCoverages) == 0) {
                $result->log("{$product->name} removed for required job contraint ({$profile->job})");

                $result->items->forget($key);
            }

            //
            // Check length
            //

            $tenant = app('currentTenant')->name;

            if (! $len = Config::get("easyprofile.values.tenants.{$tenant}.length.{$profile->length}")) {
                $len = Config::get("easyprofile.values.tenants.default.length.{$profile->length}");
            }

            $length = $this->checkLength(
                $len['min'], 
                $len['max'], 
                $product->minLen, 
                $product->maxLen
            );

            if (! $length) {
                $result->log("{$product->name} removed for length contraint");

                $result->items->forget($key);
            }
        }

        return $result;
    }

    public function checkLength($profileMin, $profileMax, $productMin, $productMax)
    {
        if (! $productMin && ! $productMax) {
            return true;
        }

        return 
            $this->checkRange($profileMin, $productMin, $productMax) || 
            $this->checkRange($profileMax, $productMin, $productMax);
    }

    public function checkRange($value, $min, $max)
    {
        if ($value < $min || $value > ($max ?? INF)) {
            return false;
        }

        return true;
    }

    public function checkAgeConstraints($value, $min, $max)
    {
        if (! $value) {
            throw new \Exception("Age cannot be null or zero.");
        }

        if ($min && $value < $min) {
            return false;
        }

        if ($max && $value > $max) {
            return false;
        }

        return true;
    }

    public function checkJobConstraints(Profile $profile, ProductMapperResultItem $item)
    {
        if (! $profile->job) {
            throw new \Exception("Job cannot be null or zero.");
        }

        $product = $item->getProduct();

        // Find coverages that are requested in client's profile
        // AND are within the product main coverages.
        $coverages = $product->mainCoverages()->intersect(
            $profile->mainCoverages()
        );

        $result = [];

        // We need at least one of the client's requested coverage
        // to be allowed by the client's job.
        foreach($coverages as $coverage) {
            // The following 2 if statements should not produce duplicate results,
            // as they work with opposite logic as of today.

            // Check for required jobs.
            if ($coverage->pivot->requiredJobs && ! $this->checkJobs($profile->job, $coverage->pivot->requiredJobs, 'required')) {
                $item->log("{$product->name} {$coverage->label} invalid for job {$profile->job}");
                $item->appendWarnings(["La garanzia {$coverage->name} è incoerente con il profilo del cliente."]);
                $item->invalidateCoverage($coverage);
                continue;
            }

            // Check for excluded jobs.
            if ($coverage->pivot->excludedJobs && ! $this->checkJobs($profile->job, $coverage->pivot->excludedJobs, 'excluded')) {
                $item->log("{$product->name} {$coverage->label} invalid for job {$profile->job}");
                $item->appendWarnings(["La garanzia {$coverage->name} è incoerente con il profilo del cliente."]);
                $item->invalidateCoverage($coverage);
                continue;
            }

            $result[] = $coverage;
        }

        return $result;
    }

    public function checkJobs(string $profileJob, string $jobs, $mode)
    {
        // Only allowed modes.
        if (! in_array($mode, ['required', 'excluded'])) {
            throw new \Exception("Mode not supported: {$mode}");
        }

        // Jobs is not set not allowed.
        if (! $jobs) {
            throw new \Exception("Jobs definition cannot be empty.");
        }

        if (in_array($profileJob, explode("|", $jobs))) {
            // In "required" mode we return true when the profile
            // job matches one entry in the jobs.

            // In "excluded" mode we return true when the profile
            // job does NOT matche entries in the hobs.
            return $mode == 'required' ? true : false;
        }

        // Same as above - reverse logic for both modes.
        return $mode == 'required' ? false : true;
    }

}