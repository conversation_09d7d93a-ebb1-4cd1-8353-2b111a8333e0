<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Product;
use App\Models\Profile;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperStageInterface;

class StageInit implements ProductMapperStageInterface
{
    public function run(Profile $profile, ProductMapperResult $result) : ProductMapperResult
    {
        if (! $mainCoverages = $profile->coverages()->where('type', 'main')->get()) {
            // da fak?
            throw new \Exception("No main coverages found.");
        }

        $products = $this->findProductsWithMain($mainCoverages->pluck('label')->toArray());

        $result =  new ProductMapperResult($products);

        $result->log("Req. main coverages: " . $mainCoverages->implode('label', ','));
        $result->log("Init set: " . $products->implode('name', ','));

        return $result;
    }

    public function findProductsWithMain(array $main)
    {
        return Product
            ::whereHas('coverages', function(Builder $query) use($main) {
                $query
                    ->where('setup', 'standard')
                    ->whereIn('label', $main);
            })
            
            ->where('enabled', true)
            ->get();
    }
}