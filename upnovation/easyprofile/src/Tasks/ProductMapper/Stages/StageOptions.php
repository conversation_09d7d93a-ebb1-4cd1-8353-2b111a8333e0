<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Product;
use App\Models\Profile;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperStageInterface;

class StageOptions implements ProductMapperStageInterface
{
    public function run(Profile $profile, ProductMapperResult $result) : ProductMapperResult
    {
        foreach ($result->items as $key => $item) {
            /** @var ProductMapperResultItem $item */

            $product = $item->getProduct();

            /** @var StageOptionsResult */
            $check = $this->checkProductOptions(
                $product,
                $profile->coverages->pluck('label')->toArray()
            );

            if (! $check->result) {
                $result->items->forget($key);

                $result->log("{$product->name} removed for incompatible options (" . (implode(", ", $check->unallowedCoverages)) . ")");

                continue;
            }

            foreach ($check->unallowedCoverages as $coverage) {
                $item->invalidateCoverage($coverage);

                $result->log("{$product->name} OK but {$coverage} removed for incompatible options.");
            }
        }

        return $result;
    }

    public function checkProductOptions(Product $product, array $labels) : StageOptionsResult
    {
        if (! $labels) {
            throw new \Exception("Coverage labels cannot be empty.");
        }

        // Product has no options.
        if (! $options = $product->getOptions($product)) {
            return new StageOptionsResult(true, []);
        }
        
        // Fill the control array with every requested coverage.
        // Valid coverages will be removed from this array, so 
        // an empty array means full compliance to the profile's 
        // request.
        $controlArray = $labels;

        $result = new StageOptionsResult(false, []);

        foreach ($options as $option => $coverages) {
            // If profile can have the whole option, 
            // the option's coverages will be removed
            // from the control array.
            if ($this->canHaveOption($coverages, $labels)) {
                $controlArray = array_diff($controlArray, $coverages);

                // Having one available option makes this product OK
                // for the profile to get regardless.
                $result->result = true;
            }
        }

        // Set the array of coverages that profile can NOT get.
        return $result->unallow(
            array_intersect($controlArray, $product->coverages->pluck('label')->toArray())
        );
    }   

    public function canHaveOption($optionCoverages, $profileCoverages)
    {
        if (! $profileCoverages) {
            return true;
        }

        // Get the intersection between requested coverages
        // and option coverages. 
        $intersection = array_intersect($optionCoverages, $profileCoverages);

        // In order to have an option, profile must be requesting
        // all of the option coverages.
        return $intersection == $optionCoverages;
    }
}