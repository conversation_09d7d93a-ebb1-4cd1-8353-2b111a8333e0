<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Coverage;
use App\Models\Product;
use App\Models\Profile;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperStageInterface;

class StageRank implements ProductMapperStageInterface
{
    public function run(Profile $profile, ProductMapperResult $result) : ProductMapperResult
    {
        foreach ($result->items as $product) {
            $this->rankProduct($profile, $product, $result);
        }

        return $result;
    }

    public function rankProduct(Profile $profile, ProductMapperResultItem $item, ProductMapperResult $result)
    {
        $product = $item->getProduct();

        $coverages = $product->coverages->intersect($profile->coverages);

        $rank = 0;

        $warnings = [];

        foreach ($coverages as $coverage) {
            // Coverage skipped and not added to ranking.
            if ($item->isCoverageInvalid($coverage)) {
                $result->log("coverage {$coverage->label} is invalid");

                continue;
            }

            $weight = 1;

            if ($coverage->type == 'main') {
                $weight *= 10;
            }

            $rank += $weight;

            $result->log("{$product->name} {$coverage->label} w:{$weight}");

            /*
            $coverageWarnings = $this->checkOptions(
                $profile->coverages->pluck('label')->toArray(), 
                $coverage
            );

            $rank += $weight - count($coverageWarnings);

            

            $result->log("{$product->name} {$coverage->label} w:{$weight} - " . (count($coverageWarnings)));

            $warnings = array_merge($warnings, $coverageWarnings);
            */
        }

        $item->rank($rank);

        //$item->appendWarnings($warnings);

        $result->log("{$product->name} rank: {$rank}");
    }

    /*
    public function checkOptions(array $profileCoverages, Coverage $coverage)
    {
        $warnings = [];

        if (in_array($coverage->label, $profileCoverages) && $coverage->pivot->inOptions) {
            $warnings[] = "La garanzia {$coverage->name} non può essere acquistata singolarmente";
        } 

        return $warnings;
    }
    */
}