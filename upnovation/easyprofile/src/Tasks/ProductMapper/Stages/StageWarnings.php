<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Product;
use App\Models\Profile;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResult;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperResultItem;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperStageInterface;

class StageWarnings implements ProductMapperStageInterface
{
    public function run(Profile $profile, ProductMapperResult $result) : ProductMapperResult
    {
        foreach ($result->items as $item) {
            //$this->getCoverageWarnings($profile, $item);

            // Static warnings
            if (! $item->getProduct()->warnings) {
                continue;
            }

            $this->getProfileWarnings($profile, $item);

            $this->getProductWarnings($item);
        }

        return $result;
    }

    //
    //
    //  Coverage Specific Warnings
    //  @TODO: refactor => StageRank
    //

    /*public function getCoverageWarnings(Profile $profile, ProductMapperResultItem $item)
    {
        $warnings = [];

        $profileCoverages = $profile->coverages->pluck('label')->toArray();
        Log::debug($item->getProduct()->name);
        foreach($item->getProduct()->coverages as $coverage) {
           
            Log::debug($coverage->label);
            Log::debug(in_array($coverage->label, $profileCoverages));
            Log::debug($coverage->pivot->inOptions);

            if (in_array($coverage->label, $profileCoverages) && $coverage->pivot->inOptions) {
                $warnings[] = "La garanzia {$coverage->name} non può essere acquistata singolarmente";
            } 
            
        }
        Log::debug("========================");
       

        //dump($warnings);
        
        return $item->appendWarnings($warnings);
    }*/

    //
    //
    //  Generic Product Warnings
    //
    //

    public function getProductWarnings(ProductMapperResultItem $item)
    {
        if (! $warnings = $item->getProduct()->warnings['product'] ?? null) {
            return;
        }

        return $item->appendWarnings($warnings);
    }


    //
    //
    //  Profile Warnings
    //
    //

    public function getProfileWarnings($profile, ProductMapperResultItem $item)
    {
        if (! $warnings = $item->getProduct()->warnings['profile'] ?? null) {
            return;
        }

        return $item->appendWarnings($this->extractWarningsFromProfile($profile, $warnings));
    }

    public function extractWarningsFromProfile(Profile $profile, $warnings)
    {
        // Only works for booleans as of now.

        $result = array_map(function($item, $key) use($profile){
            return $profile->{$key} ? $item : null;
        }, $warnings, array_keys($warnings));

        return array_filter($result);
    }

}