<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

use App\Models\Company;
use App\Models\CoverageCategory;
use Database\Factories\CoverageFactory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;
use Upnovation\Easyprofile\Tasks\ProductMapper\ProductMapperSeeder;

class StageOptionsSeeder extends Seeder
{
    public function run()
    {
        DB::connection('tenant')->table('coverage_product')->delete();
        DB::connection('tenant')->table('coverage_profile')->delete();
        DB::connection('tenant')->table('profiles')->delete();
        DB::connection('tenant')->table('coverages')->delete();
        DB::connection('tenant')->table('products')->delete();

        (new ProductMapperSeeder)->init();

        DB::connection('tenant')->table('products')->insert([
            ['id' => 1, 'company_id' => 1, 'code' => 'code1', 'name' => 'p1', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 2, 'company_id' => 1, 'code' => 'code2', 'name' => 'p2', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 3, 'company_id' => 1, 'code' => 'code3', 'name' => 'p3', 'enabled' => false, 'minLen' => null, 'maxLen' => null,],
            ['id' => 4, 'company_id' => 2, 'code' => 'code4', 'name' => 'p4', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 5, 'company_id' => 2, 'code' => 'code5', 'name' => 'p5', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 6, 'company_id' => 2, 'code' => 'code6', 'name' => 'p6', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 7, 'company_id' => 2, 'code' => 'code7', 'name' => 'p7', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
        ]);

        DB::connection('tenant')->table('coverages')->insert([
            ['id'=> 1, 'category_id' => 1, 'name' => 'main1', 'label' => 'main1', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 2, 'category_id' => 1, 'name' => 'main2', 'label' => 'main2', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 3, 'category_id' => 1, 'name' => 'main3', 'label' => 'main3', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 4, 'category_id' => 1, 'name' => 'main4', 'label' => 'main4', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 5, 'category_id' => 1, 'name' => 'main5', 'label' => 'main5', 'type' => 'main', 'target' => 'retail'],
        ]);

        DB::connection('tenant')->table('coverage_product')->insert([
            ['product_id'=> 1, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 1, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 1, 'coverage_id' => 3, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 1, 'coverage_id' => 4, 'setup' => 'optional', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 2, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 2, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 3, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 3, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 3, 'coverage_id' => 3, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 4, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 4, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 4, 'coverage_id' => 3, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            
            ['product_id'=> 5, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 5, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 5, 'coverage_id' => 3, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 5, 'coverage_id' => 4, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            
            ['product_id'=> 6, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 3, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 4, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 5, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 7, 'coverage_id' => 1, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 7, 'coverage_id' => 2, 'setup' => 'standard', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 7, 'coverage_id' => 3, 'setup' => 'optional', 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
         ]);

         DB::connection('tenant')->table('coverage_options')->insert([
            // Product 1 no options
            //...

            // Product 2: 1 option
            ['product_id'=> 2, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 2, 'coverage_id' => 2, 'option' => 1, ],

            // Product 3: 2 options
            ['product_id'=> 3, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 3, 'coverage_id' => 2, 'option' => 1, ],
            ['product_id'=> 3, 'coverage_id' => 1, 'option' => 2, ],
            ['product_id'=> 3, 'coverage_id' => 3, 'option' => 2, ],

            // Product 4: 2 options, one of which with a single coverage.
            ['product_id'=> 4, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 4, 'coverage_id' => 2, 'option' => 2, ],
            ['product_id'=> 4, 'coverage_id' => 3, 'option' => 2, ],

            ['product_id'=> 5, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 2, 'option' => 1, ],
            ['product_id'=> 5, 'coverage_id' => 3, 'option' => 2, ],
            ['product_id'=> 5, 'coverage_id' => 4, 'option' => 2, ],

            ['product_id'=> 6, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 6, 'coverage_id' => 2, 'option' => 1, ],
            ['product_id'=> 6, 'coverage_id' => 4, 'option' => 1, ],
            ['product_id'=> 6, 'coverage_id' => 5, 'option' => 1, ],
            ['product_id'=> 6, 'coverage_id' => 1, 'option' => 2, ],
            ['product_id'=> 6, 'coverage_id' => 2, 'option' => 2, ],
            ['product_id'=> 6, 'coverage_id' => 3, 'option' => 2, ],
            ['product_id'=> 6, 'coverage_id' => 4, 'option' => 2, ],

            ['product_id'=> 7, 'coverage_id' => 1, 'option' => 1, ],
            ['product_id'=> 7, 'coverage_id' => 2, 'option' => 1, ],
            ['product_id'=> 7, 'coverage_id' => 1, 'option' => 2, ],
            ['product_id'=> 7, 'coverage_id' => 2, 'option' => 2, ],
            ['product_id'=> 7, 'coverage_id' => 3, 'option' => 2, ],
         ]);
    }
}