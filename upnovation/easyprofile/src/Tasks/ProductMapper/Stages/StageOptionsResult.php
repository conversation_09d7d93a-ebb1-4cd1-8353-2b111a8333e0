<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper\Stages;

class StageOptionsResult
{
    /** @var bool */
    public $result;

    /** @var array */
    public $unallowedCoverages = [];

    public function __construct($result, $unallowedCoverages) 
    {
        $this->result = $result;

        $this->unallowedCoverages = $unallowedCoverages;
    }

    public function result($result) 
    {
        $this->result = $result;

        return $this;
    }

    public function unallow(array $unallowedCoverages) 
    {
        $this->unallowedCoverages = $unallowedCoverages;

        return $this;
    }
}