<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper;

use App\Models\Profile;
use App\Models\Task;
use Illuminate\Support\Facades\DB;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageConstraints;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageInit;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageOptions;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageRank;
use Upnovation\Easyprofile\Tasks\ProductMapper\Stages\StageWarnings;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class ProductMapperManager extends AbstractTask
{
    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $task = parent::initialize($task, $previousTask);

        DB::connection('tenant')->beginTransaction();

        $profile = $task->pipeline->getProfile();

        $profile->mapperResult()->delete();

        $this->map($profile);

        DB::connection('tenant')->commit();

        return $task;
    }

    public function map(Profile $profile) 
    {
        // @TODO: tenant-specific config.
        
        $stages = [
            // Products with main coverages - determine overall needs
            StageInit::class,

            // Products within Company constraints
            //  age
            //  job
            //  duration
            StageConstraints::class,

            // Check for product options compatibility.
            StageOptions::class,

            // Ranking (complementary coverages + other stuff)
            StageRank::class,

            // Warnings
            StageWarnings::class,
        ];

        $result = new ProductMapperResult();

        foreach($stages as $stageConfig) {
            /** @var ProductMapperStageInterface */
            $stage = app()->make($stageConfig);

            $result = $stage->run($profile, $result);
        }

        // @TODO
        // Stage x: raccomandazioni
        //      caso bloccante 
        //      se cliente segna "sto facendo/ho già un mutuo" => caso generico in cui avviso di verificare coerenza copertura es. con durata mutuo
        //          sottocaso TCM per mutuo: warning di verifica durata TCM con durata mutuo   

        // Stage n: 
        //  - products within salesman network

        return $result->save($profile);
    }
    
    public function confirm(array $results)
    {

        DB::connection('tenant')->beginTransaction();

        foreach($results as $result) {
            $result->confirmed = true;
            
            $result->save();
        }

        DB::connection('tenant')->commit();

        return $results;
    }
}
