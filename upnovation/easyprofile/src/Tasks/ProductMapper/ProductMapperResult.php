<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper;

use App\Models\MapperLog;
use App\Models\MapperResult;
use App\Models\Profile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductMapperResult 
{
    
    /** @var Collection */
    public $items;

    /** @var array */
    public $logs;

    public function __construct(Collection $products = null) 
    {
        if ($products) {
            $this->setProducts($products);
        }

        $this->logs = [];
    }

    public function products()
    {
        return $this->items->map(function($item){
            return $item->getProduct();
        });
    }

    public function setProducts(Collection $products)
    {
        $self = $this;

        return $this->items = $products->map(function($product) use($self) {
            return new ProductMapperResultItem($product, $self);
        });
    }

    public function log($log)
    {
        $this->logs[] = $log;

        return $this;
    }

    public function save(Profile $profile)
    {
        DB::connection('tenant')->beginTransaction();

        foreach ($this->items as $item) {
            $record = new MapperResult();
            $record->profile_id = $profile->id;
            $record->product_id = $item->getProduct()->id;
            $record->rank = $item->getRank() ?: 0;

            $record->recommendations = [
                "invalidCoverages" => $item->invalidCoverages->pluck("label"),
                "warnings" => $item->getWarnings()->flatten(),
            ];

            $record->save();
        }

        $logs = new MapperLog();
        $logs->profile_id = $profile->id;
        $logs->logs = $this->logs;
        $logs->save();

        DB::connection('tenant')->commit();

        return $profile->mapperResult;
    }
}