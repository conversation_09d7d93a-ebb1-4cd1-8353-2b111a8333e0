<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper;

use App\Models\Product;
use Illuminate\Support\Collection;

class ProductMapperResultItem
{
    /** @var Product */
    protected $product;

    /** @var int */
    protected $rank;

    /** @var Collection */
    public $invalidCoverages;

    /** @var ProductMapperResult */
    protected $parent;

    /** @var Collection */
    protected $warnings;

    public function __construct(Product $product, ProductMapperResult $parent) 
    {
        $this->product = $product;
        
        $this->parent = $parent;

        $this->invalidCoverages = collect([]);

        $this->warnings = collect([]);
    }

    public function log($log)
    {
        $this->parent->log($log);

        return $this;
    }

    public function setProduct(Product $product)
    {
        $this->product = $product;

        return $this;
    }

    public function getProduct()
    {
        return $this->product;
    }

    public function invalidateCoverage($coverage)
    {
        $this->invalidCoverages->push($coverage);

        return $this;
    }

    public function isCoverageInvalid($coverage)
    {
        return $this->invalidCoverages->contains($coverage);
    }

    public function rank($value)
    {
        $this->rank = $value;

        return $this;
    }

    public function getRank()
    {
        return $this->rank;
    }

    public function getWarnings()
    {
        return $this->warnings;
    }

    public function appendWarnings($warnings)
    {
        $this->warnings = $this->warnings->merge($warnings);

        return $this;
    }
}