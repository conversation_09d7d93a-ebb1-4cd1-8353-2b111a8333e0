<?php namespace Upnovation\Easyprofile\Tasks\ProductMapper;

use App\Models\Company;
use App\Models\CoverageCategory;
use App\Models\Pipeline;
use Database\Factories\CoverageFactory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Models\Tenant;

class ProductMapperSeeder extends Seeder
{
    public function run()
    {
        DB::connection('tenant')->table('coverage_product')->delete();
        DB::connection('tenant')->table('coverage_profile')->delete();
        DB::connection('tenant')->table('profiles')->delete();
        DB::connection('tenant')->table('coverages')->delete();
        DB::connection('tenant')->table('products')->delete();
        DB::connection('tenant')->table('clients')->delete();
        DB::connection('tenant')->table('pipelines')->delete();
        DB::connection('tenant')->table('users')->delete();

        $this->init();

        DB::connection('tenant')->table('products')->insert([
            ['id' => 1, 'company_id' => 1, 'code' => 'code1', 'name' => 'p1', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 2, 'company_id' => 1, 'code' => 'code2', 'name' => 'p2', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 3, 'company_id' => 1, 'code' => 'code3', 'name' => 'p3', 'enabled' => false, 'minLen' => null, 'maxLen' => null,],
            ['id' => 4, 'company_id' => 2, 'code' => 'code4', 'name' => 'p4', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 5, 'company_id' => 2, 'code' => 'code5', 'name' => 'p5', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 6, 'company_id' => 2, 'code' => 'code6', 'name' => 'p6', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 7, 'company_id' => 2, 'code' => 'code7', 'name' => 'p7', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            // To check for len
            //['id' => 8, 'company_id' => 2, 'name' => 'p8', 'enabled' => true, 'minLen' => 5, 'maxLen' => 10,],
            //['id' => 9, 'company_id' => 2, 'name' => 'p9', 'enabled' => true, 'minLen' => 30, 'maxLen' => 50,],

            // For standard/optional coverages
            ['id' => 10, 'company_id' => 2, 'code' => 'code10', 'name' => 'p10', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
            ['id' => 11, 'company_id' => 2, 'code' => 'code11', 'name' => 'p11', 'enabled' => true, 'minLen' => null, 'maxLen' => null,],
        ]);

        DB::connection('tenant')->table('coverages')->insert([
            ['id'=> 1, 'category_id' => 1, 'name' => 'main1', 'label' => 'main1', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 2, 'category_id' => 1, 'name' => 'main2', 'label' => 'main2', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 3, 'category_id' => 1, 'name' => 'main3', 'label' => 'main3', 'type' => 'main', 'target' => 'retail'],

            ['id'=> 4, 'category_id' => 1, 'name' => 'comp1', 'label' => 'comp1', 'type' => 'complementary', 'target' => 'retail'],
            ['id'=> 5, 'category_id' => 1, 'name' => 'comp2', 'label' => 'comp2', 'type' => 'complementary', 'target' => 'retail'],
            ['id'=> 6, 'category_id' => 1, 'name' => 'comp3', 'label' => 'comp3', 'type' => 'complementary', 'target' => 'retail'],
            ['id'=> 7, 'category_id' => 1, 'name' => 'comp4', 'label' => 'comp4', 'type' => 'complementary', 'target' => 'retail'],
            ['id'=> 8, 'category_id' => 1, 'name' => 'comp5', 'label' => 'comp5', 'type' => 'complementary', 'target' => 'retail'],
            ['id'=> 9, 'category_id' => 1, 'name' => 'comp6', 'label' => 'comp6', 'type' => 'complementary', 'target' => 'retail'],

            ['id'=> 10, 'category_id' => 1, 'name' => 'main10', 'label' => 'main10', 'type' => 'main', 'target' => 'retail'],
            ['id'=> 11, 'category_id' => 1, 'name' => 'main11', 'label' => 'main11', 'type' => 'main', 'target' => 'retail'],
        ]);

        DB::connection('tenant')->table('coverage_product')->insert([
            ['product_id'=> 1, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'job1', 'excludedJobs' => null],
            ['product_id'=> 1, 'coverage_id' => 2, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => 'job1|job2', 'excludedJobs' => null],
            ['product_id'=> 1, 'coverage_id' => 4, 'setup' => 'standard', 'inOptions' => true, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 2, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 2, 'coverage_id' => 3, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            // Disabled product
            ['product_id'=> 3, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 4, 'coverage_id' => 4, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 5, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'job5', 'excludedJobs' => null],
            ['product_id'=> 5, 'coverage_id' => 2, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => 'job5'],

            ['product_id'=> 6, 'coverage_id' => 1, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => 'job1', 'excludedJobs' => 'job1'],
            ['product_id'=> 6, 'coverage_id' => 2, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 7, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 8, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 6, 'coverage_id' => 9, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            ['product_id'=> 10, 'coverage_id' => 10, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            ['product_id'=> 10, 'coverage_id' => 11, 'setup' => 'optional', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            
            ['product_id'=> 11, 'coverage_id' => 11, 'setup' => 'standard', 'inOptions' => false, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],

            //['product_id'=> 9, 'coverage_id' => 1, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
            //['product_id'=> 9, 'coverage_id' => 2, 'weight' => 1, 'requiredJobs' => null, 'excludedJobs' => null],
        ]);

        DB::connection('tenant')->table('clients')->insert([
            ['id'=> 1, 'name' => 'client name', ],
        ]);

        \App\Models\User::factory()->create([
            'id' => 1,
        ]);

        Pipeline::factory()->createMany([
            ['id' => 1, 'user_id' => 1, ],
        ]);

        DB::connection('tenant')->table('profiles')->insert([
            ['id'=> 1, 'pipeline_id' => 1, 'job' => 'job1', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 2, 'pipeline_id' => 1, 'job' => 'job2', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 3, 'pipeline_id' => 1, 'job' => 'job3', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 4, 'pipeline_id' => 1, 'job' => null, 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 5, 'pipeline_id' => 1, 'job' => 'job5', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 6, 'pipeline_id' => 1, 'job' => 'job5', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            ['id'=> 7, 'pipeline_id' => 1, 'job' => 'job5', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
            //['id'=> 8, 'client_id' => 1, 'pipeline_id' => 1, 'job' => 'job1', 'length' => 'short', 'areaHouse' => 0, 'areaAssets' => 0, 'areaPerson' => 0, ],
        ]);

        DB::connection('tenant')->table('coverage_profile')->insert([
            ['profile_id'=> 1, 'coverage_id' => 1,],
            ['profile_id'=> 1, 'coverage_id' => 2,],

            ['profile_id'=> 2, 'coverage_id' => 1,],
            ['profile_id'=> 2, 'coverage_id' => 2,],

            ['profile_id'=> 3, 'coverage_id' => 1,],
            ['profile_id'=> 3, 'coverage_id' => 2,],

            ['profile_id'=> 5, 'coverage_id' => 1,],
            ['profile_id'=> 5, 'coverage_id' => 2,],

            ['profile_id'=> 6, 'coverage_id' => 1,],
            ['profile_id'=> 6, 'coverage_id' => 2,],
            ['profile_id'=> 6, 'coverage_id' => 3,],
            ['profile_id'=> 6, 'coverage_id' => 4,],
            ['profile_id'=> 6, 'coverage_id' => 5,],
            ['profile_id'=> 6, 'coverage_id' => 6,],
            ['profile_id'=> 6, 'coverage_id' => 7,],
            ['profile_id'=> 6, 'coverage_id' => 8,],

            // To flow-test length constraints
            //['profile_id'=> 8, 'coverage_id' => 1,],
            //['profile_id'=> 8, 'coverage_id' => 2,],
        ]);
    }

    public function init() 
    {
        DB::connection('tenant')->table('companies')->delete();
        DB::connection('tenant')->table('coverage_categories')->delete();

        DB::connection('tenant')->table('companies')->insert([
            ['id' => 1, 'name' => 'Groupama', 'logo' => '',],
            ['id' => 2, 'name' => 'CF Assicurazioni', 'logo' => '',],
            ['id' => 3, 'name' => 'Net Insurance', 'logo' => '',],
            ['id' => 4, 'name' => 'Axa Assicurazioni', 'logo' => '',],
            ['id' => 5, 'name' => 'Afi Esca', 'logo' => '',],
        ]);

        DB::connection('tenant')->table('coverage_categories')->insert([
            ['id' => 1, 'name' => 'Protezione Persona', 'branch' => 'non-life'],
            ['id' => 2, 'name' => 'Protezione Beni', 'branch' => 'non-life'],
            ['id' => 3, 'name' => 'Protezione Patrimonio', 'branch' => 'non-life'],
        ]);
    }
}