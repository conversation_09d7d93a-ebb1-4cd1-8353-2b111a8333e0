<?php namespace Upnovation\Easyprofile\Tasks\Summary;

use App\Http\Controllers\Controller;
use App\Models\Coverage;
use App\Models\Form;
use App\Models\Pipeline;
use App\Models\Profile;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;

class SummaryController extends Controller
{
    public function getIndex(Pipeline $pipeline, Task $task)
    {
        //dump($pipeline->form->cachedStructure);
        //dump($pipeline->form->cachedValues);
        //app()->make(SurveyManager::class)->finalize($task);
        //dd(app()->make(SurveyManager::class)->stripForm($pipeline->form->cachedStructure, $pipeline->form->cachedValues));

        try {
            $results = $pipeline->getProfile()->mapperResult()->where('confirmed', true)->get();
        } catch (\Exception $ex) {
            Log::debug($ex->getTraceAsString());
            Log::error($ex->getMessage());
            throw $ex;
        }

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'form' => $pipeline->form,
            'results' => $results,
        ]);
    }
}
