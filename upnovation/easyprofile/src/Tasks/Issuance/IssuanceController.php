<?php namespace Upnovation\Easyprofile\Tasks\Issuance;

use App\Events\IssuanceProcessed;
use App\Events\PolicyUploaded;
use App\Http\Controllers\Controller;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use Error;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Errors;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Toast;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;


class IssuanceController extends Controller
{
    protected IssuanceManager $manager;

    public function __construct(IssuanceManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        $issuances = $task->issuances()->with('product')->get()->map(function ($issuance) use ($task) {
            $issuance->load('product.company');
            $issuance->load('contract');
            $issuance->load('form');

            if (! $policyDocument = $issuance->product->getPolicyDocument()) {
                // Products with processType == 'direct' do not have a policy document
                
                return $issuance;
            }

            $issuance->policy = File::whereDocumentId($policyDocument->id)
                ->whereTaskId($task->id)
                ->first();

            return $issuance;
        });
         
        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'issuances' => $issuances,
        ]);
    }

    public function getIssue(Pipeline $pipeline, Task $task, Product $product)
    {
        if (! isset($product->issuanceProcessor['controller'])) {
            Log::error("Issuance processor not defined for product: {$product->name}");

            abort(500, "Issuance processor not defined for product: {$product->name}");
        }

        return redirect()->action([$product->issuanceProcessor['controller'], 'getIndex'], [
            'pipeline' => $pipeline->id,
            'task' => $task->id,
            'product' => $product->id,
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        $task->addData('issuanceData', $request->all());
        $task->save();

        return redirect()->route('pipeline.next', ['pipeline' => $pipeline->id]);
    }

    /**
     * Policy upload from backend operators.
     */
    public function postUpload(Request $request, Pipeline $pipeline, Task $task)
    {
        if (! $uploaded = $request->file('file')) {
            Log::error('File not uploaded');

            return abort(400, 'File not uploaded');
        }

        if (! $issuance = Issuance::find($request->input('issuance_id'))) {
            Log::error('Issuance not found');
            
            return abort(404, 'Issuance not found');
        }

        $file = $this->manager->makePolicyFile($issuance);

        ($this->manager->savePolicyFile(
            $issuance,
            $file,
            file_get_contents($uploaded->getRealPath())
        ));

        return null;
    }

    public function getIssuanceForm(Request $request, Pipeline $pipeline, Task $task, Issuance $issuance)
    {
        if (! $document = $issuance->product->getFormDocument()) {
            Log::error('Form document not found for this product.');

            return redirect()->back()->withErrors(Errors::make($task, 'issuanceFormNotFound'));
        }

        if (! $file = $document->template) {
            Log::error('Template file not found for this document.');

            return redirect()->back()->withErrors(Errors::make($task, 'templateNotFound'));
        }

        return response()->download($file->getFullPath());
    }

    /**
     * Form upload for "process C"
     */
    public function postIssuanceForm(Request $request, Pipeline $pipeline, Task $task, Issuance $issuance)
    {
        if (! $uploaded = $request->file('file')) {
            Log::error('File not uploaded');

            return redirect()->back()->withErrors(Errors::make($task, 'fileNotUploaded'));
        }

        // @TODO maybe refactor idk, maybe manager should take
        // the raw input and make the file himself.

        $file = $this->manager->makeDownloadableFile($issuance);

        $issuance = $this->manager->processDownload(
            $issuance, 
            $file,
            file_get_contents($uploaded->getRealPath())
        );

        return redirect()->back()->with(Toast::success("file.uploaded"));
    }

    function postAskReload(Request $request, Pipeline $pipeline, Task $task, Issuance $issuance)
    {
        // Disabled: see manager->askReload()
        abort(400);

        $this->manager->askReload($issuance);

        return redirect()->back()->with(Toast::success("reload.requested"));
    }

    // todo
    public function disable(Issuance $issuance)
    {
        $issuance->status = 'disabled';
        $issuance->save();
        return redirect()->back();
    }

}
