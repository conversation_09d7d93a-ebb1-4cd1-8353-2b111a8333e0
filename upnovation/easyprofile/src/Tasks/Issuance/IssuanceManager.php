<?php namespace Upnovation\Easyprofile\Tasks\Issuance;

use App\Events\IssuanceProcessed;
use App\Events\PolicyUploaded;
use App\Models\Client;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Task;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use League\CommonMark\Extension\SmartPunct\Quote;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Quote\QuoteService;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class IssuanceManager extends AbstractTask
{
    protected QuoteService $quoteService;

    public function __construct(QuoteService $quoteService, DocumentsManager $documentsManager, FileManager $fileManager)
    {
        parent::__construct($documentsManager, $fileManager);

        $this->quoteService = $quoteService;
    }

    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $task = parent::initialize($task, $previousTask);

        $products = $task->pipeline->getSelectedProducts();

        DB::connection('tenant')->beginTransaction();

        // Delete all issuances for the task
        $task->issuances()->delete();

        // Create new issuance for each product
        foreach ($products as $product) {
            $task->issuances()->create([
                'product_id' => $product->id,
                'status' => 'pending',
            ]);
        }

        DB::connection('tenant')->commit();

        return $task;
    }

    public function getConfig(Issuance $issuance): array|null
    {
        $product = $issuance->product;

        if (! $document = $product->getFormDocument()) {
            Log::error("Product form document not found for product: {$product->name}");

            return null;
        }

        Log::debug("Loading configuration for document: {$document->id} - {$document->title}");

        if (! $config = $document->loadConfiguration()) {
            Log::error("Document configuration not found for document: {$document->title}");

            return null;
        }

        return $config;
    }

    /**
     * Get the input from the user and process the issuance.
     * Direct: the user compile a form and the issuance pdf is compiled immediately.
     * Deferred: the user fill a form and the issuance pdf uploaded at backoffice.
     * 
     * Download: @todo the user downloads/reuploads the issuance pdf.
     */
    public function process(Task $task, Issuance $issuance, array $data)
    {
        switch ($issuance->product->processType) {
            case 'direct':
            case 'deferred':
                // Deferred seems to be the same as direct, so we can use the same method.
                return $this->processDirect($task, $issuance, $data);
            case 'download':
                // Actually, the controller is calling processDownload directly.

                //return $this->processDownload($task, $issuance, $data);
        }

        throw new Exception("Unknown issuance process type: {$issuance->product->processType}");
    }

    public function processDirect(Task $task, Issuance $issuance, array $data)
    {
        if (! $config = $this->getConfig($issuance)) {
            throw new Exception("Issuance configuration not found for product: {$issuance->product->name}");
        }

        // Setup data.
        $product = $issuance->product;

        $document = $product->getFormDocument();

        switch ($product->processType) {
            case 'direct':
                $fileType = 'signable';
                $issuance->status = 'completed';
                break;
            case 'deferred':
                $fileType = 'compiled';
                $issuance->status = 'awaiting';
                break;
            default:
                throw new \Exception("Unknown process type: {$product->processType}");
        }

        // This product requires a quotation to be printed in the docs.
        /*if (isset($config['quote']) && isset($config['quote']['fields'])) {
            $parameters = Arr::only($data, $config['quote']['fields']);

            $quote = $this->quoteService->quote($product->code, $parameters);

            $data = array_merge($data, ['quote' => $quote['totale']]);
        } */

        // If issuance were already processed, we need to delete the previous files.
        if ($file = File::whereDocumentId($document->id)->whereTaskId($task->id)->first()) {
            $this->files->delete($file);
        }

        // Process file.
        /** @var PdfProcessor $processor */
        $processor = app()->make(PdfProcessor::class);

        $file = $processor->compile(
            $document,
            $task->pipeline,
            new File([
                'filename' => "{$document->type}-{$document->version}-". (date('Ymd-His')) ."-{$task->pipeline_id}-{$task->id}.pdf",
                'disk' => 'documents',
                'path' => 'compiled',
                'task_id' => $task->id,
                'document_id' => $document->id,
                'type' => $fileType,
            ]),
            $data
        );

        $file->save();

        switch ($product->processType) {
            case 'direct':
                $issuance->contract_file_id = $file->id;
                break;
            case 'deferred':
                $issuance->form_file_id = $file->id;
                break;
            default:
                throw new \Exception("Unknown process type: {$product->processType}");
        }

        $issuance->save();

        $this->updateTaskCompletion($task);

        // fire an event
        event(new IssuanceProcessed($issuance));

        return $issuance;
    }

    /**
     * Process type C.
     * Basically it saves the file as compiled and puts the issuance status to 'awaiting'.
     */
    public function processDownload(Issuance $issuance, File $file, $data)
    {
        if ($oldFile = File::whereDocumentId($file->document_id)->whereTaskId($issuance->task_id)->first()) {
            $this->files->delete($oldFile);
        }

        $file = $this->files->save(
            app('currentTenant')->name,
            $file,
            $data
        );

        $issuance->status = 'awaiting';
        $issuance->form_file_id = $file->id;
        $issuance->save();

        // Update the task completion.
        $this->updateTaskCompletion($issuance->task);

        return $issuance;
    }

    public function makePolicyFile(Issuance $issuance): File
    {
        if (! $issuance->product) {
            throw new Exception("Issuance product not found for issuance: {$issuance->id}");
        }

        $product = $issuance->product;

        if (! $document = $product->getPolicyDocument()) {
            throw new Exception("Product policy document not found for product: {$product->name}");
        }

        // Create file.
        return new File([
            'task_id' => $issuance->task_id,
            'document_id' => $document->id,
            'type' => 'signable',
            'disk' => 'documents',
            'path' => 'compiled',
            'filename' => "policy-" . date('Ymd-His') . "-{$issuance->task_id}-{$issuance->id}.pdf",
        ]);
    }

    public function makeDownloadableFile(Issuance $issuance): File
    {
        if (! $product = $issuance->product) {
            throw new Exception("Issuance product not found for issuance: {$issuance->id}");
        }

        if (! $document = $product->getFormDocument()) {
            throw new Exception("Product form document not found for product: {$product->name}");
        }

        return new File([
            'filename' => "{$document->type}-{$document->version}-". (date('Ymd-His')) ."-{$issuance->task->pipeline_id}-{$issuance->task->id}.pdf",
            'disk' => 'documents',
            'path' => 'compiled',
            'task_id' => $issuance->task_id,
            'document_id' => $document->id,
            'type' => 'compiled',
        ]);
    }

    public function makeFormFile(Issuance $issuance): File
    {
        if (! $issuance->product) {
            throw new Exception("Issuance product not found for issuance: {$issuance->id}");
        }

        $product = $issuance->product;

        if (! $document = $product->getPolicyDocument()) {
            throw new Exception("Product policy document not found for product: {$product->name}");
        }

        // Create file.
        return new File([
            'task_id' => $issuance->task_id,
            'document_id' => $document->id,
            'type' => 'signable',
            'disk' => 'documents',
            'path' => 'compiled',
            'filename' => "policy-" . date('Ymd-His') . "-{$issuance->task_id}-{$issuance->id}.pdf",
        ]);
    }
   
    public function savePolicyFile(Issuance $issuance, File $file, $rawData)
    {
        if ($issuance->status != 'awaiting') {
            throw new Exception("Issuance status is not 'awaiting': {$issuance->status}");
        }

        // We need to delete the form file that the user generated.
        $formDocument = $issuance->product->getFormDocument();
        $formFile = File::whereDocumentId($formDocument->id)->whereTaskId($issuance->task->id)->first();

        // @fixme sembra non cancellare questo
        if ($formFile) {
            $this->files->delete($formFile);
        }

        // We also want to delete any previous policy file.
        $policyDocument = $issuance->product->getPolicyDocument();
        $policyFile = File::whereDocumentId($policyDocument->id)->whereTaskId($issuance->task->id)->first();
        if ($policyFile) {
            $this->files->delete($policyFile);
        }

        // Now we save the new policy file.
        $file = $this->files->save(
            app('currentTenant')->name, 
            $file, 
            $rawData
        );

        // Update the issuance status.
        $issuance->status = 'completed';
        $issuance->contract_file_id = $file->id; 
        $issuance->save();

        // Update the task completion.
        $this->updateTaskCompletion($issuance->task);

        event(new PolicyUploaded($issuance));

        return $issuance;
    }

    public function getIssuancesByClient(Client $client)
    {
        // 1. Trova tutti i pipeline_id associati al client
        if ($client->person) {
            $id = [
                'field' => 'person_id',
                'value' => $client->person->id,
            ];
        } elseif ($client->enterprise) {
            $id = [
                'field' => 'enterprise_id',
                'value' => $client->enterprise->id,
            ];
        } else {
            throw new \Exception("Client or person not found.");
        }

        $pipelineIds = Client::where($id['field'], $id['value'])->pluck('pipeline_id')->toArray();

        // 2. Trova tutti i task di questi pipeline
        $tasks = Task::whereIn('pipeline_id', $pipelineIds)->pluck('id');

        // 3. Trova tutte le issuance di questi task
        $issuances = Issuance::whereIn('task_id', $tasks)
            ->with(['task.pipeline'])
            ->with(['product.company'])
            ->get();

        // 4. Raggruppa per pipeline_id
        $grouped = $issuances
            ->groupBy(function($issuance) {
                return $issuance->task->pipeline_id;
            })
            ->sortByDesc(function($issuances, $pipelineId) {
                return optional($issuances->first()->task->pipeline)->created_at;
            })
            ->values(); 

        return $grouped;
    }

    public function askReload(Issuance $issuance): Issuance
    {
        // @todo disabled: il form file viene cancellato quando si carica la polizza,
        // quindi bisognerebbe mantenerlo e cancellarlo successivamente e verificare
        // le conseguenze, non jela posso fa adesso.
        return $issuance;


        // If the issuance is not completed, we can reload it.
        if ($issuance->status != 'completed') {
            return $issuance;
        }

        // If the issuance has a contract file, we can reload it.
        $issuance->status = 'awaiting';

        $issuance->save();

        $this->updateTaskCompletion($issuance->task);

        return $issuance;
    }

    public function updateTaskCompletion(Task $task): Task
    {
        $completed = true;

        foreach ($task->issuances as $issuance) {
            if ($issuance->status != 'completed') {
                $completed = false;
                break;
            }
        }

        $task->addData('taskCompleted', $completed);
        $task->save();

        return $task;
    }

}
