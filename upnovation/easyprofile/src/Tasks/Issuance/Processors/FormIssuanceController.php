<?php namespace Upnovation\Easyprofile\Tasks\Issuance\Processors;

use App\Http\Controllers\Controller;
use App\Models\Document;
use App\Models\File;
use App\Models\Issuance;
use App\Models\Pipeline;
use App\Models\Product;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Upnovation\Easyprofile\Pdf\PdfProcessor;
use Upnovation\Easyprofile\Quote\QuoteService;
use Upnovation\Easyprofile\Tasks\Issuance\IssuanceManager;
use Upnovation\Easyprofile\Tasks\Signature\SignatureManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;


class FormIssuanceController extends Controller
{
    use CanSwitchTheme;

    protected QuoteService $quoteService;

    protected IssuanceManager $manager;

    public function __construct(QuoteService $quoteService, IssuanceManager $signatureManager)
    {
        $this->quoteService = $quoteService;

        $this->manager = $signatureManager;
    }

    public function getIndex(Pipeline $pipeline, Task $task, Issuance $issuance)
    {
        $product = $issuance->product;

        if (! isset($product->issuanceProcessor['template'])) {
            Log::error("Issuance template not defined for product: {$product->name}");

            abort(500, "Issuance template not defined for product: {$product->name}");
        }

        $path = $this->getView(app('currentTenant')->name, $product->issuanceProcessor['template']);

        return Inertia::render($path, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'issuance' => $issuance->load('product.company'),
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task, Issuance $issuance)
    {
        if (! $config = $this->manager->getConfig($issuance)) {
            abort(500, "Issuance configuration not found for product: {$issuance->product->name}");
        }

        $request->validate($config['formRules']);

        $this->manager->process($task, $issuance, $request->all());

        return redirect()->route('tasks.issuance', ['pipeline' => $pipeline, 'task' => $task,]);
    }
}
