<?php namespace Upnovation\Easyprofile\Tasks\Signature;

use App\Events\SignatureComplete;
use App\Models\Document;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Task;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\Signature\IGSign\IGSign;
use Upnovation\Easyprofile\Signature\SignatureBatch;
use setasign\Fpdi\Fpdi;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Illuminate\Support\Str;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;

class SignatureManager extends AbstractTask
{
    static string $SIGNATURE_SIMPLE = 'simple';
    static string $SIGNATURE_FEA = 'fea';

    // @todo dependency injection for signature service
    protected IGSign $service;

    protected FileManager $files;

    protected SurveyManager $surveyManager;

    public function __construct(DocumentsManager $documents, FileManager $files, SurveyManager $surveyManager)
    {
        parent::__construct($documents, $files);

        // @todo dependency injection for signature service
        //$this->service = app()->make(SignatureServiceInterface::class);
        $this->service = new IGSign();

        $this->files = $files;

        $this->surveyManager = $surveyManager;
    }

    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $files = $this->loadFiles($task);

        if (! $files->count()) {
            throw new Exception("No files to sign for task: {$task->id}");
        }

        return $task;
    }

    public function finalize(Task $task): Task
    {
        parent::finalize($task);

        $this->files->deletePipelineSignableFiles($task->pipeline);

        if (! isset($task['config']['finalizationEvents'])) {
            return $task;
        }

        foreach ($task['config']['finalizationEvents'] as $event) {
            if (! class_exists($event)) {
                Log::error("Event class {$event} does not exist for task: {$task->id}");

                continue;
            }

            event(new $event($task, $this->getSigners($task)));
        }

        return $task;
    }

    public function skip(Task $currentTask, Task $targetTask, $data = null)
    {
        parent::skip($currentTask, $targetTask, $data);

        // $data is the most recent profile found for this client.

        $files = $this->files->loadPipelineFiles(
            $data->pipeline, 
            ['type' => 'folder', 'tag' => 'precontractual'],
        );

        DB::connection('tenant')->beginTransaction();

        foreach ($files as $file) {
            $file = $file->replicate();
            $file->uuid = null;
            $file->task_id = $currentTask->id;
            $file->created_at = null;
            $file->updated_at = null;
            $file->save();
        }

        DB::connection('tenant')->commit();
    }
    
    public function loadFiles(Task $task) : Collection
    {
        return $this->files->loadPipelineFiles(
            $task->pipeline, 
            ['type' => 'signable']
        );
    }
    
    // @todo maybe refactor to get info from SignatureBatch
    public function getSigners(Task $task) : array
    {
        $signers = [];

        if (! $documents = Document::whereIn('type', $task->config['documents'])->get()) {
            throw new Exception("No documents found for task: {$task->id}");
        }

        // @todo testare con più di un ruolo possible
        $roles = $documents->pluck('signers')->flatten()->unique('id')->values()->all();

        foreach ($roles as $role) {
            $signers = array_merge(
                $signers, 
                $task->pipeline->getPersonSubjects($role)
            );
        }

        return $signers;
    }

    public function processSignature(Task $task) 
    {
        if (! $task->config || ! isset($task->config['signatureType'])) {
            throw new Exception("Task {$task->id} does not have signers configured.");
        }

        // @todo check: read from document
        $signers = $this->getSigners($task);

        if (app()->environment('local')) {
            if (! env('IGSIGN_DEBUG_PHONE') || ! env('IGSIGN_DEBUG_EMAIL')) {
                throw new Exception('Please set IGSIGN_DEBUG_PHONE and IGSIGN_DEBUG_EMAIL in your .env file for local testing.');
            }

            foreach($signers as $signer) {
                $signer->phone = env('IGSIGN_DEBUG_PHONE');
                $signer->email = env('IGSIGN_DEBUG_EMAIL');
                $signer->taxCode = 'TEST1234567890'; // Use a dummy tax code for testing
            }
        }

        $batch = $this->makeBatch(
            $task->pipeline, 
            $task
        );
       
        if ($task->config['signatureType'] == SignatureManager::$SIGNATURE_SIMPLE) {
            $folder = $this->service->signSimple(
                $batch,
                $signers,
                $task->config['authentication'] ?? null
            ); 
        }

        elseif ($task->config['signatureType'] == SignatureManager::$SIGNATURE_FEA) {
            $folder = $this->service->signFea(
                $batch,
                $signers
            );
        }

        else {
            throw new Exception("Unsupported signature type: {$task->config['signatureType']}");
        }

        $task->addData('signatureStatus', 'pending');
        $task->addData('folder', $folder);
        $task->save();

        return $folder;
    }

    public function processCallback(Task $task, array $data)
    {
        // Update task status, folder, etc.
        Log::debug('Processing callback for task: ' . $task->id);

        $localFolder = File::where('task_id', $task->id)
            ->where('type', 'folder')
            ->first();

        if ($localFolder) {
            Log::warning("Local folder already processed: {$task->id}");

            return null;
        }

        // @TODO encode / encrypt params
        if ($data['eventId'] != 11) {
            Log::error("Unsupported IGSign event: {$data['eventId']}");

            return null;
        }

        if (! $folder = $this->service->getFolder($data['folderId'])) {
            Log::error("Folder not found: {$data['folderId']}");

            return null;
        }

        if (! $this->service->isFolderCompleted($folder)) {
            Log::debug("Folder not completed: {$data['folderId']}");

            return null;
        }

        $documents = $this->service->downloadFolder($data['folderId']);
    
        $file = new File([
            'filename' => (date('Y-m-d-His')) . "-folder-{$data['folderId']}-{$task->pipeline_id}-{$task->id}.zip",
            'signatureProviderId' => $data['folderId'],
            'displayName' => $task['config']['folderTitle'] ?? 'Documenti firmati',
            'disk' => 'documents',
            'path' => 'signed',
            'task_id' => $task->id,
            'type' => 'folder',
            'tag' => $task->config['folderTag'] ?? null,
        ]);

        try {
            $signed = $this->files->save(
                app('currentTenant')->name, 
                $file, 
                $documents
            );

            $this->files->deleteTaskSignableFiles($task);

        } catch (Exception $e) {
            Log::error("Error saving signed documents for task: {$task->id} - {$e->getMessage()}");
            Log::debug($e->getMessage());

            throw new Exception("Error saving signed documents for task: {$task->id}");
        }

        $task->addData('folder', null);
        $task->addData('folder', null);
        $task->addData('folderId', $data['folderId']);
        $task->addData('signatureStatus', 'done');
        $task->save();

        event(new SignatureComplete(
            $signed, 
            $task
        ));

        return $task;
    }

    public function makeBatch(Pipeline $pipeline, Task $task) : SignatureBatch | null
    {
        if (! isset($task->config['folderTitle'])) {
            throw new Exception("Task {$task->id} does not have folderTitle configured.");
        }

        if (! isset($task->config['folderDescription'])) {
            throw new Exception("Task {$task->id} does not have folderDescription configured.");
        }

        $date = Carbon::parse($pipeline->created_at);

        $batch = new SignatureBatch();
        $batch->pipeline = $pipeline;
        $batch->task = $task;
        $batch->title = $task->config['folderTitle'] . " #{$pipeline->id}-{$task->id} del " . $date->format('d/m/Y');
        $batch->description = $task->config['folderDescription'] ." relativa alla posizione #{$pipeline->id}-{$task->id} del " . $date->format('d/m/Y');
        $batch->deadline = $task->config['signatureDeadlineInDays'] ?? 1;


        $documents = $this->files->loadPipelineFiles(
            $task->pipeline, 
            ['type' => 'signable'],

            // @fixme va bene passare type=>var? non dovrebbe essere solo var?
            // sembra funzionare nelle prime due firma ma non nell'ultima
            $task->config['documents']
        );

        // Weird.
        if (! $documents->count()) {
            throw new Exception("No signable documents found for task: {$task->id}");
        }

        $attachments = [];

        // We don't want to run that query if attachments are not configured,
        // since if the array is empty that filter would be skipped.
        if ( ! empty($task->config['attachments'])) {
            $attachments = $this->files->loadPipelineFiles(
                $task->pipeline, 
                ['type' => 'signable'],
                $task->config['attachments']
            );

            if (! $attachments->count()) {
                Log::debug("No signable attachments found for task: {$task->id}");
            }
        }

        foreach ($documents as $file) {
            $batch->documents[] = (object)[
                'name' => $file->document->title,
                'filepath' => $file->getFullPath(),
                'outputFilename' => Str::slug($file->document->title) . ".pdf",
                'signers' => $file->document->signers,
                'signatures' => $file->document->signatures,
                'uploadedDocument' => null,
            ];
        }

        foreach ($attachments as $file) {
            $batch->attachments[] = (object)[
                'name' => $file->document->title,
                'filepath' => $file->getFullPath(),
                'outputFilename' => Str::slug($file->document->title) . ".pdf",
                'signers' => $file->document->signers,
                'signatures' => $file->document->signatures,
                'uploadedDocument' => null,
            ];
        }

        return $batch;
    }
}