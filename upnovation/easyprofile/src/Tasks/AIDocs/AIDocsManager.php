<?php namespace Upnovation\Easyprofile\Tasks\AIDocs;

use Exception;
use Illuminate\Http\UploadedFile;
use Upnovation\DocumentReader\Providers\Readers\AzureIdReader;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Toast;
use App\Models\Person;
use App\Models\Address;
use App\Models\File;
use App\Models\Pipeline;
use App\Models\Zip; // Model per gi_comuni_cap

class AIDocsManager extends AbstractTask
{
    protected AzureIdReader $azure;

    public function __construct(DocumentsManager $documentsManager, FileManager $fileManager)
    {
        parent::__construct($documentsManager, $fileManager);

        $this->azure = new AzureIdReader(
            env('AZURE_OPENAI_ENDPOINT'), 
            env('AZURE_OPENAI_APIKEY1')
        );
    }

    public function read(Pipeline $pipeline, UploadedFile $file)
    {
        $result = $this->azure->analyzeFile(
            $file->getRealPath(),
        );

        if (! $result) {
            throw new Exception("Azure failed to read the document.");
        }

        if (! isset($result['status']) || $result['status'] !== 'succeeded') {
            throw new Exception("Azure analyze failed.");
        }

        if (! isset($result['analyzeResult']) || ! is_array($result['analyzeResult'])) {
            throw new Exception("Azure analyze returned invalid data.");
        }

        if (! isset($result['analyzeResult']['documents']) || ! is_array($result['analyzeResult']['documents'])) {
            throw new Exception("Azure analyze returned no documents.");
        }

        if (empty($result['analyzeResult']['documents'])) {
            throw new Exception("Azure analyze returned empty documents.");
        }

        $result = $this->parseResult($result['analyzeResult']['documents']);

        $file = $this->files->store(
            app('currentTenant')->name,
            new File([
                'disk' => 'documents',
                'path' => 'id',
                'filename' => "{$pipeline->id}-temp-0-" . $file->getClientOriginalName(),
                'type' => 'identity_document',
            ]), 
            file_get_contents($file->getRealPath())
        );

        $result['file'] = $file;

        return $result;
    }

    public function parseResult(array $result)
    {
        // @FIXME: handle multiple documents, how does it work!?
        if (! $document = $result[0]) {
            throw new Exception("No document found in the result.");
        }

        $docType = $this->parseDocType($document);

        return [
            'docType' => $docType,
            'fields' => $this->mapAIDocsFieldsToPersonAndAddress($docType, $document['fields']),
            'confidence' => $document['confidence'] ?? 0,
        ];
    }

    public function parseDocType(array $document)
    {
        if( ! isset($document['docType']) || ! is_string($document['docType'])) {
            throw new Exception("Document type not found in the result.");
        }

        switch($document['docType']) {
            case 'idDocument.nationalIdentityCard':
                return 'id';
            case 'idDocument.passport':
                return 'passport';
            default:
                throw new Exception("Only ID type implemented but {$document['docType']} found.");
        }
    }

    public function parseFields(array $fields)
    {
        $result = [];

        foreach ($fields as $key => $field) {
            switch ($field['type']) {
                case 'string':
                    $result[$key] = $field['valueString'] ?? '';
                    break;
                case 'date':
                    $result[$key] = $field['valueDate'] ?? '';
                    break;
                case 'address':
                    // Mappa l'indirizzo come array o come stringa formattata
                    $result[$key] = [
                        'street' => $field['valueAddress']['road'] ?? '',
                        'number' => $field['valueAddress']['houseNumber'] ?? '',
                    ];
                    break;
                default:
                    throw new Exception("Unsupported field type: {$field['type']} for key: {$key}");
            }
        }

        return $result;
    }

    protected function getFieldValue($fields, $key, $default = null)
    {
        if (!isset($fields[$key])) return $default;
        $field = $fields[$key];
        switch ($field['type'] ?? null) {
            case 'string':
                return $field['valueString'] ?? $default;
            case 'date':
                return $field['valueDate'] ?? $default;
            case 'address':
                return $field['valueAddress'] ?? $default;
            default:
                return $default;
        }
    }

    public function mapAIDocsFieldsToPersonAndAddress(string $docType, array $fields)
    {
        // --- PERSON ---
        $firstName = $this->getFieldValue($fields, 'FirstName');
        $lastName = $this->getFieldValue($fields, 'LastName');
        $taxCode = $this->getFieldValue($fields, 'TaxCode');
        $birthdate = $this->getFieldValue($fields, 'DateOfBirth');
        $sex = $this->getFieldValue($fields, 'Sex');
        $birthplace = $this->getFieldValue($fields, 'PlaceOfBirth');

        $birthplaceCity = $birthplace;
        $birthplaceProvince = null;
        if ($birthplace && preg_match('/^(.*)\s+\((.*)\)$/', $birthplace, $matches)) {
            $birthplaceCity = trim($matches[1]);
            $birthplaceProvince = trim($matches[2]);
        }

        // Normalizza birthdate in formato yyyy-mm-dd
        if ($birthdate) {
            try {
                $birthdate = (new \DateTime($birthdate))->format('Y-m-d');
            } catch (\Exception $e) {
                $birthdate = null;
            }
        }

        // --- DOCUMENT FIELDS ---
        $documentNumber        = $this->getFieldValue($fields, 'DocumentNumber');
        $documentDiscriminator = $this->getFieldValue($fields, 'DocumentDiscriminator');
        $dateOfExpiration      = $this->getFieldValue($fields, 'DateOfExpiration');
        $dateOfIssue           = $this->getFieldValue($fields, 'DateOfIssue');

        // Normalizza date documento
        if ($dateOfExpiration) {
            try {
                $dateOfExpiration = (new \DateTime($dateOfExpiration))->format('Y-m-d');
            } catch (\Exception $e) {
                $dateOfExpiration = null;
            }
        }
        if ($dateOfIssue) {
            try {
                $dateOfIssue = (new \DateTime($dateOfIssue))->format('Y-m-d');
            } catch (\Exception $e) {
                $dateOfIssue = null;
            }
        }

        // --- ADDRESS ---
        $addressValue = $this->getFieldValue($fields, 'Address');
        $addressZip = null;
        $addressCity = $birthplaceCity; // fallback
        $addressProvince = null;

        if ($addressValue && isset($addressValue['road'])) {
            $addressZipObj = Zip::where('denominazione_ita', $addressCity)
                ->orWhere('denominazione_ita_altra', $addressCity)
                ->first();
            if ($addressZipObj) {
                $addressZip = $addressZipObj->cap;
                $addressProvince = $addressZipObj->sigla_provincia;
            }
        }

        return [
            'name'                => ucfirst($firstName),
            'lastname'            => ucfirst($lastName),
            'taxCode'             => strtoupper($taxCode),
            'birthdate'           => $birthdate,
            'birthplaceCountry'   => 'IT',
            'birthplaceCity'      => $birthplaceCity,
            'birthplaceProvince'  => $birthplaceProvince,
            'sex'                 => $sex,
            'email'               => null,
            'pec'                 => null,
            'phone'               => null,
            'iban'                => null,
            'sdi'                 => null,
            'privacy'             => false,
            'privacy_accepted_at' => null,
            'documents'           => [
                $docType => [
                    'type'        => $docType,
                    'files'       => [], // Popola qui se hai info file, altrimenti lascia array vuoto
                    'expiry'      => $dateOfExpiration,
                    'issuer'      => null, // valorizza se hai campo comune rilascio
                    'number'      => $documentNumber,
                    'issuerDate'  => $dateOfIssue,
                    'issuerCountry' => 'Italia',
                    'discriminator' => $documentDiscriminator,
                ],
            ],
            'address' => $addressValue ? [
                'type'    => 'residence',
                'street'  => $addressValue['road'] ?? null,
                'number'  => $addressValue['houseNumber'] ?? null,
                'zip'     => $addressZip,
                'country' => 'IT',
            ] : null,
        ];
    }
}