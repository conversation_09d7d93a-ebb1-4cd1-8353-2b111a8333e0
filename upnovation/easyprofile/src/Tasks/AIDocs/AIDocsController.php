<?php namespace Upnovation\Easyprofile\Tasks\AIDocs;

use App\Http\Controllers\Controller;
use App\Models\Pipeline;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Upnovation\Easyprofile\Toast;

class <PERSON>DocsController extends Controller
{
    protected AIDocsManager $manager;

    public function __construct(AIDocsManager $manager)
    {
        $this->manager = $manager;
    }

    public function getIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        /*return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'data' => Session::get('aidocs_data', []),
        ]);*/


        
        $file = $request->file('file');

        if (! $file) {
            return redirect()->back()->withErrors(Toast::error('error.fileUpload', ['task' => $task]));
        }

        try {
            $data = $this->manager->read($pipeline, $file);

            Session::put('aidocs_data', $data);
        } catch (\Exception $e) {
            return redirect()->back()->withErrors(Toast::error('error.azure', ['task' => $task]));
        }

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'data' => $data,
        ]);
    }
}