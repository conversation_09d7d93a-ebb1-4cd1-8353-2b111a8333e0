<?php namespace Upnovation\Easyprofile\Tasks;

use App\Models\Task;
use Illuminate\Support\Facades\Log;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;

abstract class AbstractTask implements TaskInterface
{
    protected DocumentsManager $documents;

    protected FileManager $files;

    public function __construct(DocumentsManager $documents, FileManager $files)
    {
        $this->documents = $documents;

        $this->files = $files;
    }

    public function initialize(Task $task, ?Task $previousTask = null): Task
    {
        $this->files->deleteTaskSignableFiles($task);

        return $task;
    }

    public function finalize(Task $task): Task
    {
        $this->handleConfig($task);

        return $task;
    }

    public function isAccessible(Task $thisTask, Task $currentTask): bool
    {
        // Task has no blacklist, so it's accessible (according to pipeline logic).
        if (! isset($thisTask->config['blacklist'])) {
            return true;
        }

        // Check if the current task type is not in the blacklist of the task.
        return ! in_array($currentTask->type, $thisTask->config['blacklist']);
    }

    public function skip(Task $t1, Task $t2, $data = null)
    {
        Log::debug("Executing skip from {$t1->type} to {$t2->type}.");

        return $data;
    }

    public function handleConfig(Task $task) : Task
    {
        if (isset($task->config['compile'])) {
            $this->compileDocuments($task);
        }

        return $task;
    }

    public function compileDocuments(Task $task)
    {
        if (! isset($task->config['compile'])) {
            return null;
        }
        
        $documents = $this->documents->getDocumentsByType(
            $task->config['compile']
        );

        // @todo return what???
        $this->documents->prepareDocuments(
            $task, 
            $documents
        );
    }
}