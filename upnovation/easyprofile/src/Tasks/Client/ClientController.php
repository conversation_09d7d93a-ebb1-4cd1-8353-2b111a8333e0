<?php namespace Upnovation\Easyprofile\Tasks\Client;

use App\Http\Controllers\Controller;
use App\Models\Address;
use App\Models\Client;
use App\Models\Coverage;
use App\Models\Enterprise;
use App\Models\Form;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Profile;
use App\Models\Rules\UniqueInTenant;
use App\Models\Rules\UniquePhoneInTenant;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Spatie\Multitenancy\Models\Tenant;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;
use Upnovation\Easyprofile\Traits\CanSwitchTheme;


class ClientController extends Controller
{
    /** @var ClientManager */
    protected $manager;

    protected SurveyManager $surveyManager;

    public function __construct(ClientManager $manager, SurveyManager $surveyManager)
    {
        $this->manager = $manager;
        
        $this->surveyManager = $surveyManager;
    }

    public function getIndex(Pipeline $pipeline, Task $task)
    {
        if ($client = $pipeline->getContractor()) {
            if ($client->person) {
                $client->person->load('addresses');
                $client->person->load('addresses.zipRecord');
            }

            if ($client->enterprise) {
                $client->enterprise->load('addresses');
                $client->enterprise->load('addresses.zipRecord');
                $client->enterprise->load('rep');
            }

            $client->preferences = $task->data['preferences'] ?? [];
        }

        /*
        $data = Session::get('aidocs_data');

        if ($data && ! empty($data['fields'])) {
            if (! $client) {
                $client = new Client();
                $client->person = new Person();
            }

            $client->person->forceFill($data['fields']);

            Session::forget('aidocs_data');
        }
        */

        return Inertia::render($task->template, [
            'pipeline' => $pipeline->load('tasks'),
            'task' => $task,
            'client' => $client,
        ]);
    }

    public function postIndex(Request $request, Pipeline $pipeline, Task $task)
    {

        $rules = $this->getValidationRules($request, $request->all());

        try {

            $request->validate($rules);
            
        } catch (ValidationException $ex) {
            Log::debug($ex->getMessage());
            
            return redirect()->back()->withErrors($ex->errors());
        }

        if ($pipeline->getContractor()) {
            $client = $this->manager->update(
                $task, 
                $request->all(), 
                $request->file('person.documents')
            );
        } else {
            $client = $this->manager->create(
                $task, 
                $request->all(), 
                $request->file('person.documents')
            );
        }

        $task->addData('preferences', $request->get('person')['preferences'] ?? false);
        $task->save();

        return redirect()->route("pipeline.next", ['pipeline' => $pipeline->id, 'task' => $task->id]);
    }

    protected function getValidationRulesForTenant($tenant)
    {
        $rules = config('easyprofile.rules.default.client');

        if ($tenantOverrides = config("easyprofile.tenants.{$tenant}.tasks.client.rules")) {
            return array_merge($rules, $tenantOverrides);
        }

        return $rules;
    }

    protected function getValidationRules(Request $request, array $data)
    {
        // Read rules from config.
        $rules = $this->getValidationRulesForTenant(app('currentTenant')->name);

        // Get person for dynamic rules.
        $person = new Person();
        $person->id = $data['person']['id'] ?? null;

        // Merge static and dynamic rules.
        $personRules = array_merge($rules['person'], [
            'person.email' => [
                'required', 
                'email', 
                'max:255', 
                with(new UniqueInTenant($person, 'email'))->ignore($data['person']['id'] ?? null)
            ],
            'person.taxCode' => [
                'required', 
                'string', 
                'max:16', 
                with(new UniqueInTenant($person, 'taxCode'))->ignore($data['person']['id'] ?? null)
            ],
            'person.phone' => [
                'required', 
                'digits_between:9,16', 
                with(new UniquePhoneInTenant($person, 'phone', $data['person']))->ignore($data['person']['id'] ?? null)
            ],
        ]);

        // Add identity document rules.
        // Document is not mandatory if person already exists.
        if (! $person->id) {
            $personRules = array_merge($personRules, $rules['identityDocument']);
        }

        // Not corporate: return person rules.
        if (! $request->get('isCorporate')) {
            return $personRules;
        }

        // Same for enterprise.
        $enterprise = new Enterprise();
        $enterprise->id = $data['enterprise']['id'] ?? null;
        $enterpriseRules = array_merge($rules['enterprise'], [
            'enterprise.name' => [
                'required', 
                'string', 
                'max:255', 
                with(new UniqueInTenant($enterprise, 'name'))->ignore($data['enterprise']['id'] ?? null)
            ],
            'enterprise.vat' => [
                'required', 
                'string', 
                'max:16', 
                with(new UniqueInTenant($enterprise, 'vat'))->ignore($data['enterprise']['id'] ?? null)
            ],
        ]);

        // Corporate: there should be a person.
        if (! $person = $request->get('person')) {
            Log::error("Person data is empty.");

            abort(400);
        }
        
        // Person has id (user selected an existing one)
        // or Enterprise already exists (hence has a valid rep)
        // => we only need to validate enterprise.
        if (! empty($person['id']) || $enterprise->id) {
            return $enterpriseRules;
        } 

        // Validate everything, enterprise and person are both new.
        return array_merge($personRules, $enterpriseRules);
    }

    public function putIndex(Request $request, Pipeline $pipeline, Task $task)
    {
        $entity = $request->get('entity');

        if (! $entity['id']) {
            Log::error("Entity model ID is empty.");

            abort(400);
        }

        if (! $entity['type']) {
            Log::error("Entity type is empty.");

            abort(400);
        }

        $client = null;

        if ($entity['type'] == 'individual') {

            $client = Person::find($entity['id']);

        } elseif ($entity['type'] == 'legal') {

            $client = Enterprise::find($entity['id']);
            
        } else {
            Log::error("Entity is not individual nor enterprise");

            return response()->json(['x' => __('easyprofile.error')], 400);
        }

        if (! $client) {
            Log::error("Client model not found.");

            return response()->json(['x' => __('easyprofile.error')], 400);
        }

        $this->manager->bind($pipeline, $client, 'contractor');

        return redirect()->route("pipeline.next", [
            'pipeline' => $pipeline->id,
            'task' => $task->id,
        ]);
    }

    public function getSearch(Request $request, $query)
    {
        //$query = $request->get('query');

        $persons = Person::where('name', 'like', "%$query%")
            ->orWhere('lastname', 'like', "%$query%")
            ->orWhere('taxCode', 'like', "%$query%")
            ->with('addresses')
            ->get()
            ->map(function ($person) {
                $person->setRelation('addresses', $person->addresses->keyBy('type'));

                return [
                    'id' => $person->id,
                    'name' => (string)$person,
                    'code' => $person->taxCode,
                    'type' => 'individual',
                    'model' => $person,
                    'profile' => $this->surveyManager->getProfileByClient($person, config('easyprofile.clientProfileExpirationInDays')),
                ];
            });

        if ($request->get('type') == 'individual') {
            return $persons;
        }

        $enterprises = Enterprise::where('name', 'like', "%$query%")
            ->orWhere('vat', 'like', "%$query%")
            ->get()
            ->map(function ($enterprise) {
                return [
                    'id' => $enterprise->id,
                    'name' => $enterprise->name,
                    'code' => $enterprise->vat,
                    'type' => 'legal',
                    'model' => $enterprise,
                    'profile' => $this->surveyManager->getProfileByClient($enterprise, config('easyprofile.clientProfileExpirationInDays')),
                ];
            });

        if ($request->get('type') == 'legal') {
            return $enterprises;
        }

        return response()->json($persons->concat($enterprises));
    }

    public function postUpload(Request $request)
    {
        // See route definition in routes/web.php for comment.
        abort(501, 'Not implemented yet.');

        $rules = $this->getValidationRulesForTenant(app('currentTenant')->name)['identityDocument'];

        try {

            //$request->validate($rules);
            
        } catch (ValidationException $ex) {
            Log::debug($ex->getMessage());
            
            return redirect()->back()->withErrors($ex->errors());
        }

        return redirect()->back();// maybe a 200 OK is enough. ->with('foo', 'bar');
    }
}
