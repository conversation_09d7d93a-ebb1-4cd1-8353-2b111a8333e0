<?php namespace Upnovation\Easyprofile\Tasks\Client;

use App\Models\Address;
use App\Models\Client;
use App\Models\Enterprise;
use App\Models\File;
use App\Models\Interfaces\Clientable;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Task;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Upnovation\Easyprofile\FileManager;
use Upnovation\Easyprofile\Modules\Documents\DocumentsManager;
use Upnovation\Easyprofile\Tasks\AbstractTask;
use Upnovation\Easyprofile\Tasks\Survey\SurveyManager;
use Upnovation\Easyprofile\Tasks\TaskInterface;

class ClientManager extends AbstractTask
{
    protected SurveyManager $surveyManager;

    public function __construct(DocumentsManager $documentsManager, FileManager $fileManager, SurveyManager $surveyManager)
    {
        parent::__construct($documentsManager, $fileManager);

        $this->surveyManager = $surveyManager;
    }

    public function skip(Task $currentTask, Task $targetTask, $data = null)
    {
        parent::skip($currentTask, $targetTask, $data);

        if (empty($data['entity'])) {
            throw new Exception("No data provided for skip operation {$currentTask->type} -> {$targetTask->type}.");
        }

        if ($data['entity']['type'] == 'individual') {

            $clientable = Person::find($data['entity']['id']);

        } elseif ($data['entity']['type'] == 'legal') {

            $clientable = Enterprise::find($data['entity']['id']);

        } else {
            throw new Exception("Entity is not individual nor enterprise");
        }

        if (! $this->surveyManager->getProfileByClient($clientable, config('easyprofile.clientProfileExpirationInDays'))) {
            throw new Exception("No profile found for client.");
        }

        //return $this->bind($currentTask->pipeline, $clientable, 'contractor');

        return $clientable;
    }


    public function bindClientWithPipeline(Pipeline $pipeline, Clientable $entity, string $role): Client | null
    {
        $client = new Client();
        $client->pipeline_id = $pipeline->id;
        $client->name = (string)$entity;
        $client->role = $role;

        if ($entity->getType() == 'individual') {
            $client->person_id = $entity->id;
        } elseif ($entity->getType() == 'legal') {
            $client->enterprise_id = $entity->id;
        } else {
            Log::error("Unknown entity type.");
        }

        $pipeline->type = $entity->getType();
        $pipeline->save();

        $client->save();

        return $client;
    }

    public function update(Task $task, array $data, ?array $documents = [])
    {
        DB::connection('tenant')->beginTransaction();

        if ($task->pipeline->type == 'legal') {
            $clientable = $this->saveEnterprise($task->pipeline, $data['enterprise'], $data['person']);
            $person = $clientable->rep;
        } else {
            $clientable = $this->savePerson($data['person']);
            $person = $clientable;
        }

        if ($documents) {
            $this->saveDocuments($person, $data['person'], $documents);
        }

        DB::connection('tenant')->commit();

        return $task->pipeline->getContractor() ;
    }

    public function create(Task $task, array $data, ?array $documents = []) : Client
    {
        DB::connection('tenant')->beginTransaction();

        $task->pipeline->type = $data['isCorporate'] ? 'legal' : 'individual';

        $task->pipeline->save();

        if ($task->pipeline->type == 'legal') {
            $clientable = $this->saveEnterprise($task->pipeline, $data['enterprise'], $data['person']);
            $person = $clientable->rep;
            if ($documents) {
                $this->saveDocuments($person, $data['person'], $documents);
            }
        } else {
            $clientable = $this->savePerson($data['person']);
            $person = $clientable;
            $this->saveDocuments($person, $data['person'], $documents);
        }

        $client = $this->bind($task->pipeline, $clientable, 'contractor');

        DB::connection('tenant')->commit();

        return $client;
    }

    public function bind(Pipeline $pipeline, Clientable $entity, string $role): Client | null
    {
        DB::connection('tenant')->beginTransaction();

        $client = $this->bindClientWithPipeline($pipeline, $entity, $role);

        DB::connection('tenant')->commit();

        return $client;
    }

    public function saveDocuments(Person $person, array $personData, array $documents): void
    {
        if (empty($documents)) {
            Log::error("No documents provided for saving.");
            return;
        }

        foreach ($documents as $type => $document) {
            if (! isset($document['files']) || empty($document['files'])) {
                Log::error("No files provided.");

                continue;
            }

            foreach ($document['files'] as $file) {
                if (! $file instanceof UploadedFile) {
                    throw new Exception("Invalid file type provided.");
                }

                $storedFile = $this->files->store(
                    app('currentTenant')->name,
                    new File([
                        'disk' => 'documents',
                        'path' => 'id',
                        'filename' => "ID-" . date('Ymd-His') . "-{$person->id}-" . $file->getClientOriginalName(),
                        'type' => 'identity_document',
                    ]),
                    $file->get()
                );
            }

            // Remove so we don't get empty arrays in person.documents
            unset($personData['documents'][$type]['files']);

            // Save document meta.
            $person->documents = $personData['documents'];
            $person->addDocument($type, $storedFile);
            $person->save();
        }
    }

    public function savePerson(array $personData): Clientable | null
    {
        $person = $personData['id'] ? Person::find($personData['id']) : new Person();

        $person->name = ucfirst($personData['name']);
        $person->lastname = ucfirst($personData['lastname']);

        $person->birthplaceCountry = $personData['birthplace']['country'];
        $person->birthplaceCity = $personData['birthplace']['city'];    
        $person->birthplaceProvince = $personData['birthplace']['province'];
        unset($personData['birthplace']);

        $person->safeForceFill($personData);

        $person->phone = $personData['phonePrefix'] . $personData['phone'];
        $person->save();

        foreach ($personData['addresses'] as $current) {
            $address = new Address();
            $address->safeForceFill($current);
            $address->person()->associate($person);
            $address->save();
        }
       
        return $person;
    }

    public function saveEnterprise(Pipeline $pipeline, array $enterpriseData, array $repData): Clientable
    {
        $enterprise = $enterpriseData['id'] ? Enterprise::find($enterpriseData['id']) : new Enterprise();

        // This is the rule: user cannot change rep after enterprise has been created.
        if (! $enterprise->rep) {
            if (empty($repData['id'])) {    
                $person = $this->savePerson($repData);
            } 
            
            elseif (! $person = Person::find($repData['id']))  {
                throw new \Exception("Enterprise representative not found.");
            }

            $enterprise->rep_id = $person->id;
        }
        
        $enterprise->safeForceFill($enterpriseData);
        $enterprise->save();

        // Manually add since it might be unavailable during the transaction.
        $enterprise->rep = $person ?? $enterprise->rep;

        foreach ($enterpriseData['addresses'] as $current) {
            $address = new Address();
            $address->safeForceFill($current);
            $address->enterprise()->associate($enterprise);
            $address->save();
        }

        return $enterprise;
    }
}