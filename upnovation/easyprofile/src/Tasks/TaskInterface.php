<?php namespace Upnovation\Easyprofile\Tasks;

use App\Models\Task;

interface TaskInterface
{

    public function initialize(Task $task, ?Task $previousTask = null) : Task;

    public function finalize(Task $task) : Task;

    public function handleConfig(Task $task) : Task;

    /**
     * This check takes place when the user is attempting to access thisTask while
     * the pipeline is currently on currentTask.
     * 
     * @param Task $thisTask is the task handled by the manager implementing this interface.
     * 
     * @param Task $currentTask The task active in the pipeline.
     */
    public function isAccessible(Task $thisTask, Task $currentTask) : bool;

    public function skip(Task $t1, Task $t2, $data = null);

}