<?php namespace Upnovation\Easyprofile;

use App\Models\NetworkNode;
use App\Models\User;
use Illuminate\Support\Facades\Config;
use Upnovation\Easyprofile\Exceptions\QuotaException;

class UserManager
{
    protected $tenant;

    public function on($tenant)
    {
        $this->tenant = $tenant;

        return $this;
    }

    public function create(User $user, string $role, NetworkNode $node = null)
    {
        if ($this->countUsers() >= (int)Config::get("easyprofile.tenants.{$this->tenant}.maxUsers")) {
            throw new QuotaException("Cannot create anymore users.");
        }

        if ($node) {
            $user->node_id = $node->id;
        }

        $user->save();
        $user->addRole($role);

        return $user;
    }

    public function load($role)
    {
        return User::whereHas('roles', function($query) use($role){
            $query->whereName($role);
        })->get();
    }

    public function bulkDisable($role)
    {
        return User::whereHas('roles', function($query) use($role){
            $query->whereName($role);
        })->update(['active' => 0]);
    }

    public function countUsers()
    {
        return User::whereHas('roles', function($query) {
            $query->whereName('salesman');
        })->count();
    }
}