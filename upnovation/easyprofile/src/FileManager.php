<?php namespace Upnovation\Easyprofile;

use App\Models\Client;
use App\Models\File;
use App\Models\Person;
use App\Models\Pipeline;
use App\Models\Task;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FileManager
{
    /**
     * Save to database and file system.
     */
    public function save(string $tenant, File $file, $data) : File
    {
        if (! $tenant) {
            throw new \Exception("No tenant set for file saving.");
        }

        $this->store($tenant, $file, $data);

        $file->save();

        return $file;
    }

    public function store(string $tenant, File $file, $data)
    {
        $file = $this->applyPath($file);

        if (! $result = Storage::disk($file->disk)->put("{$tenant}/{$file->path}/{$file->filename}", $data)) {
            throw new Exception("Error saving file to disk: {$file->disk}/{$tenant}/{$file->path}/{$file->filename}");
        }

        return $file;
    }

    public function applyPath(File $file) : File
    {
        $file->path = $file->path . "/" . Carbon::now()->format('Y-m');

        return $file;
    }

    public function delete(File $file) 
    {
        DB::connection('tenant')->beginTransaction();
        
        $file->delete();

        if (! Storage::disk($file->disk)->delete($file->getFullPath())) {
            throw new Exception("Error deleting file: {$file->getFullPath()}");
        }
        
        DB::connection('tenant')->commit();
    }

    public function deleteTaskSignableFiles(Task $task)
    {
        // Delete all files associated with the task
        $files = File::where('task_id', $task->id)->where('type', 'signable');

        $files = $files->get();

        foreach ($files as $file) {
            try {
                $this->delete($file);
            } catch (Exception $e) {
                Log::error("Error deleting file: {$file->getFullPath()} - " . $e->getMessage());
            }
        }
    }

    public function deletePipelineSignableFiles(Pipeline $pipeline)
    {
        // Delete all files associated with the pipeline
        $files = File::whereIn('task_id', function($query) use ($pipeline) {
            $query->select('id')
                ->from('tasks')
                ->where('pipeline_id', $pipeline->id);
        })->where('type', 'signable');

        $files = $files->get();

        foreach ($files as $file) {
            try {
                $this->delete($file);
            } catch (Exception $e) {
                Log::error("Error deleting file: {$file->getFullPath()} - " . $e->getMessage());
            }
        }
    }

    public function loadPipelineFiles(Pipeline $pipeline, array $attrs = [], array|null $documentTypes = []) : Collection
    {
        // Recupera tutti i file associati a task che appartengono alla stessa pipeline
        // e dove i documenti associati soddisfano gli attributi specificati
        $files = File::whereIn('task_id', function($query) use ($pipeline) {
            $query->select('id')
                ->from('tasks')
                ->where('pipeline_id', $pipeline->id);
        });

        // Applica eventuali filtri aggiuntivi
        foreach ($attrs as $key => $value) {
            $files->where($key, $value);
        }

        if (! empty($documentTypes)) {
            $files->whereHas('document', function($q) use ($documentTypes) {
                $q->whereIn('type', $documentTypes);
            });
        }

        return $files->with('document')->get();
    }   

    public function loadClientFiles(Client $client)
    {
        if ($client->person) {
            return $this->loadPersonFiles($client->person);
        }

        throw new \Exception("Enterprise not implemented.");
    }

    public function loadPersonFiles(Person $person)
    {
        // 1. Trova tutti i client della persona
        $pipelineIds = Client::where('person_id', $person->id)->pluck('pipeline_id');

        // 2. Trova tutti i task di questi pipeline
        $taskIds = Task::whereIn('pipeline_id', $pipelineIds)->pluck('id');

        // 3. Trova tutti i file di questi task
        $files = File
            ::whereIn('task_id', $taskIds)
            ->where('type', '!=', 'template') 
            ->with('document')
            ->with('task')
            ->with(['task.pipeline'])
            ->get();

            // 4. Raggruppa per pipeline_id
        $grouped = $files->groupBy(function($file) {
            return $file->task->pipeline_id;
        })
        // 5. Ordina i gruppi per pipeline.created_at desc
        ->sortByDesc(function($files, $pipelineId) {
            return optional($files->first()->task->pipeline)->created_at;
        })
        // 6. Trasforma in array di gruppi (senza chiave pipeline_id)
        ->values();

        return $grouped;
    }

}
